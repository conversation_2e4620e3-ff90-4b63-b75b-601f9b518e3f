<template>
    <div>
        <transition name="guangdong">
            <!-- 广东 -->
            <div id="guangdong"
                style="position: absolute; top:520px;pointer-events: none; width: 550px; height: 280px; background-color: rgba(18, 137, 221, 0.1); background-size: cover; background-position: center center; overflow: hidden; filter: unset;left:25px">
                <div class="wrapper">
                    <div v-if="Data.name" class="tipTitle">
                        <div class="tipArrow"></div>
                        <div class="tipArrow"></div>
                        <div class="tipArrow"></div>
                        <span class="name">{{Data.name}}</span>
                    </div>
                    <div v-if="Data.province">
                        <div class="tipsIcon1"></div>
                        <span class="tipName">所在省份：</span>
                        <span class="tipContent">{{Data.province}}</span>
                    </div>
                    <div v-if="Data.computeScale">
                        <div class="tipsIcon2"></div>
                        <span class="tipName">算力规模：</span>
                        <span class="tipContent2">{{Data.computeScale}}</span><span class="tipContent">POps@FP16</span>
                    </div>
                    <div v-if="Data.accessTime">
                        <div class="tipsIcon3"></div>
                        <span class="tipName">接入时间：</span>
                        <span class="tipContent">{{ parseTime(Data.accessTime)}}</span>
                    </div>
                    <div v-if="Data.cardRunTime">
                        <div class="tipsIcon3"></div>
                        <span class="tipName">提供卡时数：</span>
                        <span class="tipContent2">{{ Data.cardRunTime}}</span><span class="tipContent">卡时</span>
                    </div>
                    <div v-if="Data.jobCount">
                        <div class="tipsIcon4"></div>
                        <span class="tipName">承接任务数：</span>
                        <span class="tipContent2">{{  Data.jobCount}}</span><span class="tipContent">个</span>
                    </div>
                </div>

            </div>
        </transition>

    </div>
</template>
<script>
import { formatDuring, parseTime } from '@/utils/index'
export default {
  props: {
    Data: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      // data: {}
    }
  },
  mounted () {

  },
  watch: {
    Data: {
      handler (newValue, oldValue) {
        this.Data = newValue
      },

      deep: true
    }
  },
  methods: {
    parseTime (val) {
      return parseTime(val)
    }
  }
}
</script>
<style scoped>
    .img {
        width: 20px;
        height: 20px;
    }

    .beijing-enter-active {
        animation: beijing-in .2s;
    }

    .beijing-leave-active {
        animation: beijing-in .2s;
    }

    .guangdong-enter-active {
        animation: guangdong-in .2s;
    }

    .guangdong-leave-active {
        animation: guangdong-in .2s;
    }

    .anhui-enter-active {
        animation: anhui-in .2s;
    }

    .anhui-leave-active {
        animation: anhui-in .2s;
    }

    .hubei-enter-active {
        animation: hubei-in .2s;
    }

    .hubei-leave-active {
        animation: hubei-in .2s;
    }

    /* 小圆点 */

    .dot {
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 100%;
        /* background: rgb(0, 255, 31); */
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .2);

    }

    .start {
        position: absolute;
        top: 215px;
        left: 320px;
        background: #FCCA00;
    }

    .end {
        position: absolute;
        top: 120px;
        left: 250px;
        background: #FCCA00;
    }

    img {
        width: 18px !important;
        height: 25px !important;
    }

    .startCity {
        font-size: 18px;
        position: absolute;
        top: 115px;
        left: 285px;
        color: white;
        font-family: SourceHanSansSC;
        font-weight: 400;
        font-style: normal;
    }

    .endCity {
        font-size: 18px;
        position: absolute;
        top: 210px;
        left: 335px;
        color: #FCCA00;
        font-family: SourceHanSansSC;
        font-style: normal;
    }

    .linetext {
        font-size: 14px;
        position: absolute;
        top: 158px;
        left: 320px;
        color: #FCCA00;
        font-family: SourceHanSansSC;
        font-weight: 400;
        font-style: normal;
    }

    .wrapper {
        padding-top: 22px;
        padding-left: 30px;
    }

    .wrapper div {
        margin-top: 5px;
    }

    .tipContent {
        margin-top: 10px;
        color: #fff;
    }
    .tipContent2 {
        margin-top: 10px;
        color: rgb(30, 231, 231);
        margin-right: 2px;
    }
    .tipName {
        color: rgba(206, 206, 206, 0.96);
        font-size: 16px;

        font-family: SourceHanSansSC-regular;
    }

    .tipTitle {
        margin-bottom: 20px;
    }

    .tipsIcon1 {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url('../assets/icon/riLine-map-pin-line Copy.svg');
        background-repeat: no-repeat;
        margin-right: 7px;
        background-position-y: 2px;
        background-size: 100% 90%;
    }

    .tipsIcon2 {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url('../assets/icon/riLine-camera-lens-line Copy.svg');
        background-repeat: no-repeat;
        margin-right: 7px;
        background-position-y: 2px;
        background-size: 100% 90%;
    }

    .tipsIcon4 {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url('../assets/icon/riLine-stack-line Copy.svg');
        background-repeat: no-repeat;
        margin-right: 7px;
        background-position-y: 2px;
        background-size: 100% 90%;
    }
    .tipsIcon3 {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url('../assets/icon/riLine-timer-flash-line Copy.svg');
        background-repeat: no-repeat;
        margin-right: 7px;
        background-position-y: 2px;
        background-size: 100% 90%;
    }
    .tipsIcon5 {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-image: url('../assets/icon/riLine-stack-line Copy.svg');
        background-repeat: no-repeat;
        margin-right: 7px;
        background-position-y: 2px;
        background-size: 100% 90%;
    }
    .tipArrow {
        background: url(../assets/arrow3.png);
        width: 12px;
        height: 20px;
        background-repeat: no-repeat;
        background-size: 150% 120%;
        display: inline-block;
        margin-top: 0px!important;
    }
    .name{margin-left:5px;}
</style>
