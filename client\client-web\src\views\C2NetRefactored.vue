<template>
  <div>
    <div class="background">
      <el-container>
        <el-header>
          <el-row justify="center" type="flex">
            <el-col :span="12">
              <div class="title">中国算力网实验场（仿真）</div>
            </el-col>
          </el-row>
        </el-header>

        <el-main>
          <el-row justify="space-around" type="flex">
            <!-- 左侧：任务总览和任务列表 -->
            <el-col :span="7">
              <!-- 任务总览组件 -->
              <task-overview
                :task-msg="msg2"
                :chart-data="Data"
              ></task-overview>

              <!-- 任务列表组件 -->
              <task-list
                ref="taskList"
                :count="count"
                :total="total"
                :stopped="Stop"
                @add-task="openAddTaskDialog"
                @row-click="openTaskDetailDialog"
              ></task-list>
            </el-col>

            <!-- 中间：地图和信息总览 -->
            <el-col :span="14">
              <div>
                <el-row justify="center" type="flex">
                  <el-col :span="18">
                    <Total :msg="msg" :taskDetail="taskDetail"></Total>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <!-- 地图容器组件 -->
                    <map-container
                      ref="mapContainer"
                      :count="count"
                      :total="total"
                      :stopped="Stop"
                      :task-response="taskIdRes"
                      :interval-value="value"
                      :disabled="disable"
                      :is-playing="buttonShow"
                      :show-button="showButtom"
                      :show-percentage="showPer"
                      :interval-change-enabled="intervalChange"
                      :province-data="tipData"
                      :show-province-map2="show2"
                      :show-province-map3="show3"
                      @start="start"
                      @stop="stop"
                      @continue="goOn"
                      @progress-change="handleProgressChange"
                      @interval-change="handleIntervalChange"
                    ></map-container>
                  </el-col>
                </el-row>
              </div>
            </el-col>

            <!-- 右侧：任务统计 -->
            <el-col :span="5">
              <div class="taskDetailWrapper">
                <!-- 任务统计组件 -->
                <task-statistics
                  ref="taskStatistics"
                  :submit-job-data="submitJobCompare0"
                  :pending-job-data="pendingJobCompare0"
                  :resource-usage-data="resouceCompare0"
                  :current-index="count"
                  :task-info="{
                    taskId: taskId,
                    compareId: compareId,
                    strategy1: Strategy1,
                    strategy2: Strategy2
                  }"
                  :disabled="disable"
                ></task-statistics>
              </div>
            </el-col>
          </el-row>
        </el-main>
        <el-row justify="center" type="flex">
          <!-- <div class="coopera-pic"></div> -->
        </el-row>
      </el-container>
      <div class="footer"></div>
    </div>

    <!-- 使用组件化的任务弹窗 -->
    <TaskDialog
      :visible.sync="addTaskDialogVisible"
      @close="handleCloseTaskDialog"
      @submit="submitTaskForm"
    />

    <!-- 任务详情弹窗 -->
    <TaskDetailDialog
      :taskId="currentTaskId"
      :visible.sync="taskDetailDialogVisible"
      @close="handleTaskDetailClose"
      @submit-success="handleTaskSubmitSuccess"
    />
  </div>
</template>

<script>
import Total from '@/components/Total'
import TaskDialog from '@/components/TaskDialog'
import TaskDetailDialog from '@/components/TaskDetailDialog'
import TaskOverview from '@/components/c2net/TaskOverview'
import TaskList from '@/components/c2net/TaskList'
import MapContainer from '@/components/c2net/MapContainer'
import TaskStatistics from '@/components/c2net/TaskStatistics'
import TaskDataService from '@/services/TaskDataService'
import { mapState } from 'vuex'

/**
 * 中国算力网实验场（仿真）主视图组件
 */
export default {
  name: 'C2NetRefactored',
  components: {
    Total,
    TaskDialog,
    TaskDetailDialog,
    TaskOverview,
    TaskList,
    MapContainer,
    TaskStatistics
  },
  data () {
    return {
      // 任务对话框
      addTaskDialogVisible: false,
      taskDetailDialogVisible: false,
      currentTaskId: '',

      // 任务数据
      msg: {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      },
      msg2: {
        totalNum: 0,
        execNum: 0
      },
      Data: {
        xAxis: [],
        yAxis: []
      },
      taskDetail: {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      },
      runningTime: 0,

      // 任务统计数据
      submitJobCompare0: {
        xData: [],
        used: [],
        used2: []
      },
      pendingJobCompare0: {
        xData: [],
        used: [],
        used2: []
      },
      resouceCompare0: {
        xData: [],
        used: [],
        used2: []
      },

      // 仿真控制
      value: 24,
      count: 0,
      nowCount: 0,
      total: 0,
      percentage: 0,
      timer: null,
      temp: null,

      // 控制状态
      disable: false,
      buttonShow: true,
      showButtom: true,
      showPer: true,
      Stop: true,
      showStart: false,
      intervalChange: true,

      // 地图显示
      show1: true,
      show2: false,
      show3: false,
      tipData: {},

      // 任务响应数据
      taskIdRes: null,
      compareIdRes: null,
      dataAlreadyLoaded: false
    }
  },
  computed: {
    // 从Vuex获取状态
    ...mapState({
      taskId: state => state.id,
      compareId: state => state.compareId,
      Strategy1: state => state.strategy1,
      Strategy2: state => state.strategy2,
      lastTime: state => state.lastTime,
      interval: state => state.interval
    })
  },
  watch: {
    taskId (newValue, oldValue) {
      this.resetUI()
      if (newValue !== 0) {
        this.intervalChange = true
      } else {
        this.intervalChange = false
      }
      clearInterval(this.timer)
    },
    compareId (newValue, oldValue) {
      this.resetUI()
      clearInterval(this.timer)
    },
    count (newValue, oldValue) {
      this.updateCountEffect()
    }
  },
  mounted () {
    // 确保组件加载后初始化空图表
    this.$nextTick(() => {
      this.resetChartData()
    })
  },
  methods: {
    // 任务对话框相关方法
    openAddTaskDialog () {
      this.addTaskDialogVisible = true
    },
    handleCloseTaskDialog () {
      this.addTaskDialogVisible = false
    },
    submitTaskForm (responseOrFormData) {
      // submitTaskForm被调用，参数: responseOrFormData

      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自TaskDialog的generateYaml响应
        this.$message.success('任务创建成功')
        this.addTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          // 新增任务成功后刷新任务列表
          if (this.$refs.taskList) {
            // 刷新任务列表
            this.$refs.taskList.initTable()
          } else {
            // taskList引用不存在，无法刷新任务列表
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/tasks', responseOrFormData)
          .then(response => {
            this.$message.success('任务创建成功')
            this.addTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              // 新增任务成功后刷新任务列表
              if (this.$refs.taskList) {
                // 刷新任务列表
                this.$refs.taskList.initTable()
              } else {
                // taskList引用不存在，无法刷新任务列表
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建任务失败: ' + error.message)
          })
      }
    },
    openTaskDetailDialog (row) {
      // 打开任务详情: row
      this.currentTaskId = row.ID
      this.taskDetailDialogVisible = true
    },
    handleTaskDetailClose () {
      this.taskDetailDialogVisible = false
    },
    handleTaskSubmitSuccess () {
      // 任务提交/重新提交成功事件被触发
      // 刷新任务列表
      if (this.$refs.taskList) {
        // 刷新任务列表
        this.$refs.taskList.initTable()
      } else {
        // taskList引用不存在，无法刷新任务列表
      }
    },

    // 重置UI方法
    resetUI () {
      this.resetChartData()
      this.resetTaskInfo()
    },

    // 重置图表数据
    resetChartData () {
      this.Data = {
        xAxis: [],
        yAxis: []
      }

      this.submitJobCompare0 = {
        xData: [],
        used: [],
        used2: []
      }

      this.pendingJobCompare0 = {
        xData: [],
        used: [],
        used2: []
      }

      this.resouceCompare0 = {
        xData: [],
        used: [],
        used2: []
      }

      this.count = 0
      this.buttonShow = true
      this.showButtom = false
    },

    // 重置任务信息
    resetTaskInfo () {
      this.msg = {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      }
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
      this.taskDetail = {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      }
    },

    // 更新计数效果
    updateCountEffect () {
      if (this.count == this.total) {
        this.percentage = 100
      } else if (this.count == 0) {
        this.percentage = 0
      } else {
        this.percentage = Number(((this.count / this.total) * 100).toFixed(0))
      }

      if (this.count == this.total) {
        this.Data.yAxis = this.temp
        clearInterval(this.timer)
        this.percentage = 100
        this.Stop = true
      } else {
        this.temp.forEach((item, key) => {
          if (key <= this.count + 1) {
            this.Data.yAxis[key] = this.temp[key]
          } else {
            this.Data.yAxis[key] = ''
          }
        })
      }
    },

    // 获取任务数据
    async getJobData () {
      return TaskDataService.getJobData(this.taskId, this.compareId, this.interval)
    },

    // 处理任务数据
    processJobData (taskRes, compareRes) {
      this.prepareForProcessing()

      if (!taskRes) return

      // 处理主任务数据
      const baseInfo = TaskDataService.initBaseTaskInfo(taskRes)
      this.msg = baseInfo.msg
      this.taskDetail = baseInfo.taskDetail

      if (
        taskRes.CenterInfoToWebList != null &&
        taskRes.CenterInfoToWebList[0] &&
        taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } = TaskDataService.extractJobData(taskRes.CenterInfoToWebList, this.total)

      const compareData = TaskDataService.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)
      this.submitJobCompare0 = compareData.submitJobCompare0
      this.pendingJobCompare0 = compareData.pendingJobCompare0
      this.resouceCompare0 = compareData.resouceCompare0

      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]

      // 处理对比任务数据
      if (compareRes) {
        this.processCompareData(compareRes)
      }

      this.finalizeProcessing()
      this.startAnimationTimer(Time, historySubmitJob, historyCompleteJob)
    },

    // 准备处理数据
    prepareForProcessing () {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      this.disable = true
      this.showButtom = true
      this.showPer = true
      this.taskIdRes = null
      this.compareIdRes = null
    },

    // 完成数据处理
    finalizeProcessing () {
      this.disable = false
    },

    // 处理对比数据
    processCompareData (compareRes) {
      if (!compareRes) return

      const total2 = compareRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length

      const {
        city,
        data1,
        data2,
        data3
      } = TaskDataService.processCompareSnapshots(compareRes, this.total, total2)

      // 更新对比数据
      this.submitJobCompare0.used2 = data1
      this.pendingJobCompare0.used2 = data2
      this.resouceCompare0.used2 = data3
    },

    // 开始动画计时器
    startAnimationTimer (Time, historySubmitJob, historyCompleteJob) {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count == that.total) {
          clearInterval(that.timer)
          return
        }

        // 更新时间和任务数据
        that.msg.SnapshotTime = Time[that.count] + 'h'
        that.msg2.totalNum = historySubmitJob[that.count]
        that.msg2.execNum = historyCompleteJob[that.count]
        that.runningTime = Time[that.count] + 'h'

        that.showStart = true
      }, 2000)
    },

    // 开始仿真
    start () {
      this.prepareForStart()

      if (this.taskId !== 0) {
        this.getJobData().then(({ taskIdRes, compareIdRes }) => {
          this.taskIdRes = taskIdRes
          this.compareIdRes = compareIdRes

          if (taskIdRes) {
            this.processJobData(taskIdRes, compareIdRes)
            this.dataAlreadyLoaded = true

            this.$nextTick(() => {
              if (this.$refs.mapContainer) {
                // 数据已加载完成，通知地图组件刷新
                if (!this.taskIdRes) {
                  this.$refs.mapContainer.getJobDetail(this.taskId)
                }
              }
            })
          }
        })
      }
    },

    // 准备开始
    prepareForStart () {
      this.disable = true
      clearInterval(this.timer)
      this.count = 0
      this.dataAlreadyLoaded = false

      this.$nextTick(() => {
        this.resetChartData()
        this.Stop = false
        this.buttonShow = true
      })
    },

    // 暂停仿真
    stop () {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.nowCount = this.count
      this.buttonShow = false
      this.Stop = true
    },

    // 继续仿真
    goOn () {
      if (this.count == this.total) {
        return
      }

      // 设置基本状态标记
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 检查是否已经有加载的数据
      if (this.dataAlreadyLoaded && this.temp && this.Data.xAxis.length > 0) {
        // 使用已缓存的数据恢复动画，不需要重新请求

        // 清除任何可能存在的定时器
        clearInterval(this.timer)

        // 从当前帧重启定时器
        this.startAnimationFromCurrentFrame()
      } else {
        // 没有缓存数据，重新请求
        this.prepareForResume()

        if (this.taskId !== 0) {
          this.getJobData().then(({ taskIdRes, compareIdRes }) => {
            this.taskIdRes = taskIdRes
            this.compareIdRes = compareIdRes

            if (taskIdRes) {
              this.processJobData(taskIdRes, compareIdRes)
              this.dataAlreadyLoaded = true
            }
          })
        }
      }
    },

    // 从当前帧开始动画
    startAnimationFromCurrentFrame () {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count >= that.total) {
          clearInterval(that.timer)
          that.count = that.total
          return
        }

        // 更新数据
        if (that.temp && that.count < that.temp.length) {
          that.Data.yAxis[that.count] = that.temp[that.count]
        }

        // 更新时间信息
        if (that.Data.xAxis && that.count < that.Data.xAxis.length) {
          that.msg.SnapshotTime = that.Data.xAxis[that.count] + 'h'
          that.runningTime = that.Data.xAxis[that.count] + 'h'
        }

        // 更新环图数据
        if (that.taskIdRes && that.taskIdRes.CenterInfoToWebList) {
          const centerInfoList = that.taskIdRes.CenterInfoToWebList
          let historySubmitJob = 0
          let historyCompleteJob = 0

          // 从当前快照收集数据
          centerInfoList.forEach(center => {
            if (center.SnapshotInfoToWebList &&
                center.SnapshotInfoToWebList[that.count]) {
              historySubmitJob += center.SnapshotInfoToWebList[that.count].HistorySubmitJob
              historyCompleteJob += center.SnapshotInfoToWebList[that.count].HistoryCompleteJob
            }
          })

          // 更新环图数据
          that.msg2.totalNum = historySubmitJob
          that.msg2.execNum = historyCompleteJob
        }
      }, 2000)
    },

    // 准备恢复
    prepareForResume () {
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 重置环图数据，避免显示旧数据
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
    },

    // 处理进度条变化
    handleProgressChange (percentage) {
      this.percentage = percentage
      this.count = Math.floor((percentage / 100) * this.total)
      this.nowCount = this.count
    },

    // 处理时间间隔变化
    handleIntervalChange (value) {
      this.value = value
      this.$store.commit('changeInterval', value)
      this.Stop = true
      this.start()
    }
  },
  beforeDestroy () {
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>

<style scoped>
.coopera-pic {
  margin: 0px 220px 0px 220px;
  background-image: url("../static/screen1/cooperativeUnit.jpg");
  background-repeat: no-repeat;
  background-size: 105%;
  height: 185px;
  width: 100%;
  margin-top: -150px;
}

.el-header {
  height: 125px !important;
  background-image: url("../static/screen1/header.png");
  background-size: 100%;
}

.background {
  background-image: url("../static/screen1/background.png");
  background-size: 100% 100%;
  min-width: 2020px;
  min-height: 100vh;
  width: 100%;
}

.title {
  margin-top: 20px;
  font-size: 3rem;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  text-align: center;
  margin-left: 20px;
}

.el-main {
  padding-bottom: 0px;
  z-index: 20;
  overflow: hidden;
  padding: 0px 20px 20px 20px !important;
}

.el-container.is-vertical {
  height: 100%;
}

.footer {
  background-image: url("../assets/footer.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 90px;
}

.taskDetailWrapper {
  background-color: rgba(18, 137, 221, 0.1);
  background-size: cover;
  background-position: center center;
  padding: 10px;
  position: absolute;
  right: 0px;
  top: 150px;
  min-height: 820px;
  border: 1px solid rgba(18, 137, 221, 0.5);
  width: 425px;
}
</style>
