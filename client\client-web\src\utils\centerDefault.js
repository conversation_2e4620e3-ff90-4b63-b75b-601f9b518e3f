export const c2netComputingCenterList =
  [{
    c2netComputingCenterSpecification:
      {
        nodeSpecification:
          {
            nGBMemoriesPerGraphicsCard: 40,
            nCpus: 64,
            nGBMemories: 800,
            nGraphicsCards: 8,
            graphicsCardType: 'A100'
          },
        nNodes: 64,
        pOPS: 128,
        name: '鹏城云脑一'
      },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 40,
              nCpus: 64,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'A100'
            },
          nNodes: 50,
          pOPS: 100,
          name: '鹏城云计算所集群'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 40,
              nCpus: 32,
              nGBMemories: 800,
              nGraphicsCards: 4,
              graphicsCardType: 'A100'
            },
          nNodes: 1,
          pOPS: 1,
          name: '启智混合智算集群'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 50,
          pOPS: 100,
          name: '昇腾宁波智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 150,
          pOPS: 300,
          name: '昇腾重庆智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 100,
          pOPS: 200,
          name: '昇腾武汉智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 50,
          pOPS: 100,
          name: '昇腾广州智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 50,
          pOPS: 100,
          name: '昇腾大连智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 100,
          pOPS: 200,
          name: '昇腾天津智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 50,
          pOPS: 100,
          name: '昇腾长春智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 32,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'V100'
            },
          nNodes: 20,
          pOPS: 32,
          name: '合肥类脑'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 150,
          pOPS: 300,
          name: '昇腾成都智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 50,
          pOPS: 100,
          name: '昇腾中原智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 50,
          pOPS: 100,
          name: '昇腾沈阳智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 10,
          pOPS: 20,
          name: '新疆大学智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 150,
          pOPS: 300,
          name: '昇腾西安智算中心'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 512,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ASCEND910'
            },
          nNodes: 512,
          pOPS: 1024,
          name: '鹏城云脑二'
        },
    nCopiedC2netComputingCenters: 1
  },
  {
    c2netComputingCenterSpecification:
        {
          nodeSpecification:
            {
              nGBMemoriesPerGraphicsCard: 32,
              nCpus: 64,
              nGBMemories: 800,
              nGraphicsCards: 8,
              graphicsCardType: 'ENFLAME-T20'
            },
          nNodes: 8,
          pOPS: 16,
          name: '启智燧原集群'
        },
    nCopiedC2netComputingCenters: 1
  }]
