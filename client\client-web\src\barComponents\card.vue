<template>
    <div>
        <el-row>
            <el-col :span="24">
                <div class="state1 pendingBorder">
                    <span class="pending"></span><span>等待中</span>
                </div>
                <div class="state2">该节点排队中任务数量</div>
                <div class="state2"><span class="pendingNum">{{pendingNum}}</span><span class="nodeUnit">个</span></div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <div class="state1 runningBorder">
                    <span class="running"></span><span>运行中</span>
                </div>
                <div class="state2">该节点正在运行任务数量</div>
                <div class="state2"><span class="runningNum">{{runningNum}}</span><span class="nodeUnit">个</span></div>
            </el-col>
        </el-row>

    </div>
</template>
<script>
export default {
  name: 'pie',
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Number,
      default: 20
    },
    title: {
      type: String,
      default: ''
    },
    pendingNum: { type: Number, default: 0 },
    runningNum: { type: Number, default: 0 }
  },
  watch: {
    pendingNum: {
      handler (newValue, oldValue) {
        this.pendingNum = newValue
      }

    },
    runningNum: {
      handler (newValue, oldValue) {
        this.runningNum = newValue
      }

    }

  },
  mounted () {
  },
  methods: {

  }

}
</script>
<style scoped>
    .state1 {
        padding: 10px;
        color: rgba(250, 251, 252, 1);
        font-size: 20px;
        font-family: "思源黑体 CN-Regular";
        letter-spacing: 1px;
        font-weight: normal;
        font-style: normal;
        line-height: 20px;
        margin-left: 50px;
        text-align: left;
        display: inline-block;
    }

    .pendingBorder {
        border: 1px solid rgb(255, 200, 24);
    }

    .runningBorder {
        border: 1px solid rgb(30, 231, 231);
    }

    .pendingNum {
        color: rgb(255, 200, 24);
        font-size: 30px;
    }

    .runningNum {
        color: rgb(30, 231, 231);
        font-size: 30px;
    }

    .state2 {
        padding: 10px;
        color: rgba(250, 251, 252, 1);
        font-size: 20px;
        font-family: "思源黑体 CN-Regular";
        letter-spacing: 1px;
        font-weight: normal;
        font-style: normal;
        line-height: 20px;
        margin: 20px 0 10px 50px;
        text-align: left;
    }

    .pending {
        color: rgb(255, 200, 24)
    }

    .running {
        color: rgb(30, 231, 231)
    }

    .nodeUnit {
        color: #fff;
        display: inline-block;
        margin-left: 10px;
        font-size: 30px;
        font-weight: 800;
    }

    .pending {
        width: 20px;
        height: 20px;
        background-color: rgb(255, 200, 24);
        display: inline-block;
        margin-right: 10px;
    }

    .running {
        background-color: rgb(30, 231, 231);
        width: 20px;
        height: 20px;
        display: inline-block;
        margin-right: 10px;
    }
</style>
