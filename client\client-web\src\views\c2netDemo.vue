<template>
  <div class="background" @click="hiddenNavbar">
    <el-container>
      <el-header>
        <el-row type="flex" justify="center">
          <el-col :span="12">
            <div class="title">
              中国算力网<span class="smalltitle">(C²NET)</span>国家总调度中心
            </div>
          </el-col>
        </el-row>
      </el-header>
      <dropdown class="dropdown" :type="'c2netDemo'"></dropdown>
      <el-main>
        <el-row type="flex" justify="space-around">
          <el-col :span="5">
            <div class="showMt">
              <el-row class="linear-gradient">
                <el-col :span="14">
                  <el-row>
                    <el-col :span="5">
                      <div class="arrow"></div>
                    </el-col>
                    <el-col :span="19">
                      <div class="title1">累计任务情况</div>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <div>
                    <CumulativeTask :config="accrueData"></CumulativeTask>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="showMt">
              <el-row class="linear-gradient">
                <el-col :span="2">
                  <div class="arrow"></div>
                </el-col>
                <el-col :span="20">
                  <div class="title1">运行任务量及等待任务量</div>
                </el-col>
              </el-row>
              <TASK id="task" :config="taskConfig"></TASK>
            </div>
            <div class="showMt">
              <el-row class="linear-gradient">
                <el-col :span="2">
                  <div class="arrow"></div>
                </el-col>
                <el-col :span="22">
                  <div class="title1">数据调度任务监控</div>
                </el-col>
              </el-row>
              <Monitor></Monitor>
            </div>
          </el-col>
          <el-col :span="14">
            <div>
              <el-row type="flex" justify="center">
                <el-col :span="22">
                  <Total :data="totalNum" :statusData="statusData"></Total>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <!-- <chinaMap
                    :mapData="mapData"
                    :show="type"
                    @showTip="showTip"
                    @hiddenTip="hiddenTip"
                    :desMap="desMap"
                    v-if="show"
                  ></chinaMap> -->
                  <!-- <div class="statusWrapper" v-if="show1 || show3">
                    <div class="wrapper">
                      <div
                        :class="{
                          buttonStyle2: true,
                          check2: type1,
                          title2: true,
                        }"
                        @click="type = 1"
                      >
                        <span class="img1 icon"></span
                        ><span class="text">智算中心</span>
                        <span class="text">{{ intelligenceTotal }}</span>
                      </div>
                      <div
                        :class="{
                          buttonStyle2: true,
                          check2: type2,
                          title2: true,
                        }"
                        @click="type = 2"
                      >
                        <span class="img2 icon"></span
                        ><span class="text">超算中心</span>
                        <span class="text">{{ superTotal }}</span>
                      </div>
                      <div
                        :class="{
                          buttonStyle2: true,
                          check2: type3,
                          title2: true,
                        }"
                        @click="type = 3"
                      >
                        <span class="img3 icon"></span
                        ><span class="text">东数西算</span>
                        <span class="text">{{ eAdnwTotal }}</span>
                      </div>
                      <div class="title3" @click="type = 3">
                        <span class="img4 icon2"></span>
                        <span class="text">已接入</span>
                        <span class="text">{{ statusData.connected }}</span>
                      </div>
                      <div class="title3" @click="type = 3">
                        <span class="img5 icon2"></span>
                        <span class="text">接入中</span>
                        <span class="text">{{ statusData.accessing }}</span>
                      </div>
                      <div class="title3" @click="type = 3">
                        <span class="img6 icon2"></span>
                        <span class="text">待接入</span>
                        <span class="text">{{ statusData.pending }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="statusWrapper2" v-if="show2">
                    <div class="wrapper">
                      <div class="arrow1">
                        <span class="ml-2">10TB全光网络互联</span>
                      </div>
                      <div class="arrow2">
                        <span class="ml-2">SD-WAN互联</span>
                      </div>
                      <div class="arrow3">
                        <span class="ml-2">MPLS互联</span>
                      </div>
                    </div>
                  </div>
                  <provinceMap
                    v-if="show1"
                    style="position: absolute; top: 0px"
                  ></provinceMap>
                  <provinceMap2
                    v-if="show2"
                    style="position: absolute; top: 0px"
                  ></provinceMap2>
                  <provinceMap3
                    v-if="show3"
                    :Data="tipData"
                    style="position: absolute; top: 0px"
                  ></provinceMap3> -->
                  <!-- <el-row
                    type="flex"
                    justify="center"
                    :gutter="30"
                    style="position: relative; top: -80px"
                  >
                    <el-col :span="5">
                      <div
                        :class="{ buttonStyle: true, check: show1 }"
                        @click="changeNetwork('computing')"
                      >
                        算力资源调度
                      </div>
                    </el-col>
                    <el-col :span="5">
                      <div
                        :class="{ buttonStyle: true, check: show2 }"
                        @click="changeNetwork('new')"
                      >
                        超宽带光纤直连
                      </div>
                    </el-col>
                  </el-row> -->
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="showMt">
              <el-row class="linear-gradient" style="margin-bottom: 3px">
                <el-col :span="2">
                  <div class="arrow"></div>
                </el-col>
                <el-col :span="20">
                  <div class="title1">算力使用趋势</div>
                </el-col>
              </el-row>
              <TEND id="TEND" :config="tendConfig"></TEND>
            </div>
            <div class="showMt">
              <el-row class="linear-gradient">
                <el-col :span="2">
                  <div class="arrow"></div>
                </el-col>
                <el-col :span="20">
                  <div class="title1">各中心算力使用情况</div>
                </el-col>
              </el-row>
              <STATUS
                id="Status"
                :data="centerData"
                :config="statusConfig"
              ></STATUS>
            </div>
            <div class="showMt">
              <el-row class="linear-gradient" style="margin-bottom: 20px">
                <el-col :span="2">
                  <div class="arrow"></div>
                </el-col>
                <el-col :span="20">
                  <div class="title1">算力接入情况</div>
                </el-col>
              </el-row>
              <INSERT
                id="Imsert"
                :data="connectedData"
                :config="connectedConfig"
              ></INSERT>
            </div>
          </el-col>
        </el-row>
      </el-main>
      <el-row type="flex" justify="center">
        <div class="coopera-pic"></div>
      </el-row>
    </el-container>
    <!-- <el-footer class="footer">Footer</el-footer> -->
  </div>
</template>
<script>
// eslint-disable-next-line import/no-duplicates
import TEND from '@/components/lineChart'
// eslint-disable-next-line import/no-duplicates
import STATUS from '@/components/Histogram'
// eslint-disable-next-line import/no-duplicates
import INSERT from '@/components/HisAndLine'
// eslint-disable-next-line import/no-duplicates
import TASK from '@/components/Histogram2'
import Monitor from '@/components/schedulingMon'
import Total from '@/components/TotalDemo'
import chinaMap from '@/components/chinaMapDemo'
// import statusMap from "@/components/statusMap";
import provinceMap from '@/components/provinceMap4'
import provinceMap2 from '@/components/provinceMap5'
import provinceMap3 from '@/components/provinceMap3'
import dropdown from '@/components/Dropdown'
import circle from '@/components/circle'
import moment from 'moment'
import { order } from '@/utils/index'
import { getConfig } from '@/utils/config'
import {
  getAicenter,
  getTrainJob,
  getAccrueCenter,
  getConnected,
  getDynamic
} from '@/api/screenService.js'
export default {
  components: {
    TEND: TEND,
    STATUS: STATUS,
    INSERT: INSERT,
    TASK: TASK,
    Monitor: Monitor,
    Total: Total,
    chinaMap: chinaMap,
    // statusMap: statusMap,
    provinceMap: provinceMap,
    provinceMap2: provinceMap2,
    provinceMap3: provinceMap3,
    CumulativeTask: circle,
    dropdown: dropdown
  },
  data () {
    return {
      npuData: { used: [], unused: [], xData: [] },
      gpuData: { used: [], unused: [], xData: [] },
      cpuData: { used: [], unused: [], xData: [] },
      taskConfig: { unit: '单位:个', status: ['运行任务数', '等待任务数'] },
      statusConfig: { unit: '单位:  卡时', status: ['使用量'] },
      tendConfig: { unit: '单位:  卡时', status: ['使用量'] },
      connectedConfig: { unit: '单位:  POps@FP16', status: ['接入数'] },
      mapData: [],
      statusData: { connected: 0, accessing: 0, pending: 0 },
      crossDomain: [
        { value: 64, name: '已完成' },
        { value: 30, name: '等待中' },
        { value: 58, name: '处理中' }
      ],
      crossTitle: '',
      collaborate: [
        { value: 64, name: '已完成' },
        { value: 30, name: '等待中' },
        { value: 58, name: '处理中' }
      ],
      coollTitle: '',
      totalNum: {
        centerTotal: 0,
        computingPower1: 0,
        storageScale1: 0,
        computingPower2: 0,
        storageScale2: 0
      },
      tableData: [],
      pageIndex: 1,
      pageSize: 100,
      timer: null,
      timer2: null,
      timer3: null,
      itemNow: [],
      item1: [], // item1智算
      item2: [], // item2超算
      item3: [], // item3集群东数西算
      item4: [], // 全部
      show: false,
      show1: true,
      show2: false,
      show3: false,
      type1: true,
      type2: false,
      type3: false,
      type: 4,
      networkType: 'computing',
      accrueData: {
        config1: undefined,
        config2: undefined,
        config3: undefined
      },
      tendData: { used: [], xData: [] },
      centerData: { used: [], xData: [] },
      connectedData: { used: [], xData: [] },
      total: 0,
      intelligenceTotal: 0,
      superTotal: 0,
      eAdnwTotal: 0,
      tipData: {},
      desMap: null,
      destination: []
    }
  },
  created () {
    this.getTaskTotal()
    this.getAicenter()
    this.getTrainJob()
    this.getAccrueCenter()
    this.getConnected()
  },
  mounted () {
    this.getNewData()
  },
  watch: {
    type: {
      handler (newValue, oldValue) {
        this.getAicenter()
      }
    }
  },
  methods: {
    changeNetwork (value) {
      this.networkType = value
      if (this.networkType == 'computing') {
        this.type = 4
        this.show1 = true
        this.show2 = false
        this.show3 = false
        this.getAicenter()
      } else {
        this.show1 = false
        this.show2 = true
        this.show3 = false
        this.mapData = []
      }
    },
    getAicenter () {
      getAicenter({ sortBy: 'weight', orderBy: 'asc' }).then((res) => {
        this.statusData = { connected: 0, accessing: 0, pending: 0 }
        // this.mapData = [];
        this.totalNum.centerTotal = res.aiCenterInfos.length
        let computingPower1 = 0
        let storageScale1 = 0
        let computingPower2 = 0
        let storageScale2 = 0
        this.item1 = []
        this.item2 = []
        this.item3 = []
        this.item4 = []
        res.aiCenterInfos.forEach((item) => {
          if (item.centerType == '东数西算') {
            this.item3.push(item)
          } else if (item.centerType == '超算中心') {
            this.item2.push(item)
          } else {
            this.item1.push(item)
          }
        })
        this.intelligenceTotal = this.item1.length
        this.superTotal = this.item2.length
        this.eAdnwTotal = this.item3.length
        switch (this.type) {
          case 1:
            this.type1 = true
            this.type2 = false
            this.type3 = false
            this.type4 = false
            this.itemNow = this.item1
            break
          case 2:
            this.type2 = true
            this.type1 = false
            this.type3 = false
            this.type4 = false
            this.itemNow = this.item2
            break
          case 3:
            this.type3 = true
            this.type2 = false
            this.type1 = false
            this.type4 = false
            this.itemNow = this.item3
            break
          default:
            this.type1 = false
            this.type2 = false
            this.type3 = false
            this.type4 = true
            this.item4 = this.item1.concat(this.item2).concat(this.item3)
            this.itemNow = this.item4
        }
        this.mapData = []
        this.itemNow.forEach((item) => {
          this.mapData.push({
            coordinateY: item.coordinateY,
            coordinateX: item.coordinateX,
            name: item.name,
            province: item.province,
            computeScale: item.computeScale,
            accessTime: item.accessTime,
            cardRunTime: item.cardRunTime,
            jobCount: item.jobCount,
            connectionState: item.connectionState,
            centerType: item.centerType
          })
          // 状态数
          if (item.connectionState == 3) {
            this.statusData.connected++
          } else if (item.connectionState == 1) {
            this.statusData.accessing++
          } else if (item.connectionState == 2) {
            this.statusData.pending++
          }
        })
        res.aiCenterInfos.forEach((item) => {
          if (item.connectionState == 3) {
            (computingPower1 = computingPower1 + item.computeScale),
            (storageScale1 = storageScale1 + item.storageScale)
          }
          if (item.connectionState != 3) {
            (computingPower2 = computingPower2 + item.computeScale),
            (storageScale2 = storageScale2 + item.storageScale)
          }
        })
        this.total =
          this.statusData.connected +
          this.statusData.accessing +
          this.statusData.pending
        this.totalNum.computingPower1 = computingPower1
        this.totalNum.storageScale1 = storageScale1
        this.totalNum.computingPower2 = computingPower2
        this.totalNum.storageScale2 = storageScale2
      })
    },
    getTrainJob () {
      getTrainJob({ pageIndex: this.pageIndex, pageSize: this.pageSize }).then(
        (res) => {
          this.tableData = []
          res.otJobs ? res.otJobs : []
          res.otJobs.forEach((item) => {
            if (this.showUnderTaker(item) !== '') {
              this.tableData.push({
                name: item.name,
                status: item.status,
                undertaker: this.showUnderTaker(item)
              })
            }
          })
          this.desMap = getConfig()
          this.destination = []
          res.otJobs.forEach((item) => {
            if (
              item.tasks[0] !== null &&
              item.tasks[0].centerName != null &&
              item.tasks[0].centerName[0]
            ) {
              this.destination.push(item.tasks[0].centerName[0])
            }
          })
          this.destination = Array.from(new Set(this.destination))
          this.desMap.lines = this.desMap.lines.filter((item) => {
            if (this.destination.indexOf(item.target) !== -1) {
              return item
            }
          })
          this.show = true
        }
      )
    },
    showUnderTaker (item) {
      if (item.tasks == null) {
        return ''
      } else if (item.tasks.length > 2) {
        return item.tasks[0].centerName[0] + '等'
      } else {
        if (item.tasks[0].centerName == null) {
          return ''
        } else {
          return item.tasks[0].centerName[0]
        }
      }
    },
    getTaskTotal () {
      getDynamic().then((res) => {
        if (res) {
          if (res.allCardRunTime && res.allJobCount && res.allJobRunTime) {
            const data = res
            this.accrueData = {
              config1: data.allCardRunTime,
              config2: data.allJobCount,
              config3: data.allJobRunTime
            }
          }
        }
      })
    },
    getAccrueCenter () {
      getAccrueCenter().then((res) => {
        this.centerData = { used: [], xData: [] }
        if (res.perCenterComputerPowers) {
          const data = res.accOtJobInfo
          const data1 = res.perCenterComputerPowers.filter((item) => {
            if (item.computerPower !== 0) {
              return item
            }
          })
          data1.sort(order)
          data1.forEach((item) => {
            if (item.computerPower !== 0) {
              this.centerData.used.push(item.computerPower.toFixed(1))
              this.centerData.xData.push(item.centerName)
            }
          })
        }
      })
    },
    getConnected () {
      getConnected().then((res) => {
        this.connectedData = { used: [], xData: [] }
        if (res.computerPowerInfos !== null) {
          res.computerPowerInfos.forEach((item) => {
            item.monthTime = item.monthTime
              .replace(/-/g, '.')
              .replace('.0', '.')
          })
        }
        if (res.computerPowerInfos !== null) {
          res.computerPowerInfos.forEach((item, index, array) => {
            if (
              item.monthAccPower == 0 &&
              array[index + 1].monthAccPower != 0
            ) {
              this.connectedData.used.push(item.monthAccPower)
              this.connectedData.xData.push(item.monthTime)
            }
            if (item.monthAccPower !== 0) {
              this.connectedData.used.push(item.monthAccPower)
              this.connectedData.xData.push(item.monthTime)
            }
          })
        }
      })
    },
    showTip (e) {
      if (this.show2 == false) {
        if (e.showTip == true) {
          this.show1 = false
          this.show3 = true
          this.tipData = JSON.parse(JSON.stringify(e))
        }
      }
    },
    hiddenTip (e) {
      if (this.show2 == false) {
        if (e == false) {
          this.show1 = true
          this.show3 = false
          this.show2 = false
        }
      }
    },
    hiddenNavbar () {
      var target = document.getElementById('navbarToggleExternalContent')
      target.className = 'collapse'
    },
    getNewData () {
      var nowTemp = new Date().getTime() // 获取当前时间戳
      var tomorrowTemp = new Date(
        moment().add(1, 'days').format().substr(0, 10) + ' 00:00:00'
      ).getTime() // 获取今天24：00的时间戳
      var residueTemp = tomorrowTemp - nowTemp // 距离当天24：00的时间戳
      setTimeout(() => {
        // location.reload();
        // 次天0点 执行每天24;00 刷新
        setTimeout(() => {
          location.reload()
        }, 60000)
      }, residueTemp)
    }
  },
  beforeDestroy () {
    this.timer && clearInterval(this.timer)
    this.timer2 && clearInterval(this.timer2)
    this.timer3 && clearInterval(this.timer3)
  }
}
</script>
<style scoped>
.coopera-pic {
  margin: 0px 220px 0px 220px;
  background-image: url("../static/screen1/cooperativeUnit.jpg");
  background-repeat: no-repeat;
  background-size: 105%;
  height: 185px;
  width: 100%;
  margin-top: -150px;
  /* background-position-x: 290px; */
}
.el-header {
  height: 4rem !important;
}

.background {
  background-image: url("../static/screen1/background-240313-01.jpeg");
  background-size: 100% 100%;
  min-width: 1800px;
  min-height: 100vh;
  width: 100%;
}

.title {
  margin-top: 20px;
  font-size: 3rem;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  /* transform: translateY(-50%); */
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
}

/* .subTitle {
    margin-top: 1px;
    font-family: "思源黑体 CN-Regular";
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0px;
    line-height: 20px;
    text-align: center;
    width: 100%;
    font-style: italic;
    font-weight: normal;
    filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  } */

.subTitle span {
  letter-spacing: 1px;
}

.title1 {
  font-family: 庞门正道标题体3;
  font-size: 24px;
  color: rgb(255, 255, 255);
  letter-spacing: 2px;
  line-height: 24px;
  text-align: left !important;
  width: 100%;
  font-style: italic;
  font-weight: normal;
  text-align: center;
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
}

.province {
  width: 100px;
  height: 100px;
}

.arrow {
  background-size: 100% 100%;
  background-image: url("../static/screen1/arrow2.png");
  width: 50px;
  height: 35px;
  position: relative;
  top: -5px;
  background-repeat: no-repeat;
}

.linear-gradient {
  background: linear-gradient(
    to right,
    rgba(255, 0, 0, 0),
    rgba(255, 255, 255, 0.2)
  );
  margin: 12px 0 18px 0;
  box-sizing: content-box;
  background-clip: content-box;
}

.el-main {
  /* height: 900px; */
  padding-top: 40px;
  padding-bottom: 0px;
  z-index: 100;
}

.el-container.is-vertical {
  height: 100%;
}

.showMt {
  margin-top: 25px !important;
}

.buttonStyle {
  height: 50px;
  color: white;
  line-height: 50px;
  text-align: center;
  background-color: rgba(255, 255, 255, 10%);
  cursor: pointer;
  min-width: 200px;
}

.check {
  font-weight: 800;
  background-image: linear-gradient(
    rgba(30, 231, 231, 40%),
    rgba(255, 255, 255, 10%),
    rgba(255, 255, 255, 0%)
  );
}

.footer {
  background-image: url("../assets/footer.svg");
  background-repeat: no-repeat;
  /* min-height: 20px !important; */
  background-size: 100% 100%;
}

.wrapper {
  position: absolute;
  left: 25px;
}

.icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.icon2 {
  display: inline-block;
  width: 25px;
  height: 25px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.text {
  display: inline-block;
  line-height: 18px;
  height: 18px;
  position: relative;
  top: -2px;
  margin-right: 2px;
}

.img1 {
  background-image: url("../static/screen1/intellectual.svg");
}

.img2 {
  background-image: url("../static/screen1/super.svg");
}

.img3 {
  background-image: url("../static/screen1/eandw.svg");
}

.img4 {
  background-image: url("../assets/statusEd.svg");
}

.img5 {
  background-image: url("../assets/statusIng.svg");
}

.img6 {
  background-image: url("../assets/statusUn.svg");
}

.buttonStyle2 {
  /* height: 50px; */
  color: white;
  /* line-height: 50px; */
  /* text-align: center; */
  /* background-color: rgba(255, 255, 255, 10%); */
  cursor: pointer;
  min-width: 150px;
  margin-bottom: 10px;
}

.check2 {
  color: #fff !important;
  background-image: linear-gradient(
    to right,
    rgba(24, 144, 255, 0%),
    rgba(24, 144, 255, 10%),
    rgba(24, 144, 255, 40%)
  );
}

.statusWrapper {
  position: absolute;
  right: 220px;
  top: 560px;
}

.statusWrapper2 {
  position: absolute;
  right: 220px;
  top: 650px;
}

.title2 {
  color: rgba(255, 255, 255, 0.7);
  line-height: 40px;
}

.title3 {
  line-height: 40px;
  color: rgba(255, 255, 255, 0.7);
  width: 140px;
}

.arrow1 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrow10TB.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow2 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowSD.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow3 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowMPLS.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.mt-2 {
  margin-top: 20px;
}

.ml-2 {
  margin-left: 65px;
}

.smalltitle {
  letter-spacing: 0px;
  font-size: 2.6rem;
}
.dropdown {
  width: 225px;
  position: fixed;
  right: 30px;
  top: 20px;
  z-index: 200;
}
</style>
