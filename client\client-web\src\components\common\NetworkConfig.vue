<template>
  <div class="network-config">
    <!-- 网络部分 -->
    <div class="section-header">
      <span class="section-title">网络</span>
    </div>

    <div class="section-content form-section">
      <div class="form-grid">
        <div class="form-item-container">
          <el-form-item label="构建网络拓扑" prop="construct">
            <el-switch v-model="networkForm.construct" active-color="#1289DD"></el-switch>
          </el-form-item>
        </div>

        <div class="form-item-container">
          <el-form-item label="网络传输加速" prop="accelerator">
            <el-switch v-model="networkForm.accelerator" active-color="#1289DD"></el-switch>
          </el-form-item>
        </div>

        <div class="form-item-container">
          <el-form-item label="网络拓扑选择" prop="topologyTpye">
            <div class="topology-select-container">
              <el-select v-model="networkForm.topologyTpye" :disabled="!networkForm.construct" class="fixed-width" placeholder="请选择网络拓扑">
                <el-option label="拓扑 01" value="topology01"></el-option>
                <el-option label="拓扑 02" value="topology02"></el-option>
                <el-option label="拓扑 03" value="topology03"></el-option>
              </el-select>
              <el-button
                class="preview-topology-btn"
                size="small"
                type="primary"
                @click="showTopologyPreview">
                <i class="el-icon-picture"></i> 网络拓扑示意
              </el-button>
            </div>
          </el-form-item>
        </div>
      </div>
    </div>

    <!-- 网络拓扑预览对话框 -->
    <el-dialog
      :visible.sync="topologyPreviewVisible"
      :close-on-click-modal="false"
      append-to-body
      custom-class="task-dialog topology-preview-dialog"
      title="网络拓扑示意"
      width="600px"
    >
      <div class="topology-preview-container">
        <el-image
          :preview-src-list="[currentTopologyImage]"
          :src="currentTopologyImage"
          fit="contain">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
        </el-image>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="topologyPreviewVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import topologyPreviewMixin from '@/mixins/topologyPreviewMixin'

export default {
  name: 'NetworkConfig',
  mixins: [topologyPreviewMixin],
  props: {
    value: {
      type: Object,
      default: () => ({
        construct: false,
        accelerator: false,
        topologyTpye: 'topology01'
      })
    }
  },
  data () {
    return {
      networkForm: {
        construct: false,
        accelerator: false,
        topologyTpye: 'topology01'
      }
    }
  },
  created () {
    this.networkForm = { ...this.value }
  },
  watch: {
    value: {
      handler (newVal) {
        // 避免循环更新，只有当值真正不同时才更新
        if (JSON.stringify(newVal) !== JSON.stringify(this.networkForm)) {
          this.networkForm = { ...newVal }
        }
      },
      deep: true
    },
    networkForm: {
      handler (newVal) {
        // 避免循环更新，只有当值真正不同时才emit
        if (JSON.stringify(newVal) !== JSON.stringify(this.value)) {
          this.$emit('input', newVal)
          this.$emit('change', newVal)
        }
      },
      deep: true
    }
  },
  computed: {
    // 为了兼容 topologyPreviewMixin 中的 taskForm.topologyTpye
    taskForm () {
      return this.networkForm
    }
  },
  methods: {
    /**
     * 验证网络配置
     * @returns {Boolean} - 验证结果
     */
    validateNetworkConfig () {
      if (this.networkForm.construct && !this.networkForm.topologyTpye) {
        this.$message.error('启用网络拓扑时必须选择拓扑类型')
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/dialog.scss';

.network-config {
  .section-header {
    margin: 5px 0;
    border-bottom: 1px solid rgba(18, 137, 221, 0.5);
    padding-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-title {
    color: #32c5ff;
    font-size: 16px;
    font-weight: bold;
    padding-left: 8px;
    border-left: 3px solid #32c5ff;
  }

  .section-content {
    padding: 5px 0 15px 0;
  }

  .form-section {
    margin-bottom: 8px;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 8px;
  }

  .form-item-container {
    display: flex;
    align-items: center;
    min-height: 32px;
  }

  .topology-select-container {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;

    .el-select {
      flex: 1;
    }

    .preview-topology-btn {
      flex-shrink: 0;
      white-space: nowrap;
    }
  }

  .topology-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 20px;

    .el-image {
      max-width: 100%;
      max-height: 400px;
    }

    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
      font-size: 30px;
    }
  }
}
</style>
