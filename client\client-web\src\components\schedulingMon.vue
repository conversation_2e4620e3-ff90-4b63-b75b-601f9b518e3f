<template>
  <div class="cardTurner">
    <el-row type="flex" justify="center">
      <el-col :span="6">
        <div class="storageWrapper">
          <div class="numTitle">迁移成功</div>
          <div class="numContent" v-if="successTotal !== 0">
            {{ successTotal }}<span class="numUnit"></span>
          </div>
          <div class="numContent" v-if="successTotal == 0">
            <span class="count"> 计算中</span>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div style="width: 100%; height: 90px" ref="echart"></div>
      </el-col>
      <el-col :span="6">
        <div class="storageWrapper">
          <div class="numTitle">迁移数据量</div>
          <div class="numContent" v-if="num !== 0">
            {{ num }}<span class="numUnit">{{ unit }}</span>
          </div>
          <div class="numContent" v-if="num == 0">
            <span class="count">计算中</span>
          </div>
        </div>
      </el-col>
    </el-row>
    <dv-loading v-show="loading" class="pos"></dv-loading>
    <el-row
      type="flex"
      justify="center"
      style="margin-top: 20px"
      v-show="!loading"
    >
      <el-col :span="24">
        <div class="block">
          <el-carousel
            trigger="click"
            height="170px"
            arrow="never"
            :autoplay="true"
            :interval="8000"
            indicator-position="outside"
            loop
          >
            <el-carousel-item v-for="(item, index) in dataList" :key="index">
              <h3 class="small">
                <div style="height: 190px">
                  <div class="taskName">
                    迁移任务: {{ item.task_id ? item.task_id : "" }}
                  </div>
                  <div class="taskMsg">
                    <el-row type="flex" class="row-bg" justify="space-between">
                      <el-col :span="8"
                        ><div class="sourceCenter">源中心</div>
                        <div class="sourceContent">
                          <div class="sourceDot"></div>
                        </div>
                        <div class="sourceName">
                          {{ item.data_source ? item.data_source : "" }}
                        </div></el-col
                      >
                      <el-col :span="8"
                        ><div class="targetCenter">目标中心</div>
                        <div class="targetContent">
                          <div class="targetDot"></div>
                        </div>
                        <div class="targetName">
                          {{
                            item.data_destination ? item.data_destination : ""
                          }}
                        </div></el-col
                      >
                      <div class="success">迁移成功</div>
                      <div class="contentLength">
                        {{
                          item["content_length"] ? item["content_length"] : 0
                        }}
                      </div>
                      <dv-flyline-chart-enhanced
                        :config="config"
                        style="width: 100%; height: 100%"
                        class="storageFly"
                        :key="index"
                      />
                      <div class="flow-arrow">
                        <div class="arrow-body">
                          <div class="flow-body"></div>
                        </div>
                      </div>
                    </el-row>
                  </div>
                </div>
              </h3>
            </el-carousel-item>
          </el-carousel>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {
  taskList,
  folderList,
  folderMsg,
  taskMsg
} from '@/api/screenService.js'
import { getDest, compare } from '@/utils/index.js'
export default {
  name: 'storageMon',
  props: {},
  watch: {
    dataNumTotal: {
      handler (newValue, oldValue) {
        this.num = this.change(newValue).number
        this.unit = this.change(newValue).unit
      }
    }
  },
  data () {
    return {
      total: '计算中',
      successTotal: 0,
      dataNumTotal: 0,
      fileTotal: 0,
      num: 0,
      unit: '',
      option: {
        series: [
          {
            // 类型
            type: 'gauge',
            // 半径
            radius: '195%',
            // 起始角度。圆心 正右手侧为0度，正上方为90度，正左手侧为180度。
            startAngle: 180,
            // 结束角度。
            endAngle: 0,
            center: ['50%', '98%'],
            progress: {
              // 展示当前进度。
              show: true, // 是否展示进度条
              overlap: false, // 是否重叠
              roundCap: false, // 是否圆顶
              clip: false // 是否裁掉超出部分
            },
            max: 0,
            // 仪表盘轴线相关配置。
            axisLine: {
              show: true,
              lineStyle: {
                width: 12,
                color: [[1, 'rgba(1, 145, 255, 0.2)']]
              }
            },
            itemStyle: {
              // 具体颜色显示
              color: {
                type: 'radial', // 使用径向渐变色
                x: 0.2, // 渐变色的起始点位置
                y: 1,
                r: 1, // 渐变半径，值为0到1之间
                colorStops: [
                  { offset: 0, color: 'rgba(3, 118, 210, 0.1)' }, // 渐变颜色1
                  { offset: 1, color: 'rgba(41, 159, 209,1)' } // 渐变颜色2
                ]
              }
            },
            // 分隔线样式。
            splitLine: {
              show: false
            },
            // 刻度样式。
            axisTick: {
              show: false
            },
            // 刻度标签。
            axisLabel: {
              show: false
            },
            // 仪表盘指针。
            pointer: {
              // 这个show属性好像有问题，因为在这次开发中，需要去掉指正，我设置false的时候，还是显示指针，估计是BUG吧，我用的echarts-3.2.3；希望改进。最终，我把width属性设置为0，成功搞定！
              show: false,
              // 指针长度
              length: '90%',
              width: 0
            },
            // 仪表盘标题。
            title: {
              show: true,
              offsetCenter: [0, '-40%'], // x, y，单位px

              color: '#000',
              fontSize: 30
            },
            // 仪表盘详情，用于显示数据。
            detail: {
              show: true,
              offsetCenter: [0, '-35%'],
              formatter: (params) => {
                return `{a|总迁移任务}
                 {b|${this.total}}`
              },
              fontSize: 1,
              rich: {
                a: {
                  fontSize: 14,
                  color: 'rgba(255, 255, 255,0.7)'
                  //  fontWeight: "normal",
                },
                b: {
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: 'rgb(1, 145, 255)'
                }
              }
            },
            data: [
              {
                value: 0,
                name: ''
              }
            ]
          }
        ]
      },
      dataList: [
        {
          content_length: '0KB',
          data_destination: '',
          data_source: '',
          id: 0
        }
      ],
      config: {
        points: [
          {
            name: 'source',
            coordinate: [0.165, 0.55],
            halo: {
              show: true,
              radius: 80,
              color: '#0376D2',
              duration: [10, 30]
            }
          },
          {
            name: 'target',
            coordinate: [0.83, 0.55],
            halo: {
              show: true,
              radius: 80,
              color: '#299FD1',
              duration: [10, 30]
            }
          }
        ],
        lines: [
          {
            source: 'source',
            target: 'target',
            width: 10,
            color: '#299FD1',
            radius: 400
          }
        ],
        curvature: 100,
        k: 0
      },
      loading: true
      // timer: null,
    }
  },
  created () {
    this.dataNumTotal = 0
    this.getAmount()
    this.getList()
  },
  mounted () {
    this.drawLine()
    window.addEventListener('resize', this.resize)
    window.addEventListener('visibilitychange', this.Refresh)
    this.timer = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('visibilitychange', this.Refresh)
    clearInterval(this.timer)
    this.timer = null
  },
  destroyed () {},
  methods: {
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      if (!this.myChart) {
        this.myChart = this.$echarts.init(this.$refs.echart)
      }
      const option = this.option
      if (this.total === '计算中') {
        option.series[0].detail.rich.b = {
          fontSize: 12,
          fontWeight: 'normal',
          color: 'rgb(222,222,222)'
        }
      } else {
        option.series[0].detail.rich.b = {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#3eaf7c'
        }
      }
      option.series[0].data[0].value = this.successTotal
      option.series[0].max = this.total
      option && this.myChart.setOption(option)
    },
    convert (dataList) {
      const that = this
      dataList.forEach((item, index) => {
        item.content_length =
          this.change(item.content_length).number +
          this.change(item.content_length).unit
        item.data_destination = that.getDest(item.data_destination)
        item.data_source = that.getDest(item.data_source)
        item.task_id = item.task_id.slice(0, 20) + '...'
      })
    },
    getList () {
      const data = []
      this.dataList = []
      taskList({ page: 1, per_page: 50, state: 'Succeeded' }).then((res) => {
        if (res) {
          res.tasks.forEach((item) => {
            data.push(item)
          })
          folderList({ page: 1, per_page: 50, state: 'Succeeded' }).then(
            (res) => {
              res.tasks.forEach((item) => {
                data.push(item)
              })
              data.forEach((item) => {
                item.created_at = new Date(
                  item.created_at.replace('T', ' ').replace('Z', ' ')
                ).getTime()
              })
              data.sort(this.compare('created_at'))
              data.forEach((item) => {
                if (item.content_length !== 0) {
                  this.dataList.push(item)
                }
              })
              this.dataList = this.dataList.slice(0, 5)
              this.convert(this.dataList)
              this.loading = false
            }
          )
        }
      })
    },
    getAmount () {
      let successTaskTotal = 0
      let taskTotal = 0
      let taskNumTotal = 0
      let successFolderTotal = 0
      let folderTotal = 0
      let folderNumTotal = 0
      taskMsg().then((res) => {
        if (res) {
          this.$nextTick(() => {
            successTaskTotal = res.success_file
            taskTotal = res.total_file
            taskNumTotal = res.total_size
            folderMsg().then((res) => {
              if (res) {
                successFolderTotal = res.success_file
                folderTotal = res.total_file
                folderNumTotal = res.total_size
                this.successTotal = successTaskTotal + successFolderTotal
                this.total = taskTotal + folderTotal
                this.dataNumTotal = taskNumTotal + folderNumTotal

                this.drawLine()
              }
            })
          })
        }
      })
    },
    change (size) {
      const data = { number: 0, unit: '' }
      if (!size) return ''
      var num = 1024.0 // byte
      if (size < num) {
        data.number = size
        data.unit = 'B'
        return data
      }
      if (size < Math.pow(num, 2)) {
        data.number = (size / num).toFixed(2)
        data.unit = 'KB'
        return data
      }
      if (size < Math.pow(num, 3)) {
        data.number = (size / Math.pow(num, 2)).toFixed(2)
        data.unit = 'MB'
        return data
      }
      if (size < Math.pow(num, 4)) {
        data.number = (size / Math.pow(num, 3)).toFixed(2)
        data.unit = 'GB'
        return data
      } else {
        data.number = (size / Math.pow(num, 4)).toFixed(2)
        data.unit = 'TB'
        return data
      }
    },
    getDest (value) {
      return getDest(value)
    },
    compare (value) {
      return compare(value)
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    }
  }
}
</script>
<style scoped>
.wrapper {
  padding: 10px;
  position: relative;
  color: red;
}

.dv-digital-flop {
  margin: 0 auto;
}

.title {
  color: rgb(24, 144, 255);
  font-size: 18px;
  font-weight: 400;
  margin: 5px 0 5px 50px;
}

.rateTitle {
  color: rgb(24, 144, 255);
  font-size: 14px;
  font-weight: 400;
  margin: 5px 0 0px 50px;
}

.runtimeTitle {
  color: rgb(24, 144, 255);
  font-size: 14px;
  font-weight: 400;
  margin: 5px 0 0px 50px;
}

.borderBox {
  display: inline-block;
  width: 34px;
  height: 60px;
  background-color: rgba(24, 144, 255, 0.4);
  margin: 0px 5px 0px 0px;
}

.Border {
  display: inline-block;
  position: absolute;
  top: 45px;
}

.clearBorder {
  background-color: rgba(0, 0, 0, 0);
  margin: 0px;
}

.decimal {
  position: relative;
  left: -7px;
}

.borderWrapper {
  margin: 0 auto;
  width: 300px;
  height: 50px;
}

.rate {
  position: relative;
  top: -50px;
}

.runtime {
  position: relative;
  top: -50px;
}

.cardTurner {
  height: 305px;
  overflow: hidden;
  margin: 0 5px 0 5px;
}
.numTitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  white-space: nowrap;
}
.numContent {
  color: #3eaf7c;
  margin-top: 5px;
  font-size: 18px;
  font-weight: 800;
  white-space: nowrap;
}
.numUnit {
  color: rgb(222, 222, 222);
  font-size: 12px;
  margin-left: 5px;
  font-weight: 400;
}
.storageWrapper {
  text-align: left;
  margin-top: 30px;
  text-align: center;
}
.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  margin: 0;
}
/deep/.el-carousel__container {
  margin: 0px 10px 0px 10px;
}
.el-carousel__item:nth-child(2n) {
}

.el-carousel__item:nth-child(2n + 1) {
}
/deep/ .el-carousel__indicator--horizontal .el-carousel__button {
  width: 10px;
  height: 10px;
  background: rgba(1, 145, 255, 0.8);
  border: 1px solid rgba(1, 145, 255, 0.1);
  border-radius: 50%;
  opacity: 0.5;
}
/deep/ .el-carousel__indicator--horizontal.is-active .el-carousel__button {
  width: 10px;
  height: 10px;
  background: rgb(1, 145, 255);
  border-radius: 50%;
  opacity: 1;
}
.small {
  background-color: blanchedalmond;
  height: 210px; /* 矩形 33 */
  box-sizing: border-box;
  background: rgba(1, 145, 255, 0.1);
  border: 1px solid rgba(1, 145, 255, 0.3);
  box-shadow: 0px 0px 10px rgba(1, 145, 255, 0.3);
  padding: 15px 10px 10px 10px;
}
.taskName {
  color: white !important;
  height: 20px;
  text-align: center;
  width: 80%;
  margin: 0 auto;
  font-weight: 800;
  font-size: 14px;
}
.taskMsg {
  margin-top: 10px;
  text-align: center;
}
.sourceCenter {
  color: #0376d2;
  position: relative;
  top: 20px;
  height: 20px;
  font-weight: 800;
}
.targetCenter {
  color: #299fd1;
  position: relative;
  top: 20px;
  height: 20px;
  font-weight: 800;
}
.sourceName {
  color: #fff;
  margin-top: 5px;
  height: 20px;
  white-space: nowrap; /* 禁止换行 */
  text-overflow: ellipsis; /* 超出省略 */
  overflow: hidden;
}
.targetName {
  color: #fff;
  margin-top: 5px;
  height: 20px;
  white-space: nowrap; /* 禁止换行 */
  text-overflow: ellipsis; /* 超出省略 */
  overflow: hidden;
}
.sourceContent {
  height: 70px;
}
.targetContent {
  height: 70px;
}
.sourceDot {
  width: 25px;
  height: 25px;
  background: #0376d2;
  /* border: 1px solid; */
  border-radius: 50%;
  margin: 0 auto;
  position: relative;
  top: 30px;
  z-index: 100;
}
.targetDot {
  width: 25px;
  height: 25px;
  background: #299fd1;
  /* border: 1px solid; */
  border-radius: 50%;
  margin: 0 auto;
  position: relative;
  top: 30px;
  z-index: 100;
}
.storageFly {
  position: absolute;
}
.success {
  color: #0376d2;
  position: absolute;
  top: -5px;
  font-size: 18px;
  width: 100%;
  font-weight: 800;
}
.contentLength {
  color: #fff;
  position: absolute;
  top: 70px;
  width: 100%;
}
/* 整个流动箭头的body */
.arrow-body {
  position: relative;
  margin: 0;
  padding: 0;
  /* 这里修改整个流动箭头的长度 */
  width: 100%;
  /* 这里修改body的高度，注意：会影响三角箭头的形状 */
  height: 28px;
  overflow: hidden;
}
/* 三角箭头 */
.arrow-body::after {
  content: "";
  position: absolute;
  /* 这里要和下面一起修改 */
  right: -12px;
  top: 0;
  width: 0;
  height: 0;
  /* 这里修改箭头的高度，这里建议是上面的24px的一半，同时要修改right：-12px的值 */
  border: 12px solid transparent;
  /* 这里可以修改箭头的横向长度，以及颜色 */
  border-left-width: 20px;
  border-left-color: #0376d2;
}
/* 流动的线条的body */
.flow-body {
  position: relative;
  margin: 0;
  padding: 0;
  height: 100%;
  width: calc(100% - 10px);
  overflow: hidden;
}
/* 线条样式 */
.flow-body::before {
  content: "";
  position: absolute;
  width: 240%;
  /* 这里修改线条的高度，同时top减去的值是高度的一半（为了保持垂直居中） */
  height: 6px;
  top: calc(50% - 3px);
  /* 这里修改线条的颜色（可以达到渐变） */
  background: repeating-linear-gradient(
    90deg,
    rgb(3, 118, 210) 0,
    rgb(3, 118, 210) 15px,
    rgba(0, 0, 0, 0) 15px,
    rgba(0, 0, 0, 0) 20px
  );
  transform: translateX(-100%);
  animation: flow linear 8s infinite;
}

/* 流动动画 */
@keyframes flow {
  from {
    transform: translateX(-50%);
  }
  to {
    transform: translateX(0%);
  }
}
.flow-arrow {
  position: absolute;
  top: 50px;
  left: 15%;
  width: 65%;
}
.count {
  color: rgb(222, 222, 222);
  font-size: 12px;
  font-weight: 100;
}
.pos {
  position: relative;
  top: -50px;
}
</style>
