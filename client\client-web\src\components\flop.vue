<template>
  <div class="cardTurner">
    <el-row>
      <el-col :span="24">
        <dv-border-box-12 :color="['#1890ff']">
          <div class="wrapper">
            <div class="title">运行卡时数(卡时)</div>
            <dv-digital-flop
              :config="config1"
              style="width: 300px; height: 70px"
            />
          </div>
        </dv-border-box-12>
        <div class="borderWrapper">
          <div class="Border"></div>
        </div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center">
      <el-col :span="12">
        <dv-border-box-12 class="rate" :color="['#1890ff']">
          <div class="wrapper">
            <div class="rateTitle">任务数(个)</div>
            <dv-digital-flop :config="config2" style="height: 80px" />
          </div>
        </dv-border-box-12>
      </el-col>
      <el-col :span="12">
        <dv-border-box-12 class="runtime" :color="['#1890ff']">
          <div class="wrapper">
            <div class="runtimeTitle">运行时长(时)</div>
            <dv-digital-flop :config="config3" style="height: 80px" />
          </div>
        </dv-border-box-12>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'flop',
  props: {
    config: {
      type: Object,
      default: () => ({
        config1: 0,
        config2: 0,
        config3: 0
      })
    }
  },
  watch: {
    config: {
      handler (newValue, oldValue) {
        this.config = newValue;
        (this.config1 = {
          number: [this.config.config1],
          toFixed: 1,
          content: '{nt}  ',
          formatter: this.formatter,
          style: {
            fontSize: 35
          },
          animationFrame: 200,
          animationCurve: 'easeOutCubic'
        }),
        (this.config2 = {
          number: [this.config.config2],
          content: ' {nt} ',
          style: {
            fontSize: 35
          },
          animationFrame: 200,
          animationCurve: 'easeOutCubic'
        }),
        (this.config3 = {
          number: [this.config.config3],
          toFixed: 1,
          content: ' {nt} ',
          style: {
            fontSize: 35
          },
          animationFrame: 200,
          animationCurve: 'easeOutCubic'
        })
      },
      deep: true
    }
  },
  data () {
    return {
      config1: {
        number: [],
        toFixed: 1,
        content: '{nt}',
        formatter: this.formatter,
        style: {
          fontSize: 35
        }
      },
      config2: {
        number: [],
        toFixed: 1,
        content: '{nt}',
        textAlign: 'center',
        style: {
          fontSize: 35
        }
      },
      config3: {
        number: [],
        toFixed: 1,
        content: '{nt}',
        textAlign: 'center',
        style: {
          fontSize: 35
        }
      }
    }
  },
  created () {},
  methods: {
    formatter (number) {
      let data = ''
      for (var i = 0; i < number.toString().length; i++) {
        data = data + '  ' + number.toString().charAt(i)
      }
      return data
    }
  }
}
</script>
<style scoped>
.wrapper {
  padding: 10px;
  position: relative;
  color: red;
}

.dv-digital-flop {
  margin: 0 auto;
}

.title {
  color: rgb(24, 144, 255);
  font-size: 18px;
  font-weight: 400;
  margin: 5px 0 5px 50px;
}

.rateTitle {
  color: rgb(24, 144, 255);
  font-size: 14px;
  font-weight: 400;
  margin: 5px 0 0px 50px;
}

.runtimeTitle {
  color: rgb(24, 144, 255);
  font-size: 14px;
  font-weight: 400;
  margin: 5px 0 0px 50px;
}

.borderBox {
  display: inline-block;
  width: 34px;
  height: 60px;
  background-color: rgba(24, 144, 255, 0.4);
  margin: 0px 5px 0px 0px;
}

.Border {
  display: inline-block;
  position: absolute;
  top: 45px;
}

.clearBorder {
  background-color: rgba(0, 0, 0, 0);
  margin: 0px;
}

.decimal {
  position: relative;
  left: -7px;
}

.borderWrapper {
  margin: 0 auto;
  width: 300px;
  height: 50px;
}

.rate {
  position: relative;
  top: -50px;
}

.runtime {
  position: relative;
  top: -50px;
}

.cardTurner {
  height: 245px;
  overflow: hidden;
}
</style>
