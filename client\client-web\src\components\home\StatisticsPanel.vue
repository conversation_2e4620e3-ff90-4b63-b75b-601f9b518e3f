<template>
  <div class="statistics-panel">
    <!-- 标签页切换 -->
    <div class="tab-section">
      <el-row justify="center" type="flex">
        <el-col>
          <div class="tab-container">
            <el-row
              class="row-bg tab-row"
              justify="space-around"
              type="flex"
              :gutter="16"
            >
              <el-col :span="8" class="bg2 tab-item" :class="{'active-tab': activeTab === 1}">
                <el-link
                  :class="{ button2: true, check3: activeTab === 1 }"
                  :disabled="disabled"
                  :underline="false"
                  @click="switchTab(1)"
                >提交任务量
                </el-link>
              </el-col>
              <el-col :span="8" class="bg2 tab-item" :class="{'active-tab': activeTab === 2}">
                <el-link
                  :class="{ button2: true, check3: activeTab === 2 }"
                  :disabled="disabled"
                  :underline="false"
                  @click="switchTab(2)"
                >任务平均等待时长
                </el-link>
              </el-col>
              <el-col :span="8" class="bg2 tab-item" :class="{'active-tab': activeTab === 3}">
                <el-link
                  :class="{ button2: true, check3: activeTab === 3 }"
                  :disabled="disabled"
                  :underline="false"
                  @click="switchTab(3)"
                >资源利用率对比
                </el-link>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div
        ref="statisticsChart"
        class="statistics-chart"
        style="width: 100%; min-height: 700px"
      ></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'StatisticsPanel',

  props: {
    // 任务数据
    taskData: {
      type: Object,
      default: () => ({
        xData: [],
        used: [],
        used2: []
      })
    },

    // 当前帧数
    currentFrame: {
      type: Number,
      default: 0
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 任务类型名称映射
    taskTypeNames: {
      type: Object,
      default: () => ({
        1: '提交任务量',
        2: '任务平均等待时长',
        3: '资源利用率对比'
      })
    }
  },

  data () {
    return {
      activeTab: 1,
      statisticsChart: null
    }
  },

  computed: {
    // 当前标签页名称
    currentTabName () {
      return this.taskTypeNames[this.activeTab] || '提交任务量'
    }
  },

  watch: {
    // 监听任务数据变化
    taskData: {
      handler (newData) {
        this.updateChart(newData)
      },
      deep: true,
      immediate: true
    },

    // 监听当前帧变化
    currentFrame (newFrame) {
      this.updateChartFrame(newFrame)
    }
  },

  mounted () {
    this.initChart()
  },

  beforeDestroy () {
    if (this.statisticsChart) {
      this.statisticsChart.dispose()
      this.statisticsChart = null
    }
  },

  methods: {
    /**
     * 初始化图表
     */
    initChart () {
      if (!this.$refs.statisticsChart) return

      this.statisticsChart = echarts.init(this.$refs.statisticsChart)
      this.updateChart(this.taskData)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    /**
     * 切换标签页
     */
    switchTab (tabIndex) {
      if (this.disabled) return

      this.activeTab = tabIndex
      this.$emit('tab-change', tabIndex)

      // 更新图表
      this.updateChart(this.taskData)
    },

    /**
     * 更新图表
     */
    updateChart (data) {
      if (!this.statisticsChart || !data) return

      const option = this.createChartOption(data)
      this.statisticsChart.setOption(option, true)
    },

    /**
     * 创建图表配置
     */
    createChartOption (data) {
      const hasData = data.xData && data.xData.length > 0 && data.used && data.used.length > 0

      if (!hasData) {
        return this.createEmptyChartOption()
      }

      // 获取当前帧的数据
      const currentData = data.used[this.currentFrame] || []
      const compareData = data.used2 && data.used2[this.currentFrame] ? data.used2[this.currentFrame] : []

      return {
        title: {
          text: this.currentTabName,
          textStyle: {
            color: '#fff',
            fontSize: 16
          },
          left: 'center',
          top: 20
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#32c5ff',
          textStyle: {
            color: '#fff'
          },
          formatter: (params) => {
            let result = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`
            params.forEach(param => {
              result += `
                <div>
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
                  ${param.seriesName}: ${param.value}
                </div>
              `
            })
            return result
          }
        },
        legend: {
          data: compareData.length > 0 ? ['主任务', '对比任务'] : ['主任务'],
          textStyle: {
            color: '#fff'
          },
          top: 50
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.xData || [],
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            interval: 0,
            rotate: 45
          },
          axisLine: {
            lineStyle: {
              color: '#32c5ff'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            formatter: (value) => {
              if (this.activeTab === 2) {
                // 等待时长格式化
                return value.toFixed(2) + 'h'
              } else if (this.activeTab === 3) {
                // 利用率格式化
                return (value * 100).toFixed(1) + '%'
              } else {
                // 任务量格式化
                return value
              }
            }
          },
          axisLine: {
            lineStyle: {
              color: '#32c5ff'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(50, 197, 255, 0.2)'
            }
          }
        },
        series: this.createSeriesData(currentData, compareData)
      }
    },

    /**
     * 创建系列数据
     */
    createSeriesData (currentData, compareData) {
      const series = [
        {
          name: '主任务',
          type: 'bar',
          data: currentData,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#32c5ff' },
                { offset: 1, color: '#1890ff' }
              ]
            }
          },
          emphasis: {
            itemStyle: {
              color: '#40d9ff'
            }
          }
        }
      ]

      // 如果有对比数据，添加对比系列
      if (compareData.length > 0) {
        series.push({
          name: '对比任务',
          type: 'bar',
          data: compareData,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#ff6b6b' },
                { offset: 1, color: '#ee5a52' }
              ]
            }
          },
          emphasis: {
            itemStyle: {
              color: '#ff8a80'
            }
          }
        })
      }

      return series
    },

    /**
     * 创建空图表配置
     */
    createEmptyChartOption () {
      return {
        title: {
          text: '暂无数据',
          textStyle: {
            color: '#fff',
            fontSize: 16
          },
          left: 'center',
          top: 'center'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            color: '#fff'
          },
          axisLine: {
            lineStyle: {
              color: '#32c5ff'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff'
          },
          axisLine: {
            lineStyle: {
              color: '#32c5ff'
            }
          }
        },
        series: []
      }
    },

    /**
     * 更新图表当前帧
     */
    updateChartFrame (frame) {
      if (!this.statisticsChart || !this.taskData.used) return

      this.updateChart(this.taskData)
    },

    /**
     * 处理窗口大小变化
     */
    handleResize () {
      if (this.statisticsChart) {
        this.statisticsChart.resize()
      }
    }
  }
}
</script>

<style scoped>
.statistics-panel {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  height: 100%;
}

.tab-section {
  margin-bottom: 20px;
}

.tab-container {
  width: 100%;
}

.tab-row {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 5px;
}

.tab-item {
  text-align: center;
  padding: 8px 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tab-item:hover {
  background: rgba(50, 197, 255, 0.2);
}

.active-tab {
  background: rgba(50, 197, 255, 0.3) !important;
}

.button2 {
  color: #fff;
  font-size: 12px;
  text-decoration: none;
  display: block;
  width: 100%;
  padding: 4px 0;
}

.check3 {
  color: #32c5ff !important;
  font-weight: bold;
}

.chart-section {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 10px;
}

.statistics-chart {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .tab-item {
    padding: 6px 2px;
  }

  .button2 {
    font-size: 11px;
  }
}
</style>
