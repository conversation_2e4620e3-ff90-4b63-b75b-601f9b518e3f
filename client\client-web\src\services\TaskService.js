import { jobDetail } from '@/api/screenService.js'

/**
 * 任务服务类 - 封装任务相关的API调用和数据处理
 */
export class TaskService {
  /**
   * 获取任务详情
   * @param {number} taskId - 任务ID
   * @param {number} intervalHours - 时间间隔（小时）
   * @returns {Promise} 任务详情数据
   */
  static async getTaskDetail (taskId, intervalHours) {
    if (taskId === 0) return null

    const params = {
      id: taskId,
      resolution_n_hours: intervalHours
    }

    try {
      return await jobDetail(params)
    } catch (error) {
      console.error('获取任务详情失败:', error)
      throw error
    }
  }

  /**
   * 批量获取任务数据（主任务和对比任务）
   * @param {number} mainTaskId - 主任务ID
   * @param {number} compareTaskId - 对比任务ID
   * @param {number} intervalHours - 时间间隔
   * @returns {Promise<{mainTask, compareTask}>} 任务数据
   */
  static async getMultipleTaskData (mainTaskId, compareTaskId, intervalHours) {
    const promises = []

    if (mainTaskId !== 0) {
      promises.push(this.getTaskDetail(mainTaskId, intervalHours))
    } else {
      promises.push(Promise.resolve(null))
    }

    if (compareTaskId !== 0) {
      promises.push(this.getTaskDetail(compareTaskId, intervalHours))
    } else {
      promises.push(Promise.resolve(null))
    }

    const [mainTask, compareTask] = await Promise.all(promises)

    return { mainTask, compareTask }
  }

  /**
   * 提取任务基本信息
   * @param {Object} taskResponse - API响应数据
   * @returns {Object} 基本任务信息
   */
  static extractBasicInfo (taskResponse) {
    if (!taskResponse) return null

    return {
      id: taskResponse.ID,
      nJobs: taskResponse.NJobs,
      snapshotTime: taskResponse.SnapshotTime,
      completedFlag: taskResponse.CompletedFlag,
      strategy: taskResponse.Strategy,
      nCenters: taskResponse.NCenters,
      nPops: taskResponse.NPops
    }
  }

  /**
   * 提取时间序列数据
   * @param {Array} centerInfoList - 中心信息列表
   * @returns {Array} 时间数据数组
   */
  static extractTimeData (centerInfoList) {
    if (!centerInfoList || !centerInfoList[0] || !centerInfoList[0].SnapshotInfoToWebList) {
      return []
    }

    return centerInfoList[0].SnapshotInfoToWebList.map(snapshot =>
      Math.floor(snapshot.Time * 1000 / (1000 * 60 * 60))
    )
  }

  /**
   * 提取统计数据
   * @param {Array} centerInfoList - 中心信息列表
   * @returns {Object} 包含各种统计数据的对象
   */
  static extractStatisticsData (centerInfoList) {
    if (!centerInfoList || centerInfoList.length === 0) {
      return {
        timeData: [],
        submitJobData: [],
        historySubmitJobData: [],
        historyCompleteJobData: [],
        cityNames: [],
        frameData: {
          submitJob: [],
          pendingTime: [],
          resourceUsage: []
        }
      }
    }

    const totalFrames = centerInfoList[0].SnapshotInfoToWebList.length
    const cityNames = centerInfoList.map(item => item.InfoName)

    const timeData = this.extractTimeData(centerInfoList)
    const submitJobData = []
    const historySubmitJobData = []
    const historyCompleteJobData = []

    // 每帧的分中心数据
    const frameData = {
      submitJob: [],
      pendingTime: [],
      resourceUsage: []
    }

    // 处理每一帧的数据
    for (let frame = 0; frame < totalFrames; frame++) {
      let totalHistorySubmit = 0
      let totalHistoryComplete = 0
      let totalSubmit = 0

      frameData.submitJob[frame] = []
      frameData.pendingTime[frame] = []
      frameData.resourceUsage[frame] = []

      centerInfoList.forEach((centerInfo, centerIndex) => {
        const snapshot = centerInfo.SnapshotInfoToWebList[frame]
        if (snapshot) {
          totalHistorySubmit += snapshot.HistorySubmitJob
          totalHistoryComplete += snapshot.HistoryCompleteJob
          totalSubmit += snapshot.SubmitJob

          frameData.submitJob[frame][centerIndex] = snapshot.HistorySubmitJob
          frameData.pendingTime[frame][centerIndex] = snapshot.AveragePendingTime
          frameData.resourceUsage[frame][centerIndex] = snapshot.AverageMachineUse
        }
      })

      historySubmitJobData[frame] = totalHistorySubmit
      historyCompleteJobData[frame] = totalHistoryComplete
      submitJobData[frame] = totalSubmit
    }

    return {
      timeData,
      submitJobData,
      historySubmitJobData,
      historyCompleteJobData,
      cityNames,
      frameData
    }
  }

  /**
   * 处理对比任务数据
   * @param {Object} compareTaskResponse - 对比任务响应数据
   * @param {number} mainTotalFrames - 主任务的总帧数
   * @returns {Object} 处理后的对比数据
   */
  static processCompareTaskData (compareTaskResponse, mainTotalFrames) {
    if (!compareTaskResponse || !compareTaskResponse.CenterInfoToWebList) {
      return null
    }

    const centerInfoList = compareTaskResponse.CenterInfoToWebList
    const compareFrames = centerInfoList[0].SnapshotInfoToWebList.length
    const cityNames = centerInfoList.map(item => item.InfoName)

    const compareFrameData = {
      submitJob: [],
      pendingTime: [],
      resourceUsage: []
    }

    // 确保对比数据的帧数与主任务一致
    const targetFrames = Math.min(mainTotalFrames, compareFrames)

    for (let frame = 0; frame < targetFrames; frame++) {
      compareFrameData.submitJob[frame] = []
      compareFrameData.pendingTime[frame] = []
      compareFrameData.resourceUsage[frame] = []

      centerInfoList.forEach((centerInfo, centerIndex) => {
        const snapshot = centerInfo.SnapshotInfoToWebList[frame]
        if (snapshot) {
          compareFrameData.submitJob[frame][centerIndex] = snapshot.HistorySubmitJob
          compareFrameData.pendingTime[frame][centerIndex] = snapshot.AveragePendingTime
          compareFrameData.resourceUsage[frame][centerIndex] = snapshot.AverageMachineUse
        } else {
          // 如果没有对应帧的数据，使用最后一帧的数据
          const lastFrame = centerInfo.SnapshotInfoToWebList[compareFrames - 1]
          compareFrameData.submitJob[frame][centerIndex] = lastFrame.HistorySubmitJob
          compareFrameData.pendingTime[frame][centerIndex] = lastFrame.AveragePendingTime
          compareFrameData.resourceUsage[frame][centerIndex] = lastFrame.AverageMachineUse
        }
      })
    }

    return {
      cityNames,
      frameData: compareFrameData,
      totalFrames: targetFrames
    }
  }
}
