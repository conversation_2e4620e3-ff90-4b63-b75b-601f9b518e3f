---
description: 
globs: 
alwaysApply: true
---
# 文档集成工具使用规范

## 文档集成工具定义

- 文档集成工具指所有以 `mcp-doc` 为前缀的MCP工具，用于查询最新的官方API文档，解决代码与知识库版本不一致的问题。

## 核心使用原则

- 何时使用 (When to Use):
  - 必须: API用法不熟、框架版本差异大、怀疑代码已过时。
  - 推荐: 实现复杂功能、集成新库、代码迁移时，可用于参考最佳实践。
  - 避免: 基础语法、项目内部API、简单的逻辑。

- 如何使用 (How to Use):
  - 精准: 优先使用最匹配的工具。
  - 高效: 一次性查询所有相关API，避免零散调用。
  - 验证: 确认文档版本与项目依赖匹配。

- 注意事项 (Keep in Mind):
  - 优先参考项目内已有代码和配置。
  - 若查询失败 (如网络问题、文档不存在)，应调用mcp-feedback-enhanced工具以获取用户反馈。
