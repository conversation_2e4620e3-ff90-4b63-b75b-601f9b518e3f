<template>
  <div class="mt">
    <el-row>
      <el-col :span="24">
        <el-row>
          <el-col :span="8">
            <el-row type="flex" justify="center">
              <el-col :span="3">
                <el-col :span="4">
                  <div class="circle2">
                    <div class="dv-loading">
                      <svg width="50px" height="50px">
                        <circle
                          cx="25"
                          cy="25"
                          r="20"
                          fill="transparent"
                          stroke-width="2"
                          stroke-dasharray="31.415, 31.415"
                          stroke="#1890ff"
                          stroke-linecap="round"
                        >
                          <animateTransform
                            attributeName="transform"
                            type="rotate"
                            values="0, 25 25;360, 25 25"
                            dur="2.5s"
                            repeatCount="indefinite"
                          ></animateTransform>
                          <animate
                            attributeName="stroke"
                            values="#1890ff;#1890ff;#1890ff"
                            dur="3s"
                            repeatCount="indefinite"
                          ></animate>
                        </circle>
                      </svg>
                      <div class="loading-tip1"></div>
                    </div>
                  </div>
                </el-col>
              </el-col>
              <el-col :span="20">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="24">
                        <div>已接入/待接入算力中心</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="6">
                      <div class="num">
                        {{
                          data.centerTotal.accessed +
                          "/" +
                          data.centerTotal.unaccess
                        }}
                      </div>
                    </el-col>
                    <el-col :span="3">
                      <div class="unit">个</div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="8">
            <el-row type="flex" justify="center">
              <el-col :span="4">
                <div class="circle2">
                  <div class="dv-loading">
                    <svg width="50px" height="50px">
                      <circle
                        cx="25"
                        cy="25"
                        r="20"
                        fill="transparent"
                        stroke-width="2"
                        stroke-dasharray="31.415, 31.415"
                        stroke="#1890ff"
                        stroke-linecap="round"
                      >
                        <animateTransform
                          attributeName="transform"
                          type="rotate"
                          values="0, 25 25;360, 25 25"
                          dur="2.5s"
                          repeatCount="indefinite"
                        ></animateTransform>
                        <animate
                          attributeName="stroke"
                          values="#1890ff;#1890ff;#1890ff"
                          dur="3s"
                          repeatCount="indefinite"
                        ></animate>
                      </circle>
                    </svg>
                    <div class="loading-tip2"></div>
                  </div>
                </div>
              </el-col>
              <el-col :span="20">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="20">
                        <div>已接入/待接入算力</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="11">
                      <div class="num">
                        <div class="num">
                          {{
                            data.computingPower1 + "/" + data.computingPower2
                          }}
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="10">
                      <div class="unitBg">POps@FP16</div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="8">
            <el-row type="flex" justify="center">
              <el-col :span="4">
                <div class="circle3">
                  <div class="dv-loading">
                    <svg width="50px" height="50px">
                      <circle
                        cx="25"
                        cy="25"
                        r="20"
                        fill="transparent"
                        stroke-width="2"
                        stroke-dasharray="31.415, 31.415"
                        stroke="#1890ff"
                        stroke-linecap="round"
                      >
                        <animateTransform
                          attributeName="transform"
                          type="rotate"
                          values="0, 25 25;360, 25 25"
                          dur="2.5s"
                          repeatCount="indefinite"
                        ></animateTransform>
                        <animate
                          attributeName="stroke"
                          values="#1890ff;#1890ff;#1890ff"
                          dur="3s"
                          repeatCount="indefinite"
                        ></animate>
                      </circle>
                    </svg>
                    <div class="loading-tip3"></div>
                  </div>
                </div>
              </el-col>
              <el-col :span="18">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="20">
                        <div>已接入/待接入存储</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="9">
                      <div class="num">
                        {{ data.storageScale1 + "/" + data.storageScale2 }}
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="unit">PB</div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'total',
  data () {
    return {}
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    statusData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    statusData: {
      handler (newValue, oldValue) {
        this.statusData = newValue
      }
    },
    data: {
      handler (newValue, oldValue) {
        this.data = newValue
      }
    }
  },
  mounted () {},
  methods: {}
}
</script>
<style scoped>
.icon {
  background-image: url(../static/screen1/cloud.png);
  width: 80px;
  height: 80px;
  background-size: 100% 100%;
  background-position-y: 10px;
  background-repeat: no-repeat;
}

.title {
  font-family: "思源黑体 CN-Regular";
  font-size: 20px;
  color: rgb(211, 237, 255);
  letter-spacing: 1px;
  font-weight: normal;
  font-style: normal;
  transform: translate(0px, 0px);
  word-break: keep-all;
}

.content {
  padding-top: 3px;
  padding-left: 10px;
}

.num {
  text-align: left;
  color: rgb(24, 144, 255);
  font-size: 20px;
  font-family: DINPro-Medium;
  font-weight: normal;
  text-shadow: rgb(19 128 255) 0px 0px 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.unit {
  display: inline-flex;
  align-items: center;
  color: rgb(211, 237, 255);
  font-size: 20px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  padding-left: 2px;
}

.unitBg {
  color: rgb(211, 237, 255);
  font-size: 20px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  /* padding-left: 10px; */
}

.arrow {
  background-size: 100% 150%;
  background-image: url("../static/screen1/arrow.png");
  width: 50px;
  height: 30px;
  background-position: 0px -8px;
  background-repeat: no-repeat;
}

.mt {
  margin-top: 120px;
  position: relative;
  left: 70px;
}
.loading-tip1 {
  background-image: url("../assets/icon/riLine-map-pin-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip2 {
  background-image: url("../assets/icon/riLine-camera-lens-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip3 {
  background-image: url("../assets/icon/riLine-database-2-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.circle1 {
  border-radius: 50px;
  border: 1px dashed #1890ff;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 18px;
}
.circle2 {
  border-radius: 50px;
  border: 1px dashed #1890ff;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 3px;
}
.circle3 {
  border-radius: 50px;
  border: 1px dashed #1890ff;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 18px;
}
.dv-loading {
  width: 100%;
  height: 100%;
}
</style>
