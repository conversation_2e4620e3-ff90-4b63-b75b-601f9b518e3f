<template>
  <div class="background" @click="hiddenNavbar()">
    <el-container>
      <el-header>
        <el-row type="flex" justify="center">
          <el-col :span="12">
            <div class="title" v-if="show1">
              中国算力网(C²NET)<span class="linear-gradient-text"
                >指挥平台</span
              >
            </div>
            <div class="title" v-if="show2">
              <span class="linear-gradient-text">粤港澳大湾区区域调度中心</span>
            </div>
            <div class="title" v-if="show3">
              中国算力网<span class="linear-gradient-text"
                >韶关数据中心分布</span
              >
            </div>
          </el-col>
        </el-row>
      </el-header>
      <dropdown class="dropdown" :type="'dispatchCenter'"></dropdown>
      <div class="main" v-if="show1 || show2">
        <el-row type="flex" justify="space-around">
          <el-col :span="5">
            <div class="showMt" v-if="show1">
              <dv-border-box-10>
                <div>
                  <div class="borderColor">累计任务情况</div>
                  <Flop :config="accrueData"></Flop>
                </div>
              </dv-border-box-10>
            </div>
            <div class="showMt" v-if="show2">
              <div>
                <div></div>
                <div class="borderColor">机时使用趋势</div>
                <TEND id="TEND" :config="tendConfig"></TEND>
              </div>
            </div>
            <div class="showMt" v-if="show1">
              <dv-border-box-10>
                <div>
                  <div class="borderColor">运行任务量及等待任务量</div>
                  <TASK id="task"></TASK>
                </div>
              </dv-border-box-10>
            </div>
            <div class="showMt" v-if="show2">
              <div>
                <div></div>
                <div class="borderColor">任务分布情况</div>
                <Pie></Pie>
              </div>
            </div>
            <div class="showMt" v-if="show1">
              <div>
                <div class="borderColor">存储调度任务任务监控</div>
                <div class="borderLinear">
                  <Monitor :config="accrueData"></Monitor>
                </div>
              </div>
            </div>
            <div class="showMt" v-if="show2">
              <div>
                <div></div>
                <div class="borderColor mb-1">任务监控</div>
                <div class="borderLinear"></div>
                <Table :data="tableData"></Table>
              </div>
            </div>
          </el-col>
          <el-col :span="14">
            <div class="mt-2">
              <el-row type="flex" justify="center">
                <el-col :span="22">
                  <div style="padding: 5px 0 5px 60px">
                    <Total
                      :data="totalNum"
                      :statusData="statusData"
                      v-if="show1"
                    ></Total>
                    <Total2
                      :data="totalNum"
                      :statusData="statusData"
                      v-if="show2"
                    ></Total2>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <div v-if="show1">
                    <chinaMap
                      :mapData="mapData"
                      :show="type"
                      @showTip="showTip"
                      @hiddenTip="hiddenTip"
                      :desMap="desMap"
                      v-if="show"
                    ></chinaMap>
                    <div class="statusWrapper1">
                      <div class="wrapper">
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type1,
                            title2: true,
                          }"
                          @click="type = 1"
                        >
                          <span class="img1 icon"></span
                          ><span class="text">智算中心</span>
                          <span class="text">{{ intelligenceTotal }}</span>
                        </div>
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type2,
                            title2: true,
                          }"
                          @click="type = 2"
                        >
                          <span class="img2 icon"></span
                          ><span class="text">超算中心</span>
                          <span class="text">{{ superTotal }}</span>
                        </div>
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type3,
                            title2: true,
                          }"
                          @click="type = 3"
                        >
                          <span class="img3 icon"></span
                          ><span class="text">东数西算</span>
                          <span class="text">{{ eAdnwTotal }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img4 icon2"></span>
                          <span class="text">已接入</span>
                          <span class="text">{{ statusData.connected }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img5 icon2"></span>
                          <span class="text">接入中</span>
                          <span class="text">{{ statusData.accessing }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img6 icon2"></span>
                          <span class="text">待接入</span>
                          <span class="text">{{ statusData.pending }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="statusWrapper2">
                      <div data-v-7a8a63e0="" class="wrapper">
                        <div data-v-7a8a63e0="" class="arrow1">
                          <span data-v-7a8a63e0="" class="ml-2"
                            >10TB全光网络互联</span
                          >
                        </div>
                        <div data-v-7a8a63e0="" class="arrow2">
                          <span data-v-7a8a63e0="" class="ml-2"
                            >SD-WAN互联</span
                          >
                        </div>
                        <div data-v-7a8a63e0="" class="arrow3">
                          <span data-v-7a8a63e0="" class="ml-2">MPLS互联</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="show2">
                    <bayArea></bayArea>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="showMt" v-if="show1">
              <dv-border-box-10>
                <div>
                  <div class="borderColor">算力使用趋势</div>
                  <div class="borderLinear"></div>
                  <TEND id="TEND" :config="tendConfig"></TEND>
                </div>
              </dv-border-box-10>
            </div>
            <div class="showMt" v-if="show2">
              <div>
                <div></div>
                <div class="borderColor">算力节点任务分布</div>
                <div class="borderLinear"></div>
                <TASK id="task"></TASK>
              </div>
            </div>
            <div class="showMt" v-if="show1">
              <dv-border-box-10>
                <div>
                  <div class="borderColor">各中心算力使用情况</div>
                  <div class="borderLinear"></div>
                  <STATUS3
                    id="Status"
                    :data="centerData"
                    :config="statusConfig"
                  ></STATUS3>
                </div>
              </dv-border-box-10>
            </div>
            <div class="showMt" v-if="show2">
              <div>
                <div></div>
                <div class="borderColor">算力节点累计机时使用情况</div>
                <div class="borderLinear"></div>
                <STATUS2
                  id="Status"
                  :data="centerData"
                  :config="statusConfig"
                ></STATUS2>
              </div>
            </div>
            <div class="showMt" v-if="show1">
              <dv-border-box-10>
                <div>
                  <div class="borderColor">算力接入情况</div>
                  <div class="borderLinear"></div>
                  <INSERT
                    id="Imsert"
                    :data="connectedData"
                    :config="connectedConfig"
                  >
                  </INSERT>
                </div>
              </dv-border-box-10>
            </div>
            <div class="showMt" v-if="show2">
              <div>
                <div></div>
                <div class="borderColor">算力存储使用情况</div>
                <div class="borderLinear mb-1"></div>
                <Storage></Storage>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-if="show3" class="main">
        <shaoguan></shaoguan>
      </div>
      <el-row type="flex" class="row-bg ckButton" justify="space-around">
        <el-col :span="20">
          <div>
            <el-row type="flex" justify="center" :gutter="30">
              <el-col :span="3">
                <div
                  :class="{ buttonStyle: true, check: show1 }"
                  @click="changeNetwork('type1')"
                >
                  算力网全景
                </div>
              </el-col>
              <el-col :span="3">
                <div
                  :class="{ buttonStyle: true, check: show2 }"
                  @click="changeNetwork('type2')"
                >
                  大湾区枢纽
                </div>
              </el-col>
              <el-col :span="3">
                <div
                  :class="{ buttonStyle: true, check: show3 }"
                  @click="changeNetwork('type3')"
                >
                  数据中心
                </div>
              </el-col>
            </el-row>
          </div></el-col
        >
      </el-row>
    </el-container>
    <el-footer class="footer">Footer</el-footer>
  </div>
</template>
<script>
import TEND from '@/barComponents/lineChart'
import STATUS from '@/barComponents/Histogram'
import STATUS2 from '@/barComponents/Histogram2'
import STATUS3 from '@/barComponents/Histogram3'
import INSERT from '@/barComponents/HisAndLine'
import TASK from '@/barComponents/Histogram'
import Table from '@/barComponents/Table'
import Total from '@/barComponents/Total'
import Total2 from '@/barComponents/Total2'
import chinaMap from '@/barComponents/chinaMap'
import bayArea from '@/barComponents/bayArea'
import shaoguan from '@/barComponents/shaoguan'
import Pie from '@/barComponents/Pie'
import Storage from '@/barComponents/Storage'
import flop from '@/barComponents/flop'
import monitor from '@/barComponents/schedulingMon'
import { orderDown } from '@/barUtils/index'
import { getConfig } from '@/utils/config'
import dropdown from '@/components/Dropdown'
import moment from 'moment'
import {
  getAicenter,
  getTrainJob,
  getConnected,
  getDynamic
} from '@/api/screenService.js'
import { getAccrueCenter } from '@/barApi/screenService.js'
export default {
  components: {
    TEND: TEND,
    STATUS: STATUS,
    STATUS2: STATUS2,
    STATUS3: STATUS3,
    INSERT: INSERT,
    TASK: TASK,
    Table: Table,
    Total: Total,
    Total2: Total2,
    chinaMap: chinaMap,
    Flop: flop,
    bayArea: bayArea,
    shaoguan: shaoguan,
    Pie: Pie,
    Storage: Storage,
    Monitor: monitor,
    dropdown: dropdown
  },
  data () {
    return {
      npuData: { used: [], unused: [], xData: [] },
      gpuData: { used: [], unused: [], xData: [] },
      cpuData: { used: [], unused: [], xData: [] },
      statusConfig: { unit: '单位:  卡时', status: ['使用量'] },
      tendConfig: { unit: '单位:  卡时', status: ['使用量'] },
      connectedConfig: { unit: '单位:  POps@FP16', status: ['接入数'] },
      mapData: [],
      statusData: { connected: 0, accessing: 0, pending: 0 },
      crossDomain: [
        { value: 64, name: '已完成' },
        { value: 30, name: '等待中' },
        { value: 58, name: '处理中' }
      ],
      crossTitle: '',
      collaborate: [
        { value: 64, name: '已完成' },
        { value: 30, name: '等待中' },
        { value: 58, name: '处理中' }
      ],
      coollTitle: '',
      totalNum: {
        centerTotal: 0,
        computingPower1: 0,
        storageScale1: 0,
        computingPower2: 0,
        storageScale2: 0
      },
      tableData: [],
      pageIndex: 1,
      pageSize: 100,
      timer: null,
      timer2: null,
      timer3: null,
      itemNow: [],
      item1: [], // item1智算
      item2: [], // item2超算
      item3: [], // item3集群东数西算
      item4: [], // 全部
      show: false,
      show1: false,
      show2: true,
      show3: false,
      type1: false,
      type2: true,
      type3: false,
      type: 4,
      networkType: 'type1',
      accrueData: {
        config1: undefined,
        config2: undefined,
        config3: undefined
      },
      centerData: { used: [], xData: [] },
      connectedData: { used: [], xData: [] },
      total: 0,
      intelligenceTotal: 0,
      superTotal: 0,
      eAdnwTotal: 0,
      tipData: {},
      desMap: null,
      destination: []
    }
  },
  created () {
    this.getTaskTotal()
    this.getAicenter()
    this.getTrainJob()
    this.getAccrueCenter()
    this.getConnected()
  },
  mounted () {
    this.getNewData()
  },
  watch: {
    type: {
      handler (newValue, oldValue) {
        this.getAicenter()
      }
    }
  },
  methods: {
    changeNetwork (value) {
      this.networkType = value
      this.getTaskTotal()
      this.getAicenter()
      this.getTrainJob()
      this.getAccrueCenter()
      this.getConnected()
      // this.timer = setInterval(() => {
      //   this.getTaskTotal();
      // }, 5000);
      if (this.networkType == 'type1') {
        // window.location.href = window.location.href.replace(
        //   "dispatchCenter",
        //   "c2net"
        // );
        // this.type = 4;
        // this.show1 = true;
        // this.show2 = false;
        // this.show3 = false;
      } else if (this.networkType == 'type2') {
        this.show1 = false
        this.show2 = true
        this.show3 = false
      } else {
        this.show1 = false
        this.show2 = false
        this.show3 = true
        // this.getAicenter();
      }
    },
    getAicenter () {
      getAicenter({ sortBy: 'weight', orderBy: 'asc' }).then((res) => {
        this.statusData = { connected: 0, accessing: 0, pending: 0 }
        this.mapData = []
        this.totalNum.centerTotal = res.aiCenterInfos.length
        let computingPower1 = 0
        let storageScale1 = 0
        let computingPower2 = 0
        let storageScale2 = 0
        this.item1 = []
        this.item2 = []
        this.item3 = []
        this.item4 = []
        res.aiCenterInfos.forEach((item) => {
          if (item.centerType == '东数西算') {
            this.item3.push(item)
          } else if (item.centerType == '超算中心') {
            this.item2.push(item)
          } else {
            this.item1.push(item)
          }
        })
        this.intelligenceTotal = this.item1.length
        this.superTotal = this.item2.length
        this.eAdnwTotal = this.item3.length
        switch (this.type) {
          case 1:
            this.type1 = true
            this.type2 = false
            this.type3 = false
            this.type4 = false
            this.itemNow = this.item1
            break
          case 2:
            this.type2 = true
            this.type1 = false
            this.type3 = false
            this.type4 = false
            this.itemNow = this.item2
            break
          case 3:
            this.type3 = true
            this.type2 = false
            this.type1 = false
            this.type4 = false
            this.itemNow = this.item3
            break
          default:
            this.type1 = false
            this.type2 = false
            this.type3 = false
            this.type4 = true
            this.item4 = this.item1.concat(this.item2).concat(this.item3)
            this.itemNow = this.item4
        }
        this.itemNow.forEach((item) => {
          this.mapData.push({
            coordinateY: item.coordinateY,
            coordinateX: item.coordinateX,
            name: item.name,
            province: item.province,
            computeScale: item.computeScale,
            accessTime: item.accessTime,
            cardRunTime: item.cardRunTime,
            jobCount: item.jobCount,
            connectionState: item.connectionState,
            centerType: item.centerType
          })
          // 状态数
          if (item.connectionState == 3) {
            this.statusData.connected++
          } else if (item.connectionState == 1) {
            this.statusData.accessing++
          } else if (item.connectionState == 2) {
            this.statusData.pending++
          }
        })
        res.aiCenterInfos.forEach((item) => {
          if (item.connectionState == 3) {
            (computingPower1 = computingPower1 + item.computeScale),
            (storageScale1 = storageScale1 + item.storageScale)
          }
          if (item.connectionState != 3) {
            (computingPower2 = computingPower2 + item.computeScale),
            (storageScale2 = storageScale2 + item.storageScale)
          }
          // 任务
          if (item.connectionState == 3) {
            // if (item.trainJob != null && item.name) {
            //   this.taskData.unused.push(item.trainJob.pendingNum),
            //     this.taskData.used.push(item.trainJob.runningNum);
            //   this.taskData.xData.push(item.name);
            // }
          }
        })
        this.total =
          this.statusData.connected +
          this.statusData.accessing +
          this.statusData.pending
        this.totalNum.computingPower1 = computingPower1
        this.totalNum.storageScale1 = storageScale1
        this.totalNum.computingPower2 = computingPower2
        this.totalNum.storageScale2 = storageScale2
      })
    },
    getTrainJob () {
      getTrainJob({ pageIndex: this.pageIndex, pageSize: this.pageSize }).then(
        (res) => {
          this.tableData = []
          res.otJobs ? res.otJobs : []
          res.otJobs.forEach((item) => {
            if (this.showUnderTaker(item) !== '') {
              this.tableData.push({
                name: item.name,
                status: item.status,
                undertaker: this.showUnderTaker(item)
              })
            }
          })
          this.desMap = getConfig()
          this.destination = []
          res.otJobs.forEach((item) => {
            if (
              item.tasks[0] !== null &&
              item.tasks[0].centerName != null &&
              item.tasks[0].centerName[0] &&
              item.tasks[0].centerName[0] != ''
            ) {
              this.destination.push(item.tasks[0].centerName[0])
            }
          })
          this.destination = Array.from(new Set(this.destination))
          this.desMap.lines = this.desMap.lines.filter((item) => {
            if (this.destination.indexOf(item.target) !== -1) {
              return item
            }
          })
          this.show = true
        }
      )
    },
    showUnderTaker (item) {
      if (item.tasks == null) {
        return ''
      } else if (item.tasks.length > 2) {
        return item.tasks[0].centerName[0] + '等'
      } else {
        if (item.tasks[0].centerName == null) {
          return ''
        } else {
          return item.tasks[0].centerName[0]
        }
      }
    },
    getTaskTotal () {
      getDynamic().then((res) => {
        if (res) {
          if (res.allCardRunTime && res.allJobCount && res.allJobRunTime) {
            const data = res
            this.accrueData = {
              config1: data.allCardRunTime,
              config2: data.allJobCount,
              config3: data.allJobRunTime
            }
          }
        }
      })
    },
    getAccrueCenter () {
      getAccrueCenter().then((res) => {
        this.centerData = { used: [], xData: [] }
        if (res.perCenterComputerPowers) {
          const data1 = res.perCenterComputerPowers.filter((item) => {
            return item
          })
          data1.sort(orderDown)
          data1.forEach((item, index) => {
            if (index < 8) {
              this.centerData.used.push(item.computerPower.toFixed(1))
              this.centerData.xData.push(item.centerName)
            }
          })
        }
      })
    },
    getConnected () {
      getConnected().then((res) => {
        this.connectedData = { used: [], xData: [] }
        if (res.computerPowerInfos !== null) {
          res.computerPowerInfos.forEach((item, index, array) => {
            if (
              item.monthAccPower &&
              item.monthAccPower == 0 &&
              array[index + 1].monthAccPower != 0
            ) {
              this.connectedData.used.push(item.monthAccPower)
              this.connectedData.xData.push(item.monthTime)
            }
            if (item.monthAccPower !== 0) {
              this.connectedData.used.push(item.monthAccPower)
              this.connectedData.xData.push(item.monthTime)
            }
          })
        }
      })
    },
    showTip (e) {
      if (this.show2 == false) {
        if (e.showTip == true) {
          // this.show1 = false;
          // this.show3 = true;
          this.tipData = JSON.parse(JSON.stringify(e))
        }
      }
    },
    hiddenTip (e) {
      if (this.show2 == false) {
        if (e == false) {
          // this.show1 = true;
          // this.show3 = false;
          this.show2 = false
        }
      }
    },
    hiddenNavbar () {
      var target = document.getElementById('navbarToggleExternalContent')
      target.className = 'collapse'
    },
    getNewData () {
      var nowTemp = new Date().getTime() // 获取当前时间戳
      var tomorrowTemp = new Date(
        moment().add(1, 'days').format().substr(0, 10) + ' 00:00:00'
      ).getTime() // 获取今天24：00的时间戳
      var residueTemp = tomorrowTemp - nowTemp // 距离当天24：00的时间戳
      setTimeout(() => {
        // location.reload();
        // 次天0点 执行每天24;00 刷新
        setTimeout(() => {
          location.reload()
        }, 60000)
      }, residueTemp)
    }
  }
}
</script>
<style scoped>
.el-header {
  /* height: 4rem !important; */
  /* background-image: url("../barStatic/screen1/header-bg.svg");
  background-size: 100% 100%; */
  height: 90px !important;
}

.background {
  background-image: url("../barAssets/background-100-01.jpg");
  background-size: 100% 100%;
  min-width: 1700px;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  min-height: 1150px;
}

.title {
  margin-top: 20px;
  font-size: 3rem;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  /* transform: translateY(-50%); */
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
}

/* .subTitle {
    margin-top: 1px;
    font-family: "思源黑体 CN-Regular";
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0px;
    line-height: 20px;
    text-align: center;
    width: 100%;
    font-style: italic;
    font-weight: normal;
    filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  } */

.subTitle span {
  letter-spacing: 1px;
}

.title1 {
  font-family: 庞门正道标题体3;
  font-size: 24px;
  color: rgb(255, 255, 255);
  letter-spacing: 2px;
  line-height: 24px;
  text-align: left !important;
  width: 100%;
  font-style: italic;
  font-weight: normal;
  text-align: center;
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
}

.province {
  width: 100px;
  height: 100px;
}

.arrow {
  background-size: 100% 100%;
  background-image: url("../barStatic/screen1/arrow2.png");
  width: 50px;
  height: 35px;
  position: relative;
  top: -5px;
  background-repeat: no-repeat;
}

.linear-gradient {
  background: linear-gradient(
    to right,
    rgba(255, 0, 0, 0),
    rgba(255, 255, 255, 0.2)
  );
  margin: 12px 0 18px 0;
  box-sizing: content-box;
  background-clip: content-box;
}

.el-container.is-vertical {
  height: 100%;
}

.showMt {
  margin-top: 10% !important;
}

.buttonStyle {
  height: 40px;
  color: white;
  line-height: 40px;
  text-align: center;
  background-color: rgba(255, 255, 255, 10%);
  cursor: pointer;
  width: 100%;
  border: 2px solid rgb(50, 197, 255, 0.5);
  font-weight: 800;
  /* width: 150px; */
  font-size: 20px;
}

.check {
  height: 40px;
  font-weight: 800;
  color: #ffd408;
  background: radial-gradient(
    65% 100% at 50% 0%,
    rgba(1, 145, 255, 0.33),
    rgba(255, 1, 246, 0) 100%
  );
  border: 2px solid rgb(50, 197, 255);
  width: 100%;
  line-height: 40px;
}

.footer {
  background-image: url("../barStatic/screen1/footer-bg.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* min-height: 20px !important; */
  background-size: 100% 100%;
  height: 20px !important;
}

.wrapper1 {
  position: absolute;
  left: 25px;
}
.wrapper2 {
  position: absolute;
  left: 25px;
}
.icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.icon2 {
  display: inline-block;
  width: 25px;
  height: 25px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.text {
  display: inline-block;
  line-height: 18px;
  height: 18px;
  position: relative;
  top: -2px;
  margin-right: 2px;
}

.img1 {
  background-image: url("../barStatic/screen1/intellectual.svg");
}

.img2 {
  background-image: url("../barStatic/screen1/super.svg");
}

.img3 {
  background-image: url("../barStatic/screen1/eandw.svg");
}

.img4 {
  background-image: url("../barAssets/statusEd.svg");
}

.img5 {
  background-image: url("../barAssets/statusIng.svg");
}

.img6 {
  background-image: url("../barAssets/statusUn.svg");
}

.buttonStyle2 {
  /* height: 50px; */
  color: white;
  /* line-height: 50px; */
  /* text-align: center; */
  /* background-color: rgba(255, 255, 255, 10%); */
  cursor: pointer;
  min-width: 150px;
  margin-bottom: 10px;
}

.check2 {
  color: #fff !important;
  background-image: linear-gradient(
    to right,
    rgba(24, 144, 255, 0%),
    rgba(24, 144, 255, 10%),
    rgba(24, 144, 255, 40%)
  );
}

.statusWrapper1 {
  position: absolute;
  right: 40px;
  top: 650px;
  z-index: 100;
}

.statusWrapper2 {
  position: absolute;
  left: 40px;
  top: 650px;
}

.title2 {
  color: rgba(255, 255, 255, 0.7);
  line-height: 40px;
}

.title3 {
  line-height: 40px;
  color: rgba(255, 255, 255, 0.7);
  width: 140px;
}

.arrow1 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../barAssets/arrow10TB.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow2 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../barAssets/arrowSD.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow3 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../barAssets/arrowMPLS.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.mt-2 {
  margin-top: 15px;
}

.ml-2 {
  margin-left: 65px;
}

.smalltitle {
  letter-spacing: 0px;
  font-size: 2.6rem;
}
.main {
  padding-left: 20px;
  padding-right: 20px;
  min-height: 1040px;
  padding-top: 20px;
}
.borderBg {
  padding: 5px 10px 10px 10px;
  background: rgba(8, 3, 51, 0.85);
  border: 2px solid rgb(23, 71, 151);
  box-shadow: 0px 4px 8px rgb(0 0 0 / 60%);
  position: relative;
}
.borderColor {
  color: rgb(255, 255, 255);
  text-align: left;
  padding: 5px 0 5px 25px;
  font-weight: 900;
  font-size: 22px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 100%;
  letter-spacing: 5px;
  background: linear-gradient(
    to right,
    rgba(40, 12, 171, 0.52),
    rgba(40, 12, 171, 0)
  );
  margin-bottom: 5%;
}
.linear-gradient-text {
  background: linear-gradient(
    to bottom,
    rgb(255, 255, 255),
    rgb(207, 242, 255)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 3rem;
  padding-left: 5px;
}
.ckButton {
  bottom: 10%;
  width: 100%;
  margin: 0 auto;
  min-width: 1600px;
}
.border1 {
  /* 矩形 4 */
  width: 20px;
  height: 20px;
  border-left: 2px solid rgb(185, 225, 255);
  border-top: 2px solid rgb(185, 225, 255);
  position: absolute;
  top: 0px;
  left: 0px;
}
.border2 {
  width: 20px;
  height: 20px;
  border-top: 2px solid rgb(185, 225, 255);
  border-right: 2px solid rgb(185, 225, 255);
  position: absolute;
  right: 0px;
  top: 0px;
}
.border3 {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 0px;
  bottom: -1px;
  border-left: 2px solid rgb(185, 225, 255);
  border-bottom: 2px solid rgb(185, 225, 255);
}
.border4 {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 0px;
  bottom: -1px;
  border-right: 2px solid rgb(185, 225, 255);
  border-bottom: 2px solid rgb(185, 225, 255);
}
.titleIcon {
  background-image: url("../barStatic/screen1/titleIcon.svg");
  background-size: 100%;
  background-repeat: no-repeat;
  height: 15px;
  width: 80%;
  margin: 0 auto;
}
.mb-1 {
  margin-bottom: 20px;
}
.dropdown {
  width: 225px;
  position: fixed;
  right: 30px;
  top: 5px;
  z-index: 200;
}
</style>
