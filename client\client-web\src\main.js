/* eslint-disable no-new */
import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import App from './App.vue'
import * as echarts from 'echarts'
import router from './router'
import dataV from '@jiaminghi/data-view'
import moment from 'moment'
import 'echarts-gl'
import HighchartsVue from 'highcharts-vue'
import VueWorker from 'vue-worker'
import 'jquery'
import 'popper.js'
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap/dist/js/bootstrap.min.js'
import 'default-passive-events'
import store from './store/index.js'
import './utils/request'
import './styles/dialog.scss'

moment.locale('zh-cn')
Vue.prototype.$moment = moment
Vue.prototype.$echarts = echarts
Vue.use(ElementUI)
Vue.use(dataV)
Vue.use(HighchartsVue)
Vue.use(VueWorker)
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
