/**
 * YAML预览混入 - 封装YAML预览相关操作逻辑
 */
import { prepareTaskData, previewYamlContent } from '@/utils/yamlHelper'

export default {
  data () {
    return {
      yamlPreviewVisible: false,
      yamlContent: '',
      yamlFilename: '',
      yamlLoading: false
    }
  },
  methods: {
    /**
     * 准备任务数据
     * @returns {Object|null} - 任务数据或null
     */
    prepareTaskData () {
      // 验证是否有计算中心
      if (this.computingCenters.length === 0) {
        this.$message.error('请至少添加一个计算中心')
        return null
      }

      // 验证表单
      let isValid = false
      this.$refs.taskForm.validate(valid => {
        isValid = valid
      })

      if (!isValid) {
        return null
      }

      return prepareTaskData(this.taskForm, this.computingCenters)
    },

    /**
     * 预览YAML内容
     * @param {Boolean} forceRefresh - 是否强制刷新
     */
    previewYaml (forceRefresh = false) {
      // 如果已有内容且不是强制刷新，直接显示
      if (this.yamlContent && !forceRefresh) {
        this.yamlPreviewVisible = true
        return
      }

      const apiRequestData = this.prepareTaskData()
      if (!apiRequestData) return

      // 显示加载中
      this.yamlLoading = true
      this.yamlPreviewVisible = true

      // 调用预览函数
      previewYamlContent(apiRequestData, {
        onSuccess: (response) => {
          this.yamlContent = response.yaml_content
          this.yamlFilename = response.filename
          this.$message.success('YAML 生成成功！')
        },
        onError: (error) => {
          console.error('生成YAML失败:', error)
          this.$message.error('生成YAML失败，请检查参数后重试')
        },
        onComplete: () => {
          this.yamlLoading = false
        }
      })
    }
  }
}
