import { ref, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { TaskService } from '@/services/TaskService'

/**
 * 任务管理组合式函数
 * 负责任务状态管理、对话框控制、任务数据获取等
 */
export function useTaskManagement () {
  const store = useStore()

  // ============ 任务状态 ============
  const currentTaskId = ref('')
  const addTaskDialogVisible = ref(false)
  const taskDetailDialogVisible = ref(false)

  // 任务基本信息
  const taskDetail = ref({
    ID: undefined,
    NJobs: undefined,
    SnapshotTime: undefined,
    CompletedFlag: undefined,
    strategy: undefined
  })

  // 任务统计信息
  const taskStatistics = ref({
    totalNum: 0,
    execNum: 0
  })

  // 任务对比数据
  const compareData = ref({
    submitJobCompare0: null,
    pendingJobCompare0: null,
    resouceCompare0: null,
    submitJobCompare: null,
    pendingJobCompare: null,
    resouceCompare: null
  })

  // ============ Computed ============
  const taskId = computed(() => store.state.id)
  const compareId = computed(() => store.state.compareId)
  const strategy1 = computed(() => store.state.strategy1)
  const strategy2 = computed(() => store.state.strategy2)
  const interval = computed(() => store.state.interval)

  // ============ 对话框管理 ============
  const openAddTaskDialog = () => {
    addTaskDialogVisible.value = true
  }

  const closeAddTaskDialog = () => {
    addTaskDialogVisible.value = false
  }

  const openTaskDetailDialog = (task) => {
    currentTaskId.value = task.id || task.ID
    taskDetailDialogVisible.value = true
  }

  const closeTaskDetailDialog = () => {
    taskDetailDialogVisible.value = false
    currentTaskId.value = ''
  }

  // ============ 任务数据获取 ============
  const loadTaskData = async (taskId, intervalHours) => {
    try {
      const response = await TaskService.getTaskDetail(taskId, intervalHours)
      if (response) {
        // 更新任务基本信息
        const basicInfo = TaskService.extractBasicInfo(response)
        taskDetail.value = { ...basicInfo }

        // 更新统计信息
        if (response.CenterInfoToWebList) {
          const statsData = TaskService.extractStatisticsData(response.CenterInfoToWebList)
          taskStatistics.value = {
            totalNum: statsData.historySubmitJobData.reduce((a, b) => Math.max(a, b), 0),
            execNum: statsData.historyCompleteJobData.reduce((a, b) => Math.max(a, b), 0)
          }
        }

        return response
      }
    } catch (error) {
      console.error('加载任务数据失败:', error)
      throw error
    }
  }

  const loadCompareTaskData = async (mainTaskId, compareTaskId, intervalHours) => {
    try {
      const { mainTask, compareTask } = await TaskService.getMultipleTaskData(
        mainTaskId,
        compareTaskId,
        intervalHours
      )

      if (mainTask && mainTask.CenterInfoToWebList) {
        const mainStats = TaskService.extractStatisticsData(mainTask.CenterInfoToWebList)
        compareData.value.submitJobCompare0 = mainStats.frameData.submitJob
        compareData.value.pendingJobCompare0 = mainStats.frameData.pendingTime
        compareData.value.resouceCompare0 = mainStats.frameData.resourceUsage
      }

      if (compareTask && compareTask.CenterInfoToWebList) {
        const mainFrames = mainTask?.CenterInfoToWebList?.[0]?.SnapshotInfoToWebList?.length || 0
        const compareStats = TaskService.processCompareTaskData(compareTask, mainFrames)
        if (compareStats) {
          compareData.value.submitJobCompare = compareStats.submitJob
          compareData.value.pendingJobCompare = compareStats.pendingTime
          compareData.value.resouceCompare = compareStats.resourceUsage
        }
      }

      return { mainTask, compareTask }
    } catch (error) {
      console.error('加载对比任务数据失败:', error)
      throw error
    }
  }

  // ============ 事件处理 ============
  const handleTaskSubmit = (taskData) => {
    console.log('提交新任务:', taskData)
    // 这里添加任务提交逻辑
    closeAddTaskDialog()
  }

  const handleTaskDetailSubmitSuccess = () => {
    console.log('任务详情提交成功')
    closeTaskDetailDialog()
    // 刷新任务列表等操作
  }

  // ============ 监听器 ============
  watch(
    [taskId, interval],
    async ([newTaskId, newInterval]) => {
      if (newTaskId && newTaskId !== 0) {
        await loadTaskData(newTaskId, newInterval)
      }
    },
    { immediate: true }
  )

  watch(
    [taskId, compareId, interval],
    async ([newTaskId, newCompareId, newInterval]) => {
      if (newTaskId && newTaskId !== 0 && newCompareId && newCompareId !== 0) {
        await loadCompareTaskData(newTaskId, newCompareId, newInterval)
      }
    },
    { deep: true }
  )

  return {
    // 状态
    currentTaskId,
    addTaskDialogVisible,
    taskDetailDialogVisible,
    taskDetail,
    taskStatistics,
    compareData,

    // Computed
    taskId,
    compareId,
    strategy1,
    strategy2,
    interval,

    // 方法
    openAddTaskDialog,
    closeAddTaskDialog,
    openTaskDetailDialog,
    closeTaskDetailDialog,
    loadTaskData,
    loadCompareTaskData,
    handleTaskSubmit,
    handleTaskDetailSubmitSuccess
  }
}
