import Vue from 'vue'
import Router from 'vue-router'

/* Layout */
Vue.use(Router)
export const constantRoutes = [
  // {
  //   path: '/',
  //   component: () => import('@/views/login'),
  //   name: 'login'
  // },
  {
    path: '/c2netDemo',
    component: () => import('@/views/c2netDemo'),
    name: 'c2netDemo'
  },
  {
    path: '/c2net3D',
    component: () => import('@/views/c2net3D'),
    name: 'c2net3D'
  },
  {
    path: '/dispatchCenter',
    component: () => import('@/views/dispatchCenter'),
    name: 'dispatchCenter'
  },
  {
    path: '/dispatchCenterDemo',
    component: () => import('@/views/dispatchCenterDemo'),
    name: 'dispatchCenterDemo'
  },
  {
    path: '/collaborative',
    component: () => import('@/views/collaborative'),
    name: 'collaborative'
  },
  {
    path: '/c2net',
    component: () => import('@/views/c2net'),
    name: 'c2net'
  },
  {
    path: '/shaoguanC2net',
    component: () => import('@/views/shaoguanC2net'),
    name: 'shaoguanC2net'
  },
  {
    path: '/intelligentJointScheduling',
    component: () => import('@/views/intelligentJointScheduling'),
    name: 'intelligentJointScheduling'
  }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter () {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
