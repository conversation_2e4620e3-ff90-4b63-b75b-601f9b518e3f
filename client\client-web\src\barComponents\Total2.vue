<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-row type="flex" justify="center">
          <el-col :span="6">
            <el-row type="flex" justify="center">
              <el-col :span="5">
                <div class="circle2"></div>
              </el-col>
              <el-col :span="19">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="20">
                        <div>算力规模</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="24">
                      <div class="num">
                        <span class="num">
                          {{ computeScale }}
                        </span>
                        <span class="unitBg">POps@FP16</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row type="flex" justify="center">
              <el-col :span="5">
                <div class="circle1"></div>
              </el-col>
              <el-col :span="18">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="12">
                        <div>已接入节点数</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="24">
                      <div class="num">
                        <span class="num">
                          {{ nodeNum }}
                        </span>
                        <span class="unit">个</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row type="flex" justify="center">
              <el-col :span="4">
                <div class="circle3"></div>
              </el-col>
              <el-col :span="18">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="20">
                        <div>已接入存储</div>
                      </el-col>
                    </el-row>
                  </div>
                   <el-row>
                    <el-col :span="24">
                      <div class="num">
                        <span class="num">
                          {{ storageScale }}
                        </span>
                        <span class="unit">PB</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row type="flex" justify="center">
              <el-col :span="5">
                <div class="circle5"></div>
              </el-col>
              <el-col :span="19">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="20">
                        <div>已运行时间</div>
                      </el-col>
                    </el-row>
                  </div>
                   <el-row>
                    <el-col :span="24">
                      <div class="num">
                        <span class="num">
                          {{ runtime }}
                        </span>
                        <span class="unit">小时</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { barMessage } from '@/barApi/screenService.js'
import { formatDuring } from '@/barUtils/index'
export default {
  name: 'total',
  data () {
    return {
      nodeNum: 0,
      computeScale: 0,
      storageScale: 0,
      runtime: ''
    }
  },
  props: {},
  watch: {},
  mounted () {
    barMessage().then((res) => {
      if (res) {
        delete res.data
        const data = JSON.parse(res.infojson)
        this.nodeNum = data.AccessNodeNum
        this.storageScale = data.AccessStorage
        this.computeScale = data.ComputeScale
        const time1 = Date.parse(new Date())
        var time2 = new Date(data.AccessTime).getTime()
        this.runtime = formatDuring((time1 - time2) / 1000)
      }
    })
  },
  methods: {
    // 时间戳转换日期
    formatDuring (val) {
      return formatDuring(val)
    }
  }
}
</script>
<style scoped>
.icon {
  background-image: url(../static/screen1/cloud.png);
  width: 80px;
  height: 80px;
  background-size: 100% 100%;
  background-position-y: 10px;
  background-repeat: no-repeat;
}

.title {
  font-family: "思源黑体 CN-Regular";
  font-size: 20px;
  color: rgb(211, 237, 255);
  letter-spacing: 1px;
  font-weight: normal;
  font-style: normal;
  transform: translate(0px, 0px);
  word-break: keep-all;
}

.content {
  padding-top: 5px;
  padding-left: 10px;
}

.num {
  text-align: left;
  color: rgb(255, 212, 8);
  font-size: 20px;
  font-family: DINPro-Medium;
  font-weight: normal;
  text-shadow: rgb(19 128 255) 0px 0px 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: 900;
}

.unit {
  display: inline-flex;
  align-items: center;
  color: rgb(222, 222, 222);
  font-size: 16px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  padding-left: 2px;
  margin-top: 4px;
}

.unitBg {
  color: rgb(222, 222, 222);
  font-size: 16px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  padding-left: 10px;
  margin-top: 4px;
}

.arrow {
  background-size: 100% 150%;
  background-image: url("../barStatic/screen1/arrow.png");
  width: 50px;
  height: 30px;
  background-position: 0px -8px;
  background-repeat: no-repeat;
}

.mt {
  margin-top: 20px;
  position: relative;
  left: 80px;
}
.loading-tip1 {
  background-image: url("../barAssets/icon/riLine-map-pin-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip2 {
  background-image: url("../barAssets/icon/riLine-camera-lens-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip3 {
  background-image: url("../barAssets/icon/riLine-map-pin-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.circle1 {
  background-image: url("../barStatic/screen1/icon-01.png");
  background-size: 100% 100%;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  background-repeat: no-repeat;
  border: 6px solid rgba(50, 197, 255, 0.2);
  box-shadow: 0px 0px 20px rgb(50, 197, 255);
  border-radius: 50%;
  left: -10px;
}
.circle2 {
  background-image: url("../barStatic/screen1/icon-05.png");
  background-size: 100% 100%;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: -15px;
  background-repeat: no-repeat;
  border: 6px solid rgba(50, 197, 255, 0.2);
  box-shadow: 0px 0px 20px rgb(50, 197, 255);
  border-radius: 50%;
  left: -15px;
}
.circle3 {
  background-image: url("../barStatic/screen1/icon-07.png");
  background-size: 100% 100%;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 0px;
  background-repeat: no-repeat;
  border: 6px solid rgba(50, 197, 255, 0.2);
  box-shadow: 0px 0px 20px rgb(50, 197, 255);
  border-radius: 50%;
  left: -15px;
}
.circle5 {
  background-image: url("../barStatic/screen1/icon-06.png");
  background-size: 100% 100%;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 0px;
  background-repeat: no-repeat;
  border: 6px solid rgba(50, 197, 255, 0.2);
  box-shadow: 0px 0px 20px rgb(50, 197, 255);
  border-radius: 50%;
  left: -15px;
}
</style>
