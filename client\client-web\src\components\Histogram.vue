<template>
  <div>
    <div>
      <!-- <el-row :gutter="20">
        <el-col :span="6"
          ><div>
            <span class="titleStyle">任务ID:</span
            ><span class="contentStyle">{{ taskDetail.ID }}</span>
          </div></el-col
        >
      </el-row> -->
      <!-- <el-row :gutter="20">
        <el-col :span="8"
          ><div>
            <span class="titleStyle">任务数:</span
            ><span class="contentStyle">{{ taskDetail.NJobs }}</span>
          </div></el-col
        >
        <el-col :span="8"
          ><div>
            <span class="titleStyle">状态:</span
            ><span class="contentStyle Finshed" v-if="taskDetail.CompletedFlag"
              >已完成</span
            >
            <span class="contentStyle Running" v-if="!taskDetail.CompletedFlag"
              >运行中</span
            >
          </div></el-col
        >
        <el-col :span="8"
          ><div>
            <span class="titleStyle">运行时长:</span
            ><span class="contentStyle">{{ runningTime }} h</span>
          </div></el-col
        >
      </el-row> -->
    </div>
  </div>
</template>
<script>
import { jobDetail } from '@/api/screenService.js'
import { formatDuring2 } from '@/utils/index'
export default {
  name: 'histogram2',
  data () {
    return {
      timer: null,
      Data: {
        unused: [],
        used: [],
        xData: []
      },
      BarWidth: 35,
      Max: 0,
      // taskDetail: {
      //   ID: undefined,
      //   NJobs: undefined,
      //   SnapshotTime: undefined,
      //   CompletedFlag: undefined,
      // },
      show1: true,
      show2: false,
      show3: false,
      type: 1,
      count: 0,
      total: 0,
      name: '提交任务量',
      time: []
    }
  },
  props: {
    runningTime: {
      type: Number,
      default: 0
    },
    taskDetail: {
      type: Object,
      default: () => {}
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    taskId (newValue, oldValue) {
      clearInterval(this.timer)
      this.timer - null
      // this.getJobDetail(this.taskId);
    }
  },
  computed: {
    taskId () {
      return this.$store.state.id
    }
  },
  mounted () {
    // this.init();
  },
  destroyed () {
    clearInterval(this.timer)
  },
  methods: {
    init () {
      clearInterval(this.timer)
      this.timer - null
      const Data = { yData: [], xData: [] }
      const data = { id: this.taskId, resolution_n_hours: 10 }
      jobDetail(data).then((res) => {
        this.total = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length
        if (this.total == 0) {
          return
        }
        if (this.type == 1) {
          res.CenterInfoToWebList.forEach((item) => {
            Data.xData.push(item.CenterName)
          })
          this.count = 0
          res.CenterInfoToWebList.forEach((item) => {
            Data.yData.push(
              Math.round(item.SnapshotInfoToWebList[this.count].SubmitJob)
            )
          })
          this.Data = Data
          this.drawLine()
          const that = this
          this.timer = setInterval(() => {
            // Data.xData = [];
            Data.yData = []
            that.count++
            if (that.count >= that.total) {
              that.count = 0
              clearTimeout(that.timer)
              that.timer = null
              that.getJobDetail(that.taskId)
            }
            res.CenterInfoToWebList.forEach((item) => {
              Data.yData.push(
                Math.round(item.SnapshotInfoToWebList[that.count].SubmitJob)
              )
            })
            that.Data = Data
            that.drawLine()
          }, 5000)
        }
        if (this.type == 2) {
          const Data = { yData: [], xData: [] }
          res.CenterInfoToWebList.forEach((item) => {
            Data.xData.push(item.CenterName)
          })
          this.count = 0
          res.CenterInfoToWebList.forEach((item) => {
            Data.yData.push(
              Math.round(
                item.SnapshotInfoToWebList[this.count].AveragePendingTime
              )
            )
          })
          this.Data = Data
          this.drawLine()
          const that = this
          this.timer = setInterval(() => {
            // Data.xData = [];
            Data.yData = []
            that.count++
            if (that.count >= that.total) {
              that.count = 0
              clearTimeout(that.timer)
              that.timer = null
              that.getJobDetail(that.taskId)
            }
            res.CenterInfoToWebList.forEach((item) => {
              Data.yData.push(
                Math.round(
                  item.SnapshotInfoToWebList[that.count].AveragePendingTime
                )
              )
            })

            that.Data = Data
            that.drawLine()
          }, 5000)
        }
        if (this.type == 3) {
          const Data = { yData: [], xData: [] }
          res.CenterInfoToWebList.forEach((item) => {
            Data.xData.push(item.CenterName)
          })
          this.count = 0
          res.CenterInfoToWebList.forEach((item) => {
            Data.yData.push(
              Math.round(
                item.SnapshotInfoToWebList[this.count].AverageMachineUse
              )
            )
          })
          this.Data = Data
          this.drawLine()
          const that = this
          this.timer = setInterval(() => {
            // Data.xData = [];
            Data.yData = []
            that.count++
            if (that.count >= that.total) {
              that.count = 0
              clearTimeout(that.timer)
              that.timer = null
              that.getJobDetail(that.taskId)
            }
            res.CenterInfoToWebList.forEach((item) => {
              Data.yData.push(
                Math.round(
                  item.SnapshotInfoToWebList[that.count].AverageMachineUse
                )
              )
            })
            that.Data = Data
            that.drawLine()
          }, 5000)
        }
      })
    },
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        title: {
          text:
            this.Data.yData.length === 0 && this.Data.xData.length == 0
              ? '暂无数据'
              : '',

          textStyle: {
            color: '#fff'
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10] // 位置/ 位置
        },
        // color: ["#32C5FF"],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            show: false
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        legend: {
          show: false,
          right: '0',
          itemWidth: 12,
          textStyle: { color: '#fff' }
        },
        grid: {
          left: '35%',
          right: '5%'
          // bottom: "18%",
          // top: "12%",
        },
        yAxis: {
          type: 'category',
          nameTextStyle: {
            color: '#FFF',
            padding: [0, 0, 0, 0] // 距离坐标位置的距离
          },
          data: this.Data.xData,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            interval: 'auto',
            textStyle: {
              fontSize: 14,
              lineHeight: 10,
              fontWeight: 'bold',
              color: '#fff'
            },
            formatter: function (value, index) {
              if (value.length > 8) {
                return value.substr(0, 10) + '...'
              } else {
                return value
              }
            }
          },
          axisTick: {
            // x轴刻度相关设置
            alignWithLabel: true,
            show: false
          }
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: false,
            lineStyle: {
              width: 1,
              type: 'dotted'
            }
          },
          data: this.Data.yData,
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: false,
            textStyle: {
              fontSize: '8',
              fontWeight: 'bold',
              color: '#fff'
            }
          },
          scale: true,
          min: 0,
          splitNumber: 1
        },
        series: [
          {
            name: this.name,
            type: 'bar',
            stack: 'total',
            barWidth: 12,
            label: {
              normal: {
                show: true, // 开启显示
                position: 'right', // 柱形上方
                textStyle: {
                  // 数值样式
                  color: '#fff'
                }
                // formatter: (param) => {
                //   return `{a|${param.data}}{b| 卡时}`;
                // },
                // rich: {
                //   a: {
                //     color: "#ffd408",
                //     fontSize: 12,
                //     fontWeight: "bold",
                //   },
                //   b: {
                //     color: "#DEDEDE",
                //     fontSize: 10,
                //     fontWeight: "bold",
                //   },
                // },
              }
            },
            emphasis: {
              focus: 'series'
            },
            showBackground: true,

            backgroundStyle: {
              color: 'rgba(110, 193, 244, 0.1)'
            },

            data: this.Data.yData ? this.Data.yData : [],
            itemStyle: {
              color: {
                type: 'linear',
                colorStops: [
                  {
                    offset: 1,
                    color: 'rgb(50,197,255)' // 0% 处的颜色
                  },
                  {
                    offset: 0,
                    color: 'rgba(22,93,255,0)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 100
        },
        animationEasing: 'backln'
      }
      option && this.myChart.setOption(option)
    },
    getJobDetail (id) {
      // let data = { id: id, resolution_n_hours: 10 };
      // jobDetail(data).then((res) => {
      //   this.taskDetail.SnapshotTime = res.SnapshotTime;
      //   this.taskDetail.ID = res.ID;
      //   this.taskDetail.NJobs = res.NJobs;
      //   this.taskDetail.CompletedFlag = res.CompletedFlag;
      //   this.time = [];
      //   res.CenterInfoToWebList[0].SnapshotInfoToWebList.forEach((item) => {
      //     this.time.push(item.Time);
      //   });
      //   this.init();
      // });
    },
    formatDuring (val) {
      //  console.log(val)
      return formatDuring2(val)
    },
    change (val) {
      this.type = val
      if (this.type == 1) {
        this.show1 = true
        this.show2 = false
        this.show3 = false
        this.name = '提交任务量'
      }
      if (this.type == 2) {
        this.show1 = false
        this.show2 = true
        this.show3 = false
        this.name = '任务平均等待时长'
      }
      if (this.type == 3) {
        this.show1 = false
        this.show2 = false
        this.show3 = true
        this.name = '资源利用率对比'
      }
      this.getJobDetail(this.taskId)
    }
  }
}
</script>
<style scoped>
.titleStyle {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  display: inline-block;
  margin-top: 20px;
  font-weight: 800;
}
.contentStyle {
  color: #fff;
  font-size: 14px;
  display: inline-block;
  font-weight: 800;
  margin-left: 20px;
  margin-top: 20px;
}
.Finshed {
  color: #1ee7e7;
}
.Running {
  color: #1890ff;
}
.bg {
  box-sizing: border-box;
  background: radial-gradient(
    65% 100% at 50% 0%,
    rgba(1, 145, 255, 0.33),
    rgba(255, 1, 246, 0) 100%
  );
  border: 1px solid rgba(68, 103, 215, 0.5);
  padding: 5px 2px 5px 2px;
  font-size: 12px;
  cursor: pointer;
}
.bg2 {
  border: 1px solid rgb(50, 197, 255);
}
.mt {
  margin-top: 30px;
}
.button {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.check {
  color: rgb(50, 197, 255);
  font-family: 思源黑体;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0px;
}
</style>
