<template>
  <div class="map-container">
    <!-- 顶部信息栏 -->
    <div class="map-header">
      <el-row justify="center" type="flex">
        <el-col :span="18">
          <Total :msg="mapInfo" :taskDetail="taskDetail"></Total>
        </el-col>
      </el-row>
    </div>

    <!-- 地图主体 -->
    <div class="map-body">
      <el-row>
        <el-col :span="24">
          <div class="map-wrapper" style="max-height: 800px; overflow: hidden">
            <chinaMap
              ref="chinaMap"
              :Count="currentFrame"
              :Stop="stopped"
              :intervalSetting="intervalSetting"
              :taskResponse="taskResponse"
              @request-job-detail="handleRequestJobDetail"
            />

            <!-- 控制面板 -->
            <div class="control-panel">
              <div class="control-wrapper">
                <!-- 进度条 -->
                <div class="progress-container">
                  <el-slider
                    v-if="showProgress && showControls"
                    v-model="progressPercentage"
                    :disabled="disabled"
                    :format-tooltip="formatTooltip"
                    class="progress-slider"
                    @change="handleProgressChange"
                  />
                </div>

                <!-- 开始按钮 -->
                <div class="start-button-container">
                  <el-button
                    v-if="showControls"
                    :disabled="disabled"
                    size="small"
                    type="primary"
                    class="control-btn start-btn"
                    @click="handleStart"
                  >开始
                  </el-button>
                </div>

                <!-- 暂停/继续按钮 -->
                <div class="pause-continue-container">
                  <el-button
                    v-show="showPauseButton && showActionButtons"
                    :disabled="disabled"
                    size="small"
                    type="warning"
                    class="control-btn pause-btn"
                    @click="handlePause"
                  >暂停
                  </el-button>

                  <el-button
                    v-show="!showPauseButton && showActionButtons"
                    :disabled="disabled"
                    size="small"
                    type="success"
                    class="control-btn continue-btn"
                    @click="handleContinue"
                  >继续
                  </el-button>
                </div>

                <!-- 时间间隔选择 -->
                <div class="interval-container">
                  <el-select
                    v-if="showControls"
                    v-model="selectedInterval"
                    :disabled="disabled"
                    class="interval-select"
                    placeholder="请选择仿真时间间隔"
                    @change="handleIntervalChange"
                  >
                    <el-option
                      v-for="item in intervalOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Total from '@/components/Total'
import chinaMap from '@/components/chinaMapDemo'

export default {
  name: 'MapContainer',

  components: {
    Total,
    chinaMap
  },

  props: {
    // 地图信息
    mapInfo: {
      type: Object,
      default: () => ({
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      })
    },

    // 任务详情
    taskDetail: {
      type: Object,
      default: () => ({
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      })
    },

    // 当前帧数
    currentFrame: {
      type: Number,
      default: 0
    },

    // 总帧数
    totalFrames: {
      type: Number,
      default: 0
    },

    // 是否停止
    stopped: {
      type: Boolean,
      default: true
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 时间间隔设置
    intervalSetting: {
      type: Number,
      default: 24
    },

    // 任务响应数据
    taskResponse: {
      type: Object,
      default: null
    },

    // 显示控制项
    showProgress: {
      type: Boolean,
      default: false
    },

    showControls: {
      type: Boolean,
      default: true
    },

    showActionButtons: {
      type: Boolean,
      default: false
    },

    showPauseButton: {
      type: Boolean,
      default: true
    }
  },

  data () {
    return {
      // 进度百分比
      progressPercentage: 0,

      // 选中的时间间隔
      selectedInterval: 24,

      // 时间间隔选项
      intervalOptions: [
        { value: 24, label: '24h' },
        { value: 12, label: '12h' },
        { value: 6, label: '6h' }
      ]
    }
  },

  watch: {
    // 监听当前帧变化
    currentFrame (newFrame) {
      this.updateProgress(newFrame)
    },

    // 监听时间间隔设置变化
    intervalSetting (newInterval) {
      this.selectedInterval = newInterval
    }
  },

  mounted () {
    this.selectedInterval = this.intervalSetting
  },

  methods: {
    /**
     * 更新进度
     */
    updateProgress (frame) {
      if (this.totalFrames > 0) {
        this.progressPercentage = Math.round((frame / this.totalFrames) * 100)
      }
    },

    /**
     * 处理开始
     */
    handleStart () {
      this.$emit('start')
    },

    /**
     * 处理暂停
     */
    handlePause () {
      this.$emit('pause')
    },

    /**
     * 处理继续
     */
    handleContinue () {
      this.$emit('continue')
    },

    /**
     * 处理进度变化
     */
    handleProgressChange (value) {
      this.$emit('progress-change', value)
    },

    /**
     * 处理时间间隔变化
     */
    handleIntervalChange (value) {
      this.selectedInterval = value
      this.$emit('interval-change', value)
    },

    /**
     * 处理请求任务详情
     */
    handleRequestJobDetail (taskId) {
      this.$emit('request-job-detail', taskId)
    },

    /**
     * 格式化提示
     */
    formatTooltip (value) {
      return `${value}%`
    },

    /**
     * 获取地图组件引用
     */
    getMapRef () {
      return this.$refs.chinaMap
    }
  }
}
</script>

<style scoped>
.map-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.map-header {
  margin-bottom: 20px;
}

.map-body {
  position: relative;
}

.map-wrapper {
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.control-panel {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(50, 197, 255, 0.3);
  border-radius: 8px;
  padding: 15px 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.control-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  min-width: 600px;
}

.progress-container {
  flex: 1;
  min-width: 150px;
}

.progress-slider {
  margin-right: 10px;
}

.start-button-container,
.pause-continue-container {
  width: 80px;
}

.interval-container {
  width: 150px;
}

.control-btn {
  width: 100%;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.start-btn {
  background: linear-gradient(90deg, #32c5ff 0%, #1890ff 100%);
  border: none;
  color: #fff;
}

.start-btn:hover {
  background: linear-gradient(90deg, #40d9ff 0%, #40a9ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(50, 197, 255, 0.3);
}

.pause-btn {
  background: linear-gradient(90deg, #ff9500 0%, #ff7300 100%);
  border: none;
  color: #fff;
}

.pause-btn:hover {
  background: linear-gradient(90deg, #ffad33 0%, #ff8533 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 149, 0, 0.3);
}

.continue-btn {
  background: linear-gradient(90deg, #52c41a 0%, #389e0d 100%);
  border: none;
  color: #fff;
}

.continue-btn:hover {
  background: linear-gradient(90deg, #73d13d 0%, #52c41a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(82, 196, 26, 0.3);
}

.control-btn:disabled {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.3) !important;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.interval-select {
  width: 100%;
}

/* 深度选择器，用于修改子组件样式 */
:deep(.el-slider__runway) {
  background: rgba(255, 255, 255, 0.2);
  height: 6px;
}

:deep(.el-slider__bar) {
  background: linear-gradient(90deg, #32c5ff 0%, #1890ff 100%);
  height: 6px;
}

:deep(.el-slider__button) {
  background: #32c5ff;
  border: 2px solid #fff;
  width: 16px;
  height: 16px;
}

:deep(.el-slider__button:hover) {
  background: #40d9ff;
  transform: scale(1.1);
}

:deep(.el-select .el-input__inner) {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(50, 197, 255, 0.3);
  color: #fff;
}

:deep(.el-select .el-input__inner:focus) {
  border-color: #32c5ff;
}

:deep(.el-select-dropdown) {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(50, 197, 255, 0.3);
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #fff;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(50, 197, 255, 0.2);
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background: rgba(50, 197, 255, 0.3);
  color: #32c5ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-wrapper {
    min-width: 500px;
    gap: 10px;
  }

  .progress-container {
    min-width: 120px;
  }

  .start-button-container,
  .pause-continue-container {
    width: 70px;
  }

  .interval-container {
    width: 130px;
  }

  .control-btn {
    font-size: 11px;
    padding: 4px 8px;
  }
}

@media (max-width: 768px) {
  .control-wrapper {
    flex-direction: column;
    min-width: 300px;
    gap: 10px;
  }

  .progress-container,
  .start-button-container,
  .pause-continue-container,
  .interval-container {
    width: 100%;
  }
}
</style>
