<template>
  <div :id="id" style="width: 250px; height: 615px" ref="echart"></div>
</template>
<script>
export default {
  name: 'computeTotal',
  data () {
    return {
      timer: null,
      Data: { unused: [], used: [], xData: [] },
      barWidth: 35
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        // let TempData = JSON.parse(JSON.stringify(newValue));
        // this.Data.used = TempData.used;
        // this.Data.xData = TempData.xData;
        // // this.barWidth = 10;
        // this.drawLine();
      },
      deep: true
    }
  },
  mounted () {
    if (this.myChart) {
      this.myChart && this.myChart.dispose()
    }
    window.addEventListener('resize', () => {
      this.myChart.resize()
    })
    this.$nextTick(() => {
      this.drawLine()
    })
  },
  destroyed () {
    clearInterval(this.timer)
  },
  methods: {
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        yAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            // y轴
            show: true,
            lineStyle: {
              color: 'rgb(27, 71, 151)'
            }
          },
          axisTick: {
            // y轴刻度线
            show: false
          },
          splitLine: {
            // 网格线
            show: false
          },
          axisLabel: {
            show: false
          },
          data: [
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8',
            '9',
            '10',
            '11',
            '12',
            '13'
          ]
        },
        xAxis: {
          type: 'value',
          axisLine: {
            // y轴
            show: false
          },
          axisTick: {
            // y轴刻度线
            show: false
          },
          splitLine: {
            // 网格线
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: [
          {
            symbol: 'none',
            data: [
              0, 0, 500, 1130, 1330, 2130, 2706, 3306, 3406, 3406, 3506, 3506,
              3963
            ],
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                // y: 0,
                x2: 1,
                // y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(22, 93, 255,0)' // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(182, 32, 224,1)' //   0% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            },
            color: 'rgba(98,54,255,0.5)'
          }
        ]
      }
      option && this.myChart.setOption(option)
    }
  }
}
</script>
