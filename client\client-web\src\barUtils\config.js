export function getConfig () {
  return {
    points: [
      {
        name: '算力调度中心',
        coordinate: [0.58, 0.60],
        halo: {
          show: true
        },
        text: {
          show: false
        }
      },
      {
        name: '昇腾沈阳智算',
        coordinate: [0.70, 0.27]

      },
      {
        name: '昇腾大连智算',
        coordinate: [0.69, 0.31]
      },
      {
        name: '昇腾青岛智算',
        coordinate: [0.69, 0.34]
      },
      {
        name: '济南智算',
        coordinate: [0.63, 0.37]
      },
      {
        name: '济南超算',
        coordinate: [0.64, 0.35]
      },
      {
        name: '昇腾海南智算',
        coordinate: [0.53, 0.66]
      },
      {
        name: '昇腾南宁智算',
        coordinate: [0.52, 0.61]
      },
      {
        name: '昇腾昆明智算',
        coordinate: [0.42, 0.58]
      },
      {
        name: '昇腾福州智算',
        coordinate: [0.65, 0.55]
      },
      {
        name: '贵安集群',
        coordinate: [0.50, 0.54]
      },
      {
        name: '昇腾长沙智算',
        coordinate: [0.55, 0.52]
      },
      {
        name: '长沙超算',
        coordinate: [0.57, 0.52]
      },
      {
        name: '成渝枢纽集群',
        coordinate: [0.50, 0.49]
      },
      {
        name: '昇腾重庆智算',
        coordinate: [0.52, 0.47]
      },
      {
        name: '成都超算',
        coordinate: [0.44, 0.47]
      },
      {
        name: '成都智算',
        coordinate: [0.43, 0.46]
      },
      {
        name: '甘肃集群',
        coordinate: [0.46, 0.39]
      },
      {
        name: '宁夏枢纽集群',
        coordinate: [0.48, 0.36]
      },
      {
        name: '武汉智算',
        coordinate: [0.57, 0.47]
      },
      {
        name: '中原智算',
        coordinate: [0.575, 0.42]
      },
      {
        name: '郑州超算',
        coordinate: [0.58, 0.4]
      },
      {
        name: '内蒙古集群',
        coordinate: [0.56, 0.3]
      },
      {
        name: '寒武纪西安智算',
        coordinate: [0.525, 0.42]
      },
      {
        name: '西安超算',
        coordinate: [0.51, 0.428]
      },
      {
        name: '西安智算',
        coordinate: [0.51, 0.41]
      },
      {
        name: '沣东智算',
        coordinate: [0.52, 0.40]
      },
      {
        name: '京津冀集群',
        coordinate: [0.60, 0.29]
      },
      {
        name: '北京大学GPU智算',
        coordinate: [0.61, 0.29]
      },
      {
        name: '昇腾河北智算',
        coordinate: [0.605, 0.31]
      },
      {
        name: '北京智算',
        coordinate: [0.62, 0.28]
      },
      {
        name: '天津市AI计算中心',
        coordinate: [0.619, 0.325]
      },
      {
        name: '天津超算',
        coordinate: [0.63, 0.30]
      },
      {
        name: '长三角枢纽集群',
        coordinate: [0.64, 0.47]
      },
      {
        name: '中科类脑GPU智算',
        coordinate: [0.63, 0.44]
      },
      {
        name: '寒武纪合肥智算',
        coordinate: [0.63, 0.45]
      },
      {
        name: '昇腾杭州智算',
        coordinate: [0.66, 0.5]
      },
      {
        name: '宁波人工智能超算中心',
        coordinate: [0.68, 0.49]
      },
      {
        name: '昇腾上海智算',
        coordinate: [0.69, 0.47]
      },
      {
        name: '昆山超算',
        coordinate: [0.68, 0.47]
      },
      {
        name: '寒武纪南京智算',
        coordinate: [0.67, 0.455]
      },
      {
        name: '昇腾南京智算',
        coordinate: [0.66, 0.457]
      },
      {
        name: '无锡超算',
        coordinate: [0.665, 0.45]
      },
      {
        name: '寒武纪昆山智算',
        coordinate: [0.68, 0.46]
      }
    ],
    lines: [

      {
        source: '算力调度中心',
        target: '昇腾沈阳智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾大连智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾青岛智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '济南智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '济南超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾海南智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾南宁智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾昆明智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾福州智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '贵安集群',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾长沙智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '长沙超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '成渝枢纽集群',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾重庆智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '成都超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '成都智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '甘肃集群',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '宁夏枢纽集群',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '武汉智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '中原智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '郑州超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '内蒙古集群',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪西安智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '西安超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '西安智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '沣东智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '京津冀集群',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '北京大学GPU智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾河北智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '北京智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '天津市AI计算中心',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '天津超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '长三角枢纽集群',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪合肥智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '中科类脑GPU智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾杭州智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '宁波人工智能超算中心',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾上海智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昆山超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昆山超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪南京智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '昇腾南京智算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '无锡超算',
        width: 4,
        duration: [20.30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪昆山智算',
        width: 4,
        duration: [20.30]
      }
    ]
  }
}
