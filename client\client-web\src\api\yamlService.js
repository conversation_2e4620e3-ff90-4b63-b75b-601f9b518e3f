import request from '@/utils/request'

// 生成YAML配置
export function generateYaml (data, params = {}) {
  return request({
    url: '/api/v1/yaml/generate',
    method: 'post',
    data,
    params // 添加查询参数支持
  })
}

// 加载任务YAML配置
export function loadTaskYaml (taskId) {
  return request({
    url: `/api/v1/yaml/load/${taskId}`,
    method: 'get'
  })
}

// 获取任务执行日志
export function getTaskLog (taskId) {
  // 获取当前页面的origin，确保在开发环境中也能正确访问
  const origin = window.location.origin
  const baseURL = process.env.VUE_APP_BASE_API || ''

  // 构建完整的URL
  let url
  if (baseURL.startsWith('http')) {
    // 如果baseURL已经是完整URL，直接使用
    url = `${baseURL}/api/v1/yaml/task_log/${taskId}`
  } else {
    // 否则，用当前页面的origin + baseURL
    url = `${origin}${baseURL}api/v1/yaml/task_log/${taskId}`
  }

  console.log('获取日志URL:', url)

  // 使用原生fetch代替axios，避开拦截器处理
  return new Promise((resolve, reject) => {
    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        return response.text() // 直接获取文本内容
      })
      .then(data => {
        resolve(data) // 直接返回文本内容
      })
      .catch(error => {
        console.error('获取日志请求失败:', error)
        reject(error)
      })
  })
}
