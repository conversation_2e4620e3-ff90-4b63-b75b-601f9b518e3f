/**
 * 任务数据服务 - 处理任务相关的API调用和数据处理
 */
import { jobDetail } from '@/api/screenService.js'

class TaskDataService {
  /**
   * 获取任务详情
   * @param {string|number} id 任务ID
   * @param {number} interval 时间间隔（小时）
   * @returns {Promise} 返回Promise对象
   */
  getJobDetail (id, interval) {
    const data = {
      id: id,
      resolution_n_hours: interval
    }
    return jobDetail(data)
  }

  /**
   * 获取多个任务的数据对比
   * @param {string|number} taskId 主任务ID
   * @param {string|number} compareId 对比任务ID
   * @param {number} interval 时间间隔（小时）
   * @returns {Promise} 返回包含任务数据的Promise对象
   */
  async getJobData (taskId, compareId, interval) {
    const promises = []
    let taskIdRes = null
    let compareIdRes = null

    if (taskId !== 0) {
      const data1 = {
        id: taskId,
        resolution_n_hours: interval
      }
      promises.push(
        jobDetail(data1).then(res => {
          taskIdRes = res
        })
      )
    }

    if (compareId !== 0) {
      const data2 = {
        id: compareId,
        resolution_n_hours: interval
      }
      promises.push(
        jobDetail(data2).then(res => {
          compareIdRes = res
        })
      )
    }

    await Promise.all(promises)
    return {
      taskIdRes,
      compareIdRes
    }
  }

  /**
   * 初始化基本任务信息
   * @param {Object} res 任务响应数据
   * @returns {Object} 基本任务信息
   */
  initBaseTaskInfo (res) {
    return {
      msg: {
        SnapshotTime: 0,
        NPops: res.NPops,
        NCenters: res.NCenters
      },
      taskDetail: {
        SnapshotTime: res.SnapshotTime,
        ID: res.ID,
        strategy: res.Strategy,
        NJobs: res.NJobs,
        CompletedFlag: res.CompletedFlag
      }
    }
  }

  /**
   * 从CenterInfoToWebList中提取各种任务数据
   * @param {Array} centerInfoList 中心信息列表
   * @param {number} total 总数
   * @returns {Object} 提取的数据对象
   */
  extractJobData (centerInfoList, total) {
    const historySubmitJob = []
    const historyCompleteJob = []
    const submitJob = []
    const completeJob = []
    const Time = []

    const submitJobCompare = this.initTaskTypeData()
    const pendingJobCompare = this.initTaskTypeData()
    const resouceCompare = this.initTaskTypeData()

    const city = []
    const data1 = []
    const data2 = []
    const data3 = []

    centerInfoList.forEach((item) => {
      city.push(item.InfoName)
    })

    for (let i = 0; i < total; i++) {
      let HistorySubmitJob = 0
      let HistoryCompleteJob = 0
      let SubmitJob = 0
      let CompleteJob = 0
      data1[i] = []
      data2[i] = []
      data3[i] = []

      centerInfoList.forEach((item, key) => {
        if (item.SnapshotInfoToWebList[i]) {
          HistorySubmitJob += item.SnapshotInfoToWebList[i].HistorySubmitJob
          HistoryCompleteJob += item.SnapshotInfoToWebList[i].HistoryCompleteJob
          SubmitJob += item.SnapshotInfoToWebList[i].SubmitJob
          CompleteJob += item.SnapshotInfoToWebList[i].CompleteJob
          data1[i][key] = item.SnapshotInfoToWebList[i].HistorySubmitJob
          data2[i][key] = item.SnapshotInfoToWebList[i].AveragePendingTime
          data3[i][key] = item.SnapshotInfoToWebList[i].AverageMachineUse
        }
      })

      historySubmitJob[i] = HistorySubmitJob
      historyCompleteJob[i] = HistoryCompleteJob
      submitJob[i] = SubmitJob
      completeJob[i] = CompleteJob
    }

    submitJobCompare.xData = JSON.parse(JSON.stringify(city))
    pendingJobCompare.xData = JSON.parse(JSON.stringify(city))
    resouceCompare.xData = JSON.parse(JSON.stringify(city))
    submitJobCompare.used = data1
    pendingJobCompare.used = data2
    resouceCompare.used = data3

    centerInfoList[0].SnapshotInfoToWebList.forEach((item) => {
      // 使用修改后的formatDuring确保返回纯小时数
      const timeValue = this.formatDuring(item.Time)
      Time.push(timeValue)
    })

    return {
      historySubmitJob,
      historyCompleteJob,
      submitJob,
      completeJob,
      Time,
      city,
      submitJobCompare,
      pendingJobCompare,
      resouceCompare
    }
  }

  /**
   * 处理对比任务的快照数据
   * @param {Object} res 任务响应数据
   * @param {number} total 总数
   * @param {number} total2 第二个任务的总数
   * @returns {Object} 处理后的对比数据
   */
  processCompareSnapshots (res, total, total2) {
    const city = []
    const data1 = []
    const data2 = []
    const data3 = []

    res.CenterInfoToWebList.forEach((item) => {
      city.push(item.InfoName)
    })

    if (total2 <= total) {
      res.CenterInfoToWebList.forEach((item) => {
        item.SnapshotInfoToWebList.length = total
      })
    }

    for (let i = 0; i < total; i++) {
      data1[i] = []
      data2[i] = []
      data3[i] = []
      res.CenterInfoToWebList.forEach((item, key) => {
        const HistorySubmitJob = item.SnapshotInfoToWebList[total2 - 1].HistorySubmitJob
        const AveragePendingTime = item.SnapshotInfoToWebList[total2 - 1].AveragePendingTime
        const AverageMachineUse = item.SnapshotInfoToWebList[total2 - 1].AverageMachineUse

        data1[i][key] = item.SnapshotInfoToWebList[i] != undefined
          ? item.SnapshotInfoToWebList[i].HistorySubmitJob
          : HistorySubmitJob
        data2[i][key] = item.SnapshotInfoToWebList[i] != undefined
          ? item.SnapshotInfoToWebList[i].AveragePendingTime
          : AveragePendingTime
        data3[i][key] = item.SnapshotInfoToWebList[i] != undefined
          ? item.SnapshotInfoToWebList[i].AverageMachineUse
          : AverageMachineUse
      })
    }

    return {
      city,
      data1,
      data2,
      data3
    }
  }

  /**
   * 初始化任务类型数据对象
   * @returns {Object} 初始化的数据对象
   */
  initTaskTypeData () {
    return {
      used: null,
      xData: [],
      used2: null
    }
  }

  /**
   * 初始化主对比数据
   * @param {Array} city 城市列表
   * @param {Object} submitJobCompare 提交任务对比数据
   * @param {Object} pendingJobCompare 等待任务对比数据
   * @param {Object} resouceCompare 资源对比数据
   * @returns {Object} 初始化的对比数据
   */
  initMainCompareData (city, submitJobCompare, pendingJobCompare, resouceCompare) {
    const submitJobCompare0 = this.initTaskTypeData()
    const pendingJobCompare0 = this.initTaskTypeData()
    const resouceCompare0 = this.initTaskTypeData()

    submitJobCompare0.xData = city
    pendingJobCompare0.xData = city
    resouceCompare0.xData = city
    submitJobCompare0.used = submitJobCompare.used
    pendingJobCompare0.used = pendingJobCompare.used
    resouceCompare0.used = resouceCompare.used

    return {
      submitJobCompare0,
      pendingJobCompare0,
      resouceCompare0
    }
  }

  /**
   * 更新指定类型的任务数据
   * @param {Array} city 城市列表
   * @param {Array} usedData 使用数据
   * @param {Array} usedData2 对比使用数据
   * @returns {Object} 更新后的任务数据
   */
  updateTaskDataForType (city, usedData, usedData2) {
    const taskData = {
      xData: city,
      used: usedData
    }

    if (usedData2) {
      taskData.used2 = usedData2
    }

    return taskData
  }

  /**
   * 将时间转换为小时格式
   * @param {number} val 时间值
   * @returns {number} 小时数
   */
  formatDuring (val) {
    // 修改为只返回小时数，不使用天(d)为单位
    const totalHours = Math.floor(val * 1000 / (1000 * 60 * 60))
    return totalHours
  }

  /**
   * 将秒转换为小时格式
   * @param {number} val 秒数
   * @returns {number} 小时数
   */
  formatDuring2 (val) {
    // 确保只返回小时数
    var hours = Math.floor(val * 3600 / 3600)
    return hours
  }

  /**
   * 将时间格式化为易读格式
   * @param {number} val 时间值
   * @returns {string} 格式化后的时间字符串
   */
  formatDuring3 (val) {
    // 修改为只返回小时为单位的时间
    if (val < (1 / 3600)) {
      return (val * 3600).toFixed(2) + 's'
    }

    // 转换为小时，包括小数部分
    const totalHours = val
    return totalHours.toFixed(2) + 'h'
  }
}

export default new TaskDataService()
