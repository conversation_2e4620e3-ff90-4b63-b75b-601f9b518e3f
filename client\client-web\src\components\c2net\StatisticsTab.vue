<template>
  <div class="statistics-tab">
    <el-row justify="center" type="flex">
      <el-col>
        <div>
          <el-row class="row-bg" justify="space-around" type="flex">
            <el-col :span="7" class="bg2">
              <el-link
                :class="{ button2: true, check3: activeTab === 1 }"
                :disabled="disabled"
                :underline="false"
                @click="changeTab(1)"
              >提交任务量
              </el-link>
            </el-col>
            <el-col :span="7" class="bg2">
              <el-link
                :class="{ button2: true, check3: activeTab === 2 }"
                :disabled="disabled"
                :underline="false"
                @click="changeTab(2)"
              >任务平均等待时长
              </el-link>
            </el-col>
            <el-col :span="7" class="bg2">
              <el-link
                :class="{ button2: true, check3: activeTab === 3 }"
                :disabled="disabled"
                :underline="false"
                @click="changeTab(3)"
              >资源利用率对比
              </el-link>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
/**
 * 统计选项卡组件 - 提供三个统计类型的切换
 */
export default {
  name: 'StatisticsTab',
  props: {
    /**
     * 当前活动选项卡
     */
    value: {
      type: Number,
      default: 1
    },
    /**
     * 是否禁用选项卡
     */
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      activeTab: this.value
    }
  },
  watch: {
    value (newVal) {
      this.activeTab = newVal
    }
  },
  methods: {
    /**
     * 切换选项卡
     */
    changeTab (tabIndex) {
      this.activeTab = tabIndex
      this.$emit('input', tabIndex)
      this.$emit('change', tabIndex)
    }
  }
}
</script>

<style scoped>
.statistics-tab {
  margin-top: 20px;
  margin-bottom: 10px;
}

.check3 {
  color: #32c5ff !important;
  font-family: 思源黑体;
  font-size: 14px;
  font-weight: 800;
  letter-spacing: 0;
  height: 14px;
  line-height: 14px;
  margin-bottom: 2px;
}

.button2 {
  color: #fff;
  font-family: 思源黑体;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  font-weight: 800;
  height: 14px;
  line-height: 14px;
  margin-bottom: 2px;
}

.bg2 {
  box-sizing: border-box;
  background: radial-gradient(
    65% 100% at 50% 0,
    rgba(1, 145, 255, 0.33),
    rgba(255, 1, 246, 0) 100%
  );
  border: 1px solid rgba(68, 103, 215, 0.5);
  padding: 2px 0px 2px 0px;
  text-align: center;
  margin-bottom: 2px;
}
</style>
