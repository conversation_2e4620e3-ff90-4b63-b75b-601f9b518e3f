<!-- eslint-disable vue/require-v-for-key -->
<template>
  <div class="mapWrapper">
    <div class="bayArea">
      <!-- 坐标点 -->
      <div class="pointWrapper">
        <div class="point1">
          <img
            src="../barAssets/superEd.svg"
            alt=""
            style="width: 50; height: 50px"
          />
          <div class="bg">广州超算</div>
        </div>
        <div class="point2">
          <img
            src="../barAssets/superEd.svg"
            alt=""
            style="width: 50; height: 50px"
          />
          <div class="bg">广州智算</div>
        </div>
        <div class="point3">
          <img
            src="../barAssets/superEd.svg"
            alt=""
            style="width: 50; height: 50px"
          />
          <div class="bg">联通云ECS</div>
        </div>
        <div class="point4">
          <img
            src="../assets/superEd.svg"
            alt=""
            style="width: 50; height: 50px"
          />
          <div class="bg" style="position: relative">鹏城云脑Ⅱ</div>
        </div>
        <div class="point5">
          <img
            src="../barAssets/superEd.svg"
            alt=""
            style="width: 50; height: 50px"
          />
          <div class="bg">横琴寒武纪</div>
        </div>
        <div class="point6">
          <div class="scale">
            <img
              src="../barStatic/screen1/shuniu.svg"
              alt=""
              style="width: 100px; height: 100px"
            />
          </div>
          <div class="bg" style="position: relative; top: -60px; left: 75px">
            大湾区枢纽集群(韶关)
          </div>
        </div>
        <div class="point7">
          <img
            src="../assets/superEd.svg"
            alt=""
            style="width: 50; height: 50px"
          />
          <div class="bg" style="position: relative">深圳超算</div>
        </div>
        <div class="dot1"><div class="beBuilt">清远(待建设)</div></div>
        <div class="dot2"><div class="beBuilt">河源(待建设)</div></div>
        <div class="dot3"><div class="beBuilt">香港(待接入)</div></div>
      </div>
      <!-- 中国地图飞线图 -->
      <dv-flyline-chart-enhanced
        :config="config"
        style="width: 100%; height: 100%"
        v-if="computing"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: 'bayArea',
  props: {
    show: {
      type: Number
    },
    desMap: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    show: {}
  },
  data () {
    return {
      name: '',
      showTitle: true,
      tipName: false,
      x: undefined,
      y: undefined,
      computing: true,
      config: {
        points: [
          {
            name: '广州超算',
            coordinate: [0.55, 0.39],
            halo: {
              show: true,
              radius: 80,
              color: '#006400'
            }
          },
          {
            name: '广州智算',
            coordinate: [0.5, 0.43],
            halo: {
              show: true,
              radius: 80,
              color: '#006400'
            }
          },
          {
            name: '联通云ECS',
            coordinate: [0.52, 0.22],
            halo: {
              show: true,
              radius: 80,
              color: '#006400'
            }
          },
          {
            name: '鹏城云脑Ⅱ',
            coordinate: [0.562, 0.53],
            halo: {
              show: true,
              radius: 80,
              color: '#006400'
            }
          },
          {
            name: '深圳超算',
            coordinate: [0.59, 0.512],
            halo: {
              show: true,
              radius: 80,
              color: '#006400'
            }
          },
          {
            name: '横琴寒武纪',
            coordinate: [0.482, 0.585],
            halo: {
              show: true,
              radius: 80,
              color: '#006400'
            }
          },
          {
            name: '清远',
            coordinate: [0.47, 0.275],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '河源',
            coordinate: [0.703, 0.325],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '香港',
            coordinate: [0.62, 0.58],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '大湾区枢纽集群(韶关)',
            coordinate: [0.58, 0.18],
            halo: {
              show: false,
              radius: 120,
              color: '#006400'
            }
          }
        ],
        lines: [
          {
            source: '大湾区枢纽集群(韶关)',
            target: '广州超算',
            width: 8,
            color: 'rgb(247, 181, 1)',
            duration: [30, 40],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '广州智算',
            width: 8,
            color: 'rgb(247, 181, 1)',
            duration: [30, 40],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '联通云ECS',
            width: 8,
            color: 'rgb(247, 181, 1)',
            duration: [30, 40],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '鹏城云脑Ⅱ',
            width: 8,
            color: 'rgb(247, 181, 1)',
            duration: [30, 40],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '深圳超算',
            width: 8,
            color: 'rgb(247, 181, 1)',
            duration: [30, 40],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '横琴寒武纪',
            width: 8,
            color: 'rgb(247, 181, 1)',
            duration: [30, 40],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '清远',
            width: 4,
            color: '#00FF00',
            duration: [30, 40],
            orbitColor: 'rgba(0,255,0,0.6)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '河源',
            width: 4,
            color: '#00FF00',
            duration: [30, 40],
            orbitColor: 'rgba(0,255,0,0.6)'
          },
          {
            source: '大湾区枢纽集群(韶关)',
            target: '香港',
            width: 4,
            color: '#00FF00',
            duration: [30, 40],
            orbitColor: 'rgba(0,255,0,0.6)'
          }
        ],
        k: 0.2,
        curvature: 5
      }
    }
  },
  created () {},
  mounted () {
    if (this.show == '4' || this.show == '1') {
      this.showTitle = false
    } else {
      this.showTitle = true
    }
  },
  methods: {}
}
</script>
<style scoped>
.tipTitle {
  font-size: 18px;
  color: yellow;
  font-weight: 800;
}

.tipTitle:hover {
  cursor: pointer;
}

.colorType1 {
  font-size: 10px;
  color: rgba(243, 202, 69, 0.95);
}

.colorType2 {
  font-size: 10px;
  color: rgba(30, 231, 231, 0.95);
}

.colorType3 {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.95);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 2s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.district {
  color: #fff;
  z-index: 1;
  position: absolute;
  font-weight: 600;
  font-size: 16px;
  opacity: 0.6;
}

.north {
  top: 315px;
  left: 650px;
}

.east {
  top: 470px;
  left: 710px;
}

.westsouth {
  top: 480px;
  left: 470px;
}

.south {
  top: 535px;
  left: 590px;
}

.middle {
  top: 408px;
  left: 625px;
}
@keyframes change {
  from {
    /* background-color: yellow; */
    transform: translate(-50%, -50%) scale(1);
  }

  to {
    /* background-color: red; */
    transform: translate(-50%, -50%) scale(1.5);
  }
}

.Point {
  position: absolute;
  width: 500px;
  height: 250px;
  background-image: url("../barAssets/ninePoint.svg");
  background-repeat: no-repeat;
  left: 315px;
  bottom: 65px;
  background-size: 110% 100%;
}

.bayArea {
  height: 950px;
  background-image: url("../barStatic/screen1/guangdong.png");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 100%;
  min-width: 1100px;
  background-position-y: 80px;
  z-index: 100;
}

.mapWrapper {
  /* max-height: 800px;
  overflow: hidden; */
  width: 1100px;
  margin: 0 auto;
}

.districtWrapper,
.pointWrapper {
  position: relative;
  top: 40px;
  left: 40px;
}

.pcStyle {
  top: 600px;
  left: 650px;
  position: absolute;
  color: yellow;
  font-weight: 800;
  font-size: 18px;
}

.gzStyle {
  top: 540px;
  left: 590px;
  position: absolute;
  color: white;
  font-weight: 800;
  font-size: 18px;
}

.jnStyle {
  top: 328px;
  left: 710px;
  position: absolute;
  color: white;
  font-weight: 800;
  font-size: 18px;
}

.zkdStyle {
  top: 420px;
  left: 610px;
  position: absolute;
  color: white;
  font-weight: 800;
  font-size: 18px;
}

.SDStyle {
  top: 430px;
  left: 750px;
  position: absolute;
  color: #00c7ff;
  font-weight: 800;
  font-size: 14px;
}

.MPLSStyle {
  top: 472px;
  left: 640px;
  position: absolute;
  color: #ff7272;
  font-weight: 800;
  font-size: 14px;
}

.StyleWrapper {
  position: relative;
}

.dot {
  position: absolute;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  /* background: #000; */
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.pcDot {
  top: 573px;
  left: 671px;
  position: absolute;
  background-color: yellow;
}

.gzDot {
  top: 564px;
  left: 643px;
  position: absolute;
  background-color: white;
}

.jnDot {
  top: 353px;
  left: 728px;
  position: absolute;
  background-color: #00c7ff;
}

.zkdDot {
  top: 420px;
  left: 712px;
  position: absolute;
  background-color: #ff7272;
}

.commonPoint {
  width: 15px;
  height: 15px;
  background-image: url("../barAssets/dot.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.point1 {
  position: absolute;
  top: 290px;
  left: 555px;
  color: #ffd408;
  font-size: 16px;
  font-weight: 800;
  z-index: 10;
}
.point2 {
  position: absolute;
  top: 325px;
  left: 495px;
  color: #ffd408;
  font-size: 16px;
  font-weight: 800;
  z-index: 10;
}
.point3 {
  position: absolute;
  top: 130px;
  left: 515px;
  color: #ffd408;
  font-size: 16px;
  font-weight: 800;
  z-index: 10;
}
.point4 {
  position: absolute;
  top: 430px;
  left: 562px;
  color: #ffd408;
  font-size: 16px;
  font-weight: 800;
  z-index: 10;
}
.point5 {
  position: absolute;
  top: 480px;
  left: 475px;
  color: #ffd408;
  font-size: 16px;
  font-weight: 800;
  z-index: 10;
}
.point6 {
  position: absolute;
  top: 60px;
  left: 550px;
  color: #ffd408;
  font-size: 16px;
  font-weight: 800;
  z-index: 10;
}
.point7 {
  position: absolute;
  top: 410px;
  left: 590px;
  color: #ffd408;
  font-size: 16px;
  font-weight: 800;
  z-index: 10;
}
.scale {
  /* animation: change 1.5s infinite; */
  /* position: relative;
  top: 65px;
  left: 50px; */
}
@keyframes change {
  from {
    /* background-color: yellow; */
    transform: translate(-50%, -50%) scale(0.5);
  }

  to {
    /* background-color: red; */
    transform: translate(-50%, -50%) scale(1);
  }
}
.cdPoint {
  position: absolute;
  top: 445px;
  left: 490px;
}

.nxPoint {
  position: absolute;
  top: 328px;
  left: 540px;
}

.bjPoint {
  position: absolute;
  top: 275px;
  left: 700px;
}

.fzPoint {
  position: absolute;
  top: 453px;
  left: 776px;
}

.compute {
  position: relative;
  top: -945px;
  left: 60px;
}
.dot1 {
  position: absolute;
  width: 15px;
  height: 15px;
  background-image: url(../barAssets/dot.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  left: 470px;
  top: 215px;
}
.dot2 {
  position: absolute;
  width: 15px;
  height: 15px;
  background-image: url(../barAssets/dot.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  left: 726px;
  top: 260px;
}
.dot3 {
  position: absolute;
  width: 15px;
  height: 15px;
  background-image: url(../barAssets/dot.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  left: 635px;
  top: 500px;
}
.bg {
  /* 矩形 9 */

  /* width: 182px;
  height: 70px; */
  background: linear-gradient(
    180deg,
    rgba(5, 16, 67, 0.78),
    rgba(26, 22, 26, 0) 100%
  );
  padding: 5px 15px 5px 15px;
}
.beBuilt {
  width: 100px;
  position: absolute;
  top: 20px;
  right: -50px;
  color: #fff;
  font-size: 16px;
  font-weight: 800;
}
</style>
