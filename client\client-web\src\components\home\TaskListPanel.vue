<template>
  <div class="task-list-panel">
    <div class="panel-header">
      <el-row class="linear-gradient">
        <el-col :span="19">
          <el-row align="middle" type="flex">
            <el-col :span="3">
              <div class="arrow"></div>
            </el-col>
            <el-col :span="16">
              <div class="title1">仿真任务列表</div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="5" style="text-align: right; padding-right: 15px;">
          <el-button
            size="small"
            type="primary"
            @click="handleAddTask"
            :disabled="disabled"
            class="add-task-btn"
          >新增任务
          </el-button>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <TaskTable
        ref="taskTable"
        :loading="loading"
        :disabled="disabled"
        @row-click="handleRowClick"
        @refresh="handleRefresh"
      />
    </div>
  </div>
</template>

<script>
import TaskTable from '@/components/Table'

/**
 * 任务列表面板组件
 * 负责显示任务列表和提供新增任务功能
 */
export default {
  name: 'TaskListPanel',

  components: {
    TaskTable
  },

  props: {
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      // 组件内部状态
    }
  },

  mounted () {
    this.initTaskList()
  },

  methods: {
    /**
     * 初始化任务列表
     */
    initTaskList () {
      if (this.$refs.taskTable) {
        this.$refs.taskTable.init()
      }
    },

    /**
     * 处理新增任务
     */
    handleAddTask () {
      if (this.disabled) return

      this.$emit('add-task')
    },

    /**
     * 处理行点击
     */
    handleRowClick (row) {
      if (this.disabled) return

      this.$emit('row-click', row)
    },

    /**
     * 处理刷新
     */
    handleRefresh () {
      this.$emit('refresh')
    },

    /**
     * 刷新任务列表
     */
    refreshTaskList () {
      if (this.$refs.taskTable) {
        this.$refs.taskTable.init()
      }
    }
  }
}
</script>

<style scoped>
.task-list-panel {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.panel-header {
  margin-bottom: 15px;
}

.linear-gradient {
  background: linear-gradient(90deg, rgba(50, 197, 255, 0.3) 0%, rgba(50, 197, 255, 0.1) 100%);
  padding: 10px;
  border-radius: 4px;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 8px solid #32c5ff;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  margin-top: 2px;
}

.title1 {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  line-height: 20px;
}

.add-task-btn {
  background: linear-gradient(90deg, #32c5ff 0%, #1890ff 100%);
  border: none;
  border-radius: 4px;
  color: #fff;
  font-size: 12px;
  padding: 6px 12px;
  transition: all 0.3s ease;
}

.add-task-btn:hover {
  background: linear-gradient(90deg, #40d9ff 0%, #40a9ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(50, 197, 255, 0.3);
}

.add-task-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.table-container {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 10px;
  min-height: 300px;
}

/* 深度选择器，用于修改子组件样式 */
:deep(.el-table) {
  background: transparent;
  color: #fff;
}

:deep(.el-table th) {
  background: rgba(50, 197, 255, 0.2);
  color: #fff;
  border-bottom: 1px solid rgba(50, 197, 255, 0.3);
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-table tr:hover) {
  background: rgba(50, 197, 255, 0.1);
}

:deep(.el-table__empty-text) {
  color: rgba(255, 255, 255, 0.6);
}

:deep(.el-pagination) {
  text-align: center;
  margin-top: 15px;
}

:deep(.el-pagination .el-pager li) {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-pagination .el-pager li:hover) {
  background: rgba(50, 197, 255, 0.3);
}

:deep(.el-pagination .el-pager li.active) {
  background: #32c5ff;
  color: #fff;
}

:deep(.el-pagination button) {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-pagination button:hover) {
  background: rgba(50, 197, 255, 0.3);
}

:deep(.el-pagination button:disabled) {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.3);
}
</style>
