<template>
  <div :id="id" style="width: 100%; height: 235px" ref="echart"></div>
</template>
<script>
import { nodeTask } from '@/barApi/screenService.js'
import { compare } from '@/barUtils/index.js'
export default {
  name: 'histogram',
  data () {
    return {
      timer: null,
      timer2: null,
      Data: {
        waitTaskNum: [],
        runningTaskNum: [],
        succeedTaskNum: [],
        centerName: []
      },
      barWidth: 15
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  created () {
    nodeTask().then((res) => {
      if (res) {
        delete res.data
        res.shaoGuanAiCentersTask.forEach((item) => {
          item.total =
            item.waitTaskNum + item.runningTaskNum + item.succeedTaskNum
        })
        res.shaoGuanAiCentersTask.sort(this.compare('total'))
        this.Data = {
          waitTaskNum: [],
          runningTaskNum: [],
          succeedTaskNum: [],
          centerName: []
        }
        res.shaoGuanAiCentersTask.forEach((item) => {
          this.Data.centerName.push(item.centerName)
          this.Data.runningTaskNum.push(item.runningTaskNum)
          this.Data.succeedTaskNum.push(item.succeedTaskNum)
          this.Data.waitTaskNum.push(item.waitTaskNum)
        })
        this.show()
      }
    })
  },
  mounted () {
    window.addEventListener('resize', this.resize)
    window.addEventListener('visibilitychange', this.Refresh)
    this.$nextTick(() => {
      this.drawLine()
    })
    this.timer2 = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('visibilitychange', this.Refresh)
    clearInterval(this.timer)
    this.timer = null
    clearInterval(this.timer2)
    this.timer2 = null
  },
  // destroyed() {
  //   clearInterval(this.timer);
  // },
  methods: {
    compare (val) {
      return compare(val)
    },
    drawLine () {
      if (this.myChart) {
        this.myChart && this.myChart.dispose()
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        title: {
          text:
            this.Data.centerName.length === 0 &&
            this.Data.runningTaskNum.length === 0 &&
            this.Data.succeedTaskNum.length === 0 &&
            this.Data.waitTaskNum.length === 0
              ? '暂无数据'
              : '',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10] // 位置
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            show: false
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        legend: {
          right: '80',
          itemWidth: 15,
          textStyle: { color: '#fff' },
          itemWidth: 25,
          itemHeight: 8
        },
        grid: {
          right: '5%',
          bottom: '25%',
          top: '12%'
        },
        xAxis: {
          type: 'category',
          data: this.Data.centerName,
          axisLine: {
            show: true
          },
          axisLabel: {
            show: true,
            interval: 0,
            rotate: 45,
            fontSize: '10',
            lineHeight: 40,
            color: '#fff',
            fontWeight: 'bold',
            fontFamily: 'Microsoft YaHei',
            formatter: function (value, index) {
              if (value.length > 7) {
                return value.substr(0, 7) + '...'
              } else {
                return value
              }
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          // offset: this.config.unit.length > 10 ? -40 : 0,
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              type: 'solid',
              color: 'rgba(255,255,255,0.2)'
            }
          },
          name: '',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: true,
            fontSize: '8',
            color: '#fff'
          },
          scale: true,
          min: 0,
          splitNumber: 5
        },
        series: [
          {
            name: '已完成',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.succeedTaskNum,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(24, 144, 255,0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(24, 144, 255)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          {
            name: '运行中',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.runningTaskNum,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(30, 231, 231,0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(30, 231, 231)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          {
            name: '正在排队',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.waitTaskNum,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255,199,0,0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(255,199,0)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 100
        },
        animationEasing: 'backln'
      }
      option && this.myChart.setOption(option)
    },
    show () {
      clearInterval(this.timer)
      const Data = {
        centerName: [],
        runningTaskNum: [],
        succeedTaskNum: [],
        waitTaskNum: []
      }
      clearInterval(this.timer)
      const TempData = JSON.parse(JSON.stringify(this.Data))
      Data.centerName = TempData.centerName
      Data.runningTaskNum = TempData.runningTaskNum
        ? TempData.runningTaskNum
        : []
      Data.succeedTaskNum = TempData.succeedTaskNum
        ? TempData.succeedTaskNum
        : []
      Data.waitTaskNum = TempData.waitTaskNum ? TempData.waitTaskNum : []
      if (TempData.centerName.length <= 5) {
        this.drawLine()
      } else {
        this.Data.centerName = Data.centerName.slice(0, 5)
        this.Data.runningTaskNum = Data.runningTaskNum.slice(0, 5)
        this.Data.succeedTaskNum = Data.succeedTaskNum.slice(0, 5)
        this.Data.waitTaskNum = Data.waitTaskNum.slice(0, 5)
        this.drawLine()
        this.timer = setInterval(() => {
          const centerName = Data.centerName.shift()
          const runningTaskNum = Data.runningTaskNum.shift()
          const succeedTaskNum = Data.succeedTaskNum.shift()
          const waitTaskNum = Data.waitTaskNum.shift()
          Data.centerName.push(centerName)
          Data.runningTaskNum.push(runningTaskNum)
          Data.succeedTaskNum.push(succeedTaskNum)
          Data.waitTaskNum.push(waitTaskNum)
          this.Data.centerName = Data.centerName.slice(0, 5)
          this.Data.runningTaskNum = Data.runningTaskNum.slice(0, 5)
          this.Data.succeedTaskNum = Data.succeedTaskNum.slice(0, 5)
          this.Data.waitTaskNum = Data.waitTaskNum.slice(0, 5)
          this.drawLine()
        }, 3000)
      }
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    }
  }
}
</script>
