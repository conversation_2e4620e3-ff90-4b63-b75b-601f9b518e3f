export function formatSize (size) {
  if (size.indexOf('m') !== -1) {
    size = size.substring(0, size.length - 1)
    size = size * 0.001
    return size
  } else if (size.indexOf('Ki') !== -1) {
    size = size.substring(0, size.length - 2)
    size = size * 1024
    return size
  } else if (size.indexOf('Mi') !== -1) {
    size = size.substring(0, size.length - 2)
    size = size * 1024 * 1024
    return size
  } else if (size.indexOf('Gi') !== -1) {
    size = size.substring(0, size.length - 2)
    size = size * 1024 * 1024 * 1024
    return size
  } else if (size.indexOf('Ti') !== -1) {
    size = size.substring(0, size.length - 2)
    size = size * 1024 * 1024 * 1024 * 1024
    return size
  } else {
    return size
  }
}
// 对象某一项进行排序
export function order (a, b) {
  if (a.computerPower > b.computerPower) {
    return -1
  } else if (a.computerPower < b.computerPower) {
    return 1
  } else if (a.computerPower == b.computerPower) {
    return 0
  }
}
export function compare (property) {
  return function (a, b) {
    var value1 = a[property]
    var value2 = b[property]
    return value2 - value1
  }
}
/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime (time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime (time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj (url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}
export function filterData (date) {
  const data = date.date
  if (data.indexOf('2022.01.31') != -1 || data.indexOf('2022.02.28') != -1 || data.indexOf('2022.03.31') != -1 || data.indexOf('2022.04.30') != -1 || data.indexOf('2022.05.31') != -1 || data.indexOf('2022.06.30') != -1 ||
    data.indexOf('2022.07.31') != -1 || data.indexOf('2022.08.31') != -1 || data.indexOf('2022.09.30') != -1 || data.indexOf('2022.10.31') != -1 || data.indexOf('2022.11.30') != -1 || data.indexOf('2022.12.31') != -1 ||
    data.indexOf('2023.01.31') != -1 || data.indexOf('2023.02.28') != -1 || data.indexOf('2023.03.31') != -1 || data.indexOf('2023.04.30') != -1 || data.indexOf('2023.05.31') != -1 || data.indexOf('2023.06.30') != -1 ||
    data.indexOf('2023.07.31') != -1 || data.indexOf('2023.08.31') != -1 || data.indexOf('2023.09.30') != -1 || data.indexOf('2023.10.31') != -1 || data.indexOf('2023.11.30') != -1 || data.indexOf('2023.12.31') != -1) {
    return true
  } else { return false }
}
export function getDest (a) {
  switch (a) {
    case '192.168.207.34:9007':
      return '启智社区'
    case '192.168.242.49:31311':
      return '启智燧原集群'
    case 'obs.cn-north-305.tjaicc.com':
      return '天津市AI计算中心'
    case 'obs.cn-south-222.ai.pcl.cn':
      return '鹏城云脑Ⅱ'
    case '19************:31311':
      return '鹏城云计算所GPU集群'
    case 'obs.cn-northeast-227.dlaicc.com':
      return '大连智算中心'
    case '**************:31311':
      return '启智混合智算集群'
    case 'obs.cn-east-321.nbaicc.com':
      return '宁波工智能超算中心'
    case 'obs.cn-northeast-226.syaicenter.com':
      return '昇腾沈阳智算中心'
    case 'obs.cn-northwest-229.yantachaosuanzhongxin.com':
      return '昇腾西安智算中心'
    case 'obs.cn-south-278.aipcc-gz.com':
      return '昇腾广州智算中心'
    case '11276.c8befbc1301665ba2dc5b2826f8dca1e.ac.sugon.com':
      return '曙光云平台'
    case 'obs.cn-southwest-259.cqaiic.com':
      return '昇腾重庆智算中心'
    case 'WORK.starlight.nscc-gz.cn':
      return '广州超算'
    case 'GPUFS.starlight.nscc-gz.cn':
      return '广州超算'
    case 'http://paas.157-122-151-240.nip.io:30088':
      return '寒武纪横琴中心'
    case 'obs.cn-central-221.ovaijisuan.com':
      return '昇腾武汉智算中心'
    case 'obs.cn-central-231.xckpjs.com':
      return '昇腾中原智算中心'
    case 's3.openi.org.cn':
      return '启智社区'
    case 'obs.cn-southwest-228.cdzs.cn':
      return '昇腾成都智算中心'
    case '***************:31311':
      return '鹏城云脑Ⅰ'
    case '**************:31311':
      return '新疆大学智算中心'
    case '*************:31311':
      return '天数智算中心'
    case 'obs.cn-south-292.ca-aicc.com':
      return '昇腾南宁智算中心'
    case 'obs.cn-east-317.qdrgznjszx.com':
      return '昇腾青岛智算中心'
    case 'eci.paracloud.com':
      return '北京超级云计算中心'
    case '************:31311':
      return '燧原智算'
    case '**************:31311':
      return '川投绿色智算中心'
    case '**************:50011':
      return '天数云中心'
    case '36.111.150.83:31311':
      return '天翼云集群'
    default:
      return '暂无信息'
  }
}
export function formatDuring (mss) {
  mss = mss * 1000
  var days = parseInt(mss / (1000 * 60 * 60 * 24))
  days = days === 0 ? '' : days
  var hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  hours = hours === 0 ? '' : hours
  var minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))

  minutes = minutes === 0 ? '' : minutes
  var seconds = Math.round((mss % (1000 * 60)) / 1000)
  seconds = seconds === 0 ? '' : seconds
  if (days == '' && hours == '') {
    return '<1h'
  }
  if (days == '' && hours != '') { return hours + 'h' }
  if (days != '' && hours == '') { return days + 'd' } else { return days + 'd' + hours + 'h' }
}
export function formatDuring2 (mss) {
  var hours = Math.floor(mss / 3600)
  var minutes = Math.floor((mss % 3600) / 60)
  // var remainingSeconds = (seconds % 3600) % 60;

  return hours
}
export function formatDuring3 (mss) {
  if (mss < 1) {
    return mss.toFixed(2) + 's'
  }
  mss = mss * 1000
  var days = parseInt(mss / (1000 * 60 * 60 * 24))
  days = days === 0 ? '' : days + 'd'
  var hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  hours = hours === 0 ? '' : hours + 'h'
  var minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
  minutes = minutes === 0 ? '' : minutes + 'm'
  var seconds = Math.round((mss % (1000 * 60)) / 1000) + 's'
  seconds = seconds === 0 ? '' : seconds
  return days + hours + minutes + seconds
}
// export function debounce(fn, wait, immediate) {
//   let timeout; // 局部全局变量
//   return function (...args) {
//     let context = this;
//     if (timeout) clearTimeout(timeout); // 清除计时器，但是timeout本身还在，只是不会在执行

//     if (immediate) { // 总是执行第一次操作
//       let callNow = !timeout; // 第一次为true
//       // 多次操作，timeout初始化，多次触发只有当wait等待时间结束timeout才为空
//       timeout = setTimeout(function () {
//         timeout = null;
//       }, wait);
//       // 第一次为true, 执行
//       if (callNow) fn.apply(context, args);
//     } else { // 总是执行最后一次操作
//       timeout = setTimeout(function () {
//         fn.apply(context, args);
//       }, wait);
//     }
//   }
// }
export function throttle (func, limit) {
  let inThrottle
  return function (...args) {
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
