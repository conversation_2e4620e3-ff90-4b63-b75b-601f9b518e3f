<template>
  <div class="task-list-manager">
    <div class="manager-header">
      <div class="header-left">
        <div class="header-icon">
          <i class="el-icon-menu"></i>
        </div>
        <h3 class="header-title">仿真任务列表</h3>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleCreateTask"
          :disabled="disabled"
          class="create-btn"
        >
          新增任务
        </el-button>
      </div>
    </div>

    <div class="manager-content">
      <!-- 搜索和筛选 -->
      <div class="filter-section">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索任务名称或ID"
              prefix-icon="el-icon-search"
              size="small"
              clearable
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="statusFilter"
              placeholder="任务状态"
              size="small"
              clearable
              @change="handleStatusFilter"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="等待中" value="pending"></el-option>
              <el-option label="运行中" value="running"></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="已失败" value="failed"></el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="strategyFilter"
              placeholder="调度策略"
              size="small"
              clearable
              @change="handleStrategyFilter"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="负载均衡" value="load_balance"></el-option>
              <el-option label="最短时间" value="shortest_time"></el-option>
              <el-option label="最低成本" value="lowest_cost"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button
              size="small"
              icon="el-icon-refresh"
              @click="handleRefresh"
              :loading="loading"
            >
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 任务表格 -->
      <div class="table-section">
        <el-table
          :data="filteredTasks"
          :loading="loading"
          stripe
          highlight-current-row
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
          class="task-table"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column
            prop="id"
            label="任务ID"
            width="100"
            sortable
          />

          <el-table-column
            prop="name"
            label="任务名称"
            min-width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="status"
            label="状态"
            width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
                effect="dark"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="strategy"
            label="调度策略"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <span class="strategy-text">{{ getStrategyText(row.strategy) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="jobCount"
            label="作业数量"
            width="100"
            align="center"
            sortable
          />

          <el-table-column
            prop="progress"
            label="进度"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress"
                :stroke-width="6"
                :show-text="false"
                :color="getProgressColor(row.progress)"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="createTime"
            label="创建时间"
            width="160"
            sortable
          >
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="180"
            align="center"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                size="mini"
                type="primary"
                @click.stop="handleViewTask(row)"
                :disabled="disabled"
              >
                查看
              </el-button>
              <el-button
                size="mini"
                type="warning"
                @click.stop="handleEditTask(row)"
                :disabled="disabled || row.status === 'running'"
              >
                编辑
              </el-button>
              <el-button
                size="mini"
                type="danger"
                @click.stop="handleDeleteTask(row)"
                :disabled="disabled || row.status === 'running'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalTasks"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskListManager',

  props: {
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    },

    // 任务列表数据
    tasks: {
      type: Array,
      default: () => []
    }
  },

  data () {
    return {
      // 搜索关键词
      searchKeyword: '',

      // 状态筛选
      statusFilter: '',

      // 策略筛选
      strategyFilter: '',

      // 分页
      currentPage: 1,
      pageSize: 20,

      // 选中的任务
      selectedTasks: []
    }
  },

  computed: {
    // 过滤后的任务列表
    filteredTasks () {
      let filtered = [...this.tasks]

      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(task =>
          task.name.toLowerCase().includes(keyword) ||
          task.id.toString().includes(keyword)
        )
      }

      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(task => task.status === this.statusFilter)
      }

      // 策略过滤
      if (this.strategyFilter) {
        filtered = filtered.filter(task => task.strategy === this.strategyFilter)
      }

      // 分页
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize

      return filtered.slice(start, end)
    },

    // 总任务数
    totalTasks () {
      return this.tasks.length
    }
  },

  methods: {
    /**
     * 处理创建任务
     */
    handleCreateTask () {
      this.$emit('create-task')
    },

    /**
     * 处理搜索
     */
    handleSearch () {
      this.currentPage = 1
    },

    /**
     * 处理状态筛选
     */
    handleStatusFilter () {
      this.currentPage = 1
    },

    /**
     * 处理策略筛选
     */
    handleStrategyFilter () {
      this.currentPage = 1
    },

    /**
     * 处理刷新
     */
    handleRefresh () {
      this.$emit('refresh')
    },

    /**
     * 处理行点击
     */
    handleRowClick (row) {
      this.$emit('row-click', row)
    },

    /**
     * 处理选择变化
     */
    handleSelectionChange (selection) {
      this.selectedTasks = selection
      this.$emit('selection-change', selection)
    },

    /**
     * 处理查看任务
     */
    handleViewTask (task) {
      this.$emit('view-task', task)
    },

    /**
     * 处理编辑任务
     */
    handleEditTask (task) {
      this.$emit('edit-task', task)
    },

    /**
     * 处理删除任务
     */
    handleDeleteTask (task) {
      this.$confirm('确定要删除这个任务吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete-task', task)
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 处理页面大小变化
     */
    handleSizeChange (size) {
      this.pageSize = size
      this.currentPage = 1
    },

    /**
     * 处理当前页变化
     */
    handleCurrentChange (page) {
      this.currentPage = page
    },

    /**
     * 获取状态类型
     */
    getStatusType (status) {
      const typeMap = {
        pending: 'info',
        running: 'warning',
        completed: 'success',
        failed: 'danger'
      }
      return typeMap[status] || 'info'
    },

    /**
     * 获取状态文本
     */
    getStatusText (status) {
      const textMap = {
        pending: '等待中',
        running: '运行中',
        completed: '已完成',
        failed: '已失败'
      }
      return textMap[status] || '未知'
    },

    /**
     * 获取策略文本
     */
    getStrategyText (strategy) {
      const textMap = {
        load_balance: '负载均衡',
        shortest_time: '最短时间',
        lowest_cost: '最低成本'
      }
      return textMap[strategy] || '未知'
    },

    /**
     * 获取进度颜色
     */
    getProgressColor (progress) {
      if (progress < 30) return '#ff4d4f'
      if (progress < 70) return '#faad14'
      return '#52c41a'
    },

    /**
     * 格式化时间
     */
    formatTime (time) {
      if (!time) return '-'
      const date = new Date(time)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.task-list-manager {
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.9) 0%, rgba(0, 30, 60, 0.9) 100%);
  border: 1px solid rgba(50, 197, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(50, 197, 255, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #32c5ff 0%, #1890ff 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  box-shadow: 0 4px 12px rgba(50, 197, 255, 0.3);
}

.header-icon i {
  font-size: 20px;
  color: #fff;
}

.header-title {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.create-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border: none;
  border-radius: 6px;
  color: #fff;
  font-weight: bold;
  transition: all 0.3s ease;
}

.create-btn:hover {
  background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.manager-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-section {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.table-section {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.task-table {
  background: transparent;
}

.strategy-text {
  color: #32c5ff;
  font-weight: 500;
}

.progress-text {
  color: #fff;
  font-size: 12px;
  margin-left: 8px;
}

.pagination-section {
  margin-top: 20px;
  text-align: center;
}

/* 深度选择器，用于修改子组件样式 */
:deep(.el-input__inner) {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(50, 197, 255, 0.3);
  color: #fff;
}

:deep(.el-input__inner:focus) {
  border-color: #32c5ff;
}

:deep(.el-input__prefix) {
  color: rgba(255, 255, 255, 0.6);
}

:deep(.el-select .el-input__inner) {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(50, 197, 255, 0.3);
  color: #fff;
}

:deep(.el-select-dropdown) {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(50, 197, 255, 0.3);
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #fff;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(50, 197, 255, 0.2);
}

:deep(.el-table) {
  background: transparent;
  color: #fff;
}

:deep(.el-table th) {
  background: rgba(50, 197, 255, 0.2);
  color: #fff;
  border-bottom: 1px solid rgba(50, 197, 255, 0.3);
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

:deep(.el-table tr:hover) {
  background: rgba(50, 197, 255, 0.1);
}

:deep(.el-table__empty-text) {
  color: rgba(255, 255, 255, 0.6);
}

:deep(.el-table tr.current-row) {
  background: rgba(50, 197, 255, 0.2);
}

:deep(.el-pagination) {
  color: #fff;
}

:deep(.el-pagination .el-pager li) {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-pagination .el-pager li:hover) {
  background: rgba(50, 197, 255, 0.3);
}

:deep(.el-pagination .el-pager li.active) {
  background: #32c5ff;
  color: #fff;
}

:deep(.el-pagination button) {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-pagination button:hover) {
  background: rgba(50, 197, 255, 0.3);
}

:deep(.el-pagination button:disabled) {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.3);
}

:deep(.el-progress-bar__outer) {
  background: rgba(255, 255, 255, 0.2);
}

:deep(.el-tag) {
  border: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .manager-header {
    flex-direction: column;
    gap: 15px;
  }

  .header-left {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .filter-section .el-row {
    flex-direction: column;
    gap: 10px;
  }

  .filter-section .el-col {
    width: 100%;
  }
}
</style>
