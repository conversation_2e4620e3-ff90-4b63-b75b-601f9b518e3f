/**
 * 对话框混入 - 封装通用的对话框操作逻辑
 */
import { Message } from 'element-ui'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      dialogVisible: false,
      loading: false
    }
  },
  watch: {
    visible (val) {
      this.dialogVisible = val
    },
    dialogVisible (val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    /**
     * 处理对话框关闭
     */
    handleClose () {
      // 如果有表单引用，重置表单
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.dialogVisible = false
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    /**
     * 显示确认对话框
     * @param {Object} options - 配置选项
     * @returns {Promise} - 确认Promise
     */
    showConfirm (options = {}) {
      const {
        title = '提示',
        message = '确认执行此操作?',
        type = 'warning',
        confirmButtonText = '确定',
        cancelButtonText = '取消',
        customClass = 'confirm-dialog'
      } = options

      return this.$confirm(message, title, {
        confirmButtonText,
        cancelButtonText,
        type,
        customClass
      })
    },

    /**
     * 成功消息提示
     * @param {String} message - 消息内容
     */
    showSuccess (message) {
      Message.success(message)
    },

    /**
     * 错误消息提示
     * @param {String} message - 消息内容
     */
    showError (message) {
      Message.error(message)
    }
  }
}
