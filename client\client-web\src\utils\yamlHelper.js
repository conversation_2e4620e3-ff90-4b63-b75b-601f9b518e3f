/**
 * YAML处理助手函数
 */
import { generateYaml } from '@/api/yamlService'

/**
 * 准备任务数据，用于YAML生成和表单提交
 * @param {Object} taskForm - 任务表单数据
 * @param {Array} computingCenters - 计算中心列表
 * @returns {Object|null} - 格式化后的API请求数据或null（如果验证失败）
 */
export function prepareTaskData (taskForm, computingCenters) {
  // 验证是否有计算中心
  if (!computingCenters || computingCenters.length === 0) {
    return null
  }

  try {
    // 保持原有的 useJsonFile 设置，不强制修改
    const formData = { ...taskForm }

    // 构建c2netComputingCenterList
    const c2netComputingCenterList = computingCenters.map(center => ({
      c2netComputingCenterSpecification: {
        nodeSpecification: {
          nGBMemoriesPerGraphicsCard: center.nGBMemoriesPerGraphicsCard,
          nCpus: center.nCpus,
          nGBMemories: center.nGBMemories,
          nGraphicsCards: center.nGraphicsCards,
          graphicsCardType: center.graphicsCardType
        },
        nNodes: center.nNodes,
        pOPS: center.pOPS,
        name: center.name
      },
      nCopiedC2netComputingCenters: 1
    }))

    // 构建scheduleConfig对象
    const scheduleConfig = {
      cycleSchedule: formData.cycleSchedule,
      timeResolution: formData.timeResolution,
      scheduleType: formData.scheduleType,
      mandatoryComputingCenterSchedule: false,
      heteroComputingCenterSchedule: false,
      heteroComputingCenterConversionRatioList: [
        {
          graphicsCardType: 'A100',
          nCpus: 8,
          runDurationTime: 1
        }
      ]
    }

    // 根据选择的调度策略设置相应的字段
    switch (formData.scheduleType) {
      case '本地调度':
        scheduleConfig.mandatoryComputingCenterSchedule = true
        scheduleConfig.heteroComputingCenterSchedule = false
        break
      case '同构调度':
        scheduleConfig.mandatoryComputingCenterSchedule = false
        scheduleConfig.heteroComputingCenterSchedule = false
        break
      case '异构调度':
        scheduleConfig.mandatoryComputingCenterSchedule = false
        scheduleConfig.heteroComputingCenterSchedule = true
        break
    }

    // 构建c2netJobConfig对象
    const c2netJobConfig = {
      useJsonFile: formData.useJsonFile !== undefined ? formData.useJsonFile : false
    }

    // 当不使用JSON文件时，必须提供c2netJobGroupList
    if (!c2netJobConfig.useJsonFile) {
      c2netJobConfig.c2netJobGroupList = formData.c2netJobGroupList || []
    } else {
      c2netJobConfig.jsonFileList = formData.jsonFileList || [] // 确保是数组，不是null
    }

    // 构建networkConfig对象
    const networkConfig = {
      construct: formData.construct,
      accelerator: formData.accelerator,
      monitor: false,
      topologyTpye: formData.topologyTpye
    }

    // 最终构建符合API格式的请求数据
    return {
      c2netComputingCenterList,
      c2netJobConfig,
      scheduleConfig,
      networkConfig
    }
  } catch (error) {
    console.error('准备数据过程中出错:', error)
    return null
  }
}

/**
 * 预览YAML内容
 * @param {Object} apiRequestData - API请求数据
 * @param {Object} options - 选项
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @param {Function} options.onComplete - 完成回调
 * @returns {Promise} - 请求Promise
 */
export function previewYamlContent (apiRequestData, options = {}) {
  const { onSuccess, onError, onComplete } = options

  if (!apiRequestData) {
    if (onError) onError(new Error('无效的请求数据'))
    return Promise.reject(new Error('无效的请求数据'))
  }

  // 调用API获取预览，传递preview=true参数
  return generateYaml(apiRequestData, { preview: true })
    .then(response => {
      if (onSuccess) onSuccess(response)
      return response
    })
    .catch(error => {
      console.error('生成YAML失败:', error)
      if (onError) onError(error)
      return Promise.reject(error)
    })
    .finally(() => {
      if (onComplete) onComplete()
    })
}
