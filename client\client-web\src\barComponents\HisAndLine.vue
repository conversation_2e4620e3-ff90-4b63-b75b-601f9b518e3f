<template>
  <div :id="id" style="width: 100%; height: 235px" ref="echart"></div>
</template>
<script>
export default {
  name: 'histogram',
  data () {
    return {
      timer: null,
      Data: { unused: [], used: [], xData: [] },
      barWidth: 35
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        const TempData = JSON.parse(JSON.stringify(newValue))
        this.Data.used = TempData.used
        this.Data.xData = TempData.xData
        // this.barWidth = 10;
        this.drawLine()
      },
      deep: true
    }
  },
  mounted () {
    if (this.myChart) {
      this.myChart && this.myChart.dispose()
    }
    window.addEventListener('resize', () => {
      this.myChart.resize()
    })
    this.$nextTick(() => {
      this.drawLine()
    })
    window.addEventListener('visibilitychange', function () {
      if (document.visibilityState == 'visible') {
        if (this.myChart) {
          this.myChart.resize()
        }
      }
    })
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.myChart.resize)
    window.removeEventListener('visibilitychange', this.myChart.resize)
    // clearInterval(this.timer);
    // this.timer = null;
  },
  methods: {
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        title: {
          text: '',
          color: '#008B45',
          padding: [10, 0, 0, 10] // 位置
        },
        color: ['#1890ff', '#1ee7e7'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid',
              color: 'rgba(0, 0, 0, 0)'
            }
          },
          formatter: function (params) {
            params = [params[0]]
            let htmlStr = ''
            htmlStr += '<div>'
            htmlStr += '<div>'
            htmlStr += params[0].axisValue
            htmlStr += '</div>'
            htmlStr +=
              '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
              '#1890ff' +
              ';"></span>'
            htmlStr += params[0].seriesName + '：' + params[0].value
            htmlStr += '</div>'
            return htmlStr
          },
          backgroundColor: '#000033',
          color: '#fff',
          borderWidth: 0
        },
        legend: {
          right: '0',
          itemWidth: 15,
          color: '#fff',
          icon: 'circle'
        },
        grid: {
          right: '5%',
          bottom: '18%',
          top: '12%',
          left: this.config.unit.length > 7 ? '15%' : '8%'
        },
        xAxis: [
          {
            type: 'category',
            data: this.Data.xData,
            axisLine: {
              show: true
            },
            axisLabel: {
              show: true,
              interval: 1,
              fontSize: '10',
              lineHeight: 40,
              color: '#fff',
              fontFamily: 'Microsoft YaHei'
            },
            axisTick: {
              // x轴刻度相关设置
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                type: 'dotted',
                color: '#fff'
              }
            },
            name: this.config.unit,
            nameTextStyle: {
              // 关键代码
              padding:
                this.config.unit.length > 7 ? [0, 0, 0, 30] : [0, 0, 0, 0]
            },
            data: [],
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
            axisLabel: {
              show: true,
              fontSize: '8',
              fontWeight: 'bolder',
              color: '#fff'
            },
            scale: true,
            min: 0,
            splitNumber: 2
          },
          {
            type: 'value',
            splitLine: { show: false },
            axisLabel: {
              formatter: function (params) {
                return ''
              }
            }
          }
        ],
        series: [
          {
            name: this.config.status[0] ? this.config.status[0] : '',
            type: 'line',
            stack: 'total',
            // barWidth: this.barWidth,
            areaStyle: {},
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.used ? this.Data.used : [],
            smooth: true,
            symbol: 'show',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(24, 144, 255,1)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(24, 144, 255,0.3)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 100
        },
        animationEasing: 'backln'
      }
      option && this.myChart.setOption(option)
    }
  }
}
</script>
