<template>
    <div style="position: absolute; display: block; width: 190px; height:300px; right: 0px; top: 515px; pointer-events: none;">
        <div class="wrapper">
            <div :class="{ buttonStyle: true, check: type1,title:true }" ><span
                    class="img1 icon"></span><span class="text">智算中心</span></div>
            <div :class="{ buttonStyle: true, check: type2,title:true }" ><span
                    class="img2 icon"></span><span class="text">超算中心</span></div>
            <div :class="{ buttonStyle: true, check: type3,title:true }"><span
                    class="img3 icon"></span><span class="text">东数西算</span></div>
        </div>
        <!-- <div style="position: absolute; left: -1251px; top: -638px;">
            <div
                style="pointer-events: none; position: absolute; top: 638px; left: 1251px; width: 189px; height: 173px; display: block; transition: transform 600ms ease 0s;">
                <div style="top: -638px; left: -1251px; position: absolute;">
                    <div id="0a4fb2ff-6954-46c0-82ec-98f561a15f11"
                        style="position: absolute; pointer-events: auto; z-index: unset;">
                        <div class="__easyv-component" id="15043"
                            style="position: absolute; left: 1251px; top: 638px; width: 189px; height: 173px; pointer-events: none;">
                            <div
                                style="left: 1251px; top: 638px; width: 189px; height: 173px; cursor: default; pointer-events: none;">
                                <svg width="0" height="0">
                                    <defs>
                                        <linearGradient id="fillGradient_15043" x1="0" y1="0" x2="0" y2="1"
                                            gradientTransform="rotate(0 , .5 .5)">
                                            <stop offset="1" stop-color="RGBA(18,137,221,0.1)" stop-opacity="1"></stop>
                                            <stop offset="0" stop-color="RGBA(18,137,221,0.1)" stop-opacity="1"></stop>
                                        </linearGradient>
                                        <filter id="dropShadow_Blur_15043">
                                            <feMorphology operator="dilate" radius="0" in="SourceAlpha"
                                                result="thicken"></feMorphology>
                                            <feOffset dx="0" dy="0" result="offset" in="thicken"></feOffset>
                                            <feGaussianBlur in="offset" stdDeviation="0" result="blurred">
                                            </feGaussianBlur>
                                            <feFlood flood-color="#0075ff" result="glowColor"></feFlood>
                                            <feComposite in="glowColor" in2="blurred" operator="in"
                                                result="softGlow_colored_15043"></feComposite>
                                            <feMerge>
                                                <feMergeNode in="softGlow_colored_15043"></feMergeNode>
                                                <feMergeNode in="SourceGraphic"></feMergeNode>
                                            </feMerge>
                                        </filter>
                                    </defs>
                                </svg>
                                <div style="position: absolute; top: 0px; left: 0px; width: 189px; height: 173px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                style="pointer-events: none; position: absolute; top: 688px; left: 1273px; width: 15px; height: 106px; display: block; transition: transform 600ms ease 0s;">
                <div style="top: -688px; left: -1273px; position: absolute;">
                    <div style="position: absolute; width: 15px; height: 106px; left: 1273px; top: 688px;">
                        <div style="position: absolute; left: -1273px; top: -688px;">
                            <div id="group_drs81tk489" class="__easyv-group"
                                style="position: absolute; display: block; width: 15px; height: 106px; left: 1273px; top: 688px; pointer-events: none;">
                                <div style="position: absolute; left: -1273px; top: -688px;">
                                    <div
                                        style="pointer-events: none; position: absolute; top: 764px; left: 1273px; width: 15px; height: 30px; display: block; transition: transform 600ms ease 0s;">
                                        <div style="top: -764px; left: -1273px; position: absolute;">
                                            <div id="32c2ca3a-d3e9-453b-9492-753b1a6e0d5f"
                                                style="position: absolute; pointer-events: auto; z-index: unset;">
                                                <div class="__easyv-component" id="15042"
                                                    style="position: absolute; left: 1273px; top: 764px; width: 15px; height: 30px; pointer-events: none;">
                                                    <img src="../assets/statusUn.svg" class="index-module_stretch_1IItz"
                                                        style="display: block; height: 100%; mix-blend-mode: normal; cursor: default; pointer-events: none; opacity: 1;width: 30px;height: 35px;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        style="pointer-events: none; position: absolute; top: 724px; left: 1273px; width: 15px; height: 30px; display: block; transition: transform 600ms ease 0s;">
                                        <div style="top: -724px; left: -1273px; position: absolute;">
                                            <div id="0e2b69d9-3466-4d9c-a46a-42943f1f044b"
                                                style="position: absolute; pointer-events: auto; z-index: unset;">
                                                <div class="__easyv-component" id="15032"
                                                    style="position: absolute; left: 1273px; top: 724px; width: 15px; height: 30px; pointer-events: none;">
                                                    <img src="../assets/statusIng.svg"
                                                        class="index-module_stretch_1IItz"
                                                        style="display: block; height: 100%; mix-blend-mode: normal; cursor: default; pointer-events: none; opacity: 1;width: 30px;height: 35px;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        style="pointer-events: none; position: absolute; top: 688px; left: 1273px; width: 15px; height: 30px; display: block; transition: transform 600ms ease 0s;">
                                        <div style="top: -688px; left: -1273px; position: absolute;">
                                            <div id="3bf54fc9-a39f-4e41-8b02-b996229d49f6"
                                                style="position: absolute; pointer-events: auto; z-index: unset;">
                                                <div class="__easyv-component" id="15041"
                                                    style="position: absolute; left: 1273px; top: 688px; width: 15px; height: 30px; pointer-events: none;">
                                                    <img src="../assets/statusEd.svg" class="index-module_stretch_1IItz"
                                                        style="display: block; height: 100%; mix-blend-mode: normal; cursor: default; pointer-events: none; opacity: 1;width: 30px;height: 35px;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                style="pointer-events: none; position: absolute; top: 769px; left: 1307px; width: 137px; height: 26px; display: block; transition: transform 600ms ease 0s;">
                <div style="top: -769px; left: -1307px; position: absolute;">
                    <div id="340be198-bbec-45ec-9eb9-ed26fb7c991d"
                        style="position: absolute; pointer-events: auto; z-index: unset;">
                        <div class="__easyv-component" id="15040"
                            style="position: absolute; left: 1307px; top: 769px; width: 137px; height: 26px;">
                            <div style="width: 100%; height: 100%; cursor: auto;">
                                <div
                                    style="font-family: &quot;思源黑体 CN-Regular&quot;; font-size: 14px; color: rgba(255, 255, 255, 0.6); letter-spacing: 1px; line-height: 48px; text-align: left; width: 100%; font-weight: normal; position: absolute; top: 50%; transform: translateY(-50%);">
                                    <span>待接入</span> <span style="color:#fff">{{statusData.pending}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                style="pointer-events: none; position: absolute; top: 729px; left: 1307px; width: 137px; height: 26px; display: block; transition: transform 600ms ease 0s;">
                <div style="top: -729px; left: -1307px; position: absolute;">
                    <div id="3c1428e2-2d6c-4dac-a687-8b8f33b2198e"
                        style="position: absolute; pointer-events: auto; z-index: unset;">
                        <div class="__easyv-component" id="15039"
                            style="position: absolute; left: 1307px; top: 729px; width: 137px; height: 26px;">
                            <div style="width: 100%; height: 100%; cursor: auto;">
                                <div
                                    style="font-family: &quot;思源黑体 CN-Regular&quot;; font-size: 14px; color: rgba(255, 255, 255, 0.6); letter-spacing: 1px; line-height: 48px; text-align: left; width: 100%; font-weight: normal; position: absolute; top: 50%; transform: translateY(-50%);">
                                    <span>接入中</span> <span style="color:#fff">{{statusData.accessing}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                style="pointer-events: none; position: absolute; top: 692px; left: 1307px; width: 137px; height: 26px; display: block; transition: transform 600ms ease 0s;">
                <div style="top: -692px; left: -1307px; position: absolute;">
                    <div id="4580e2a3-f76e-46ef-877f-cc4c93a97ca6"
                        style="position: absolute; pointer-events: auto; z-index: unset;">
                        <div class="__easyv-component" id="15038"
                            style="position: absolute; left: 1307px; top: 692px; width: 137px; height: 26px;">
                            <div style="width: 100%; height: 100%; cursor: auto;">
                                <div
                                    style="font-family: &quot;思源黑体 CN-Regular&quot;; font-size: 14px; color: rgba(255, 255, 255, 0.6); letter-spacing: 1px; line-height: 48px; text-align: left; width: 100%; font-weight: normal; position: absolute; top: 50%; transform: translateY(-50%);">
                                    <span>已接入</span> <span style="color:#fff">{{statusData.connected}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                style="pointer-events: none; position: absolute; top: 652px; left: 1288px; width: 153px; height: 26px; display: block; transition: transform 600ms ease 0s;">
                <div style="top: -652px; left: -1288px; position: absolute;">
                    <div id="b4c47203-e13b-4caf-bae9-523d372c8603"
                        style="position: absolute; pointer-events: auto; z-index: unset;">
                    </div>
                </div>
            </div>
            <div
                style="pointer-events: none; position: absolute; top: 653px; left: 1257px; width: 30px; height: 24px; display: block; transition: transform 600ms ease 0s;">
                <div style="top: -653px; left: -1257px; position: absolute;">
                    <div style="position: absolute; width: 30px; height: 24px; left: 1257px; top: 653px;">
                        <div style="position: absolute; left: -1257px; top: -653px;">
                        </div>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</template>
<script>
export default {
  name: 'statusMap',
  props: {
    statusData: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      type1: true,
      type2: false,
      type3: false,
      type: 1
    }
  },
  mounted () {
  },
  methods: {
  },
  watch: {
    statusData: {
      handler (newValue, oldValue) {
        this.statusData = newValue
      }
    },
    type: {
      handler (newValue, oldValue) {
        switch (this.type) {
          case 1:
            this.type1 = true
            this.type2 = false
            this.type3 = false
            break
          case 2:
            this.type2 = true
            this.type1 = false
            this.type3 = false
            break
          case 3:
            this.type3 = true
            this.type2 = false
            this.type1 = false
            break
        }
      }
    }

  }
}
</script>
<style scoped>
    .img {
        width: 100%;
        height: 100%;
    }

    .title {
        color: #fff;
        line-height: 40px;
    }

    .wrapper {
        position: absolute;
        left: 25px
    }

    .icon {
        display: inline-block;
        ;
        width: 18px;
        height: 18px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 10px;
        line-height: 18px;

    }

    .text {
        display: inline-block;
        line-height: 18px;
        height: 18px;
        position: relative;
        top: -3px
    }

    .img1 {
        background-image: url('../static/screen1/intellectual.svg');

    }

    .img2 {
        background-image: url('../static/screen1/super.svg');

    }

    .img3 {
        background-image: url('../static/screen1/eandw.svg');

    }

    .buttonStyle {
        /* height: 50px; */
        color: white;
        /* line-height: 50px; */
        /* text-align: center; */
        background-color: rgba(255, 255, 255, 10%);
        cursor: pointer;
        min-width: 120px;
        margin-bottom: 10px;
    }

    .check {
        font-weight: 800;
        background-image: linear-gradient(to right, rgba(24, 144, 255, 0%),
                rgba(24, 144, 255, 10%),
                rgba(24, 144, 255, 40%));
    }
</style>
