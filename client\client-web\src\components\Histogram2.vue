<template>
  <div :id="id" style="width: 100%; height: 255px" ref="echart"></div>
</template>
<script>
import { nodeTask } from '@/api/screenService.js'
import { compare } from '@/utils/index.js'
export default {
  name: 'histogram',
  data () {
    return {
      timer: null,
      timer2: null,
      // timer2: null,
      Data: {
        waitTaskNum: [],
        runningTaskNum: [],
        succeedTaskNum: [],
        centerName: []
      },
      barWidth: 20
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  created () {},
  mounted () {
    nodeTask().then((res) => {
      if (res) {
        delete res.data
        res.shaoGuanAiCentersTask.forEach((item) => {
          item.total =
            item.waitTaskNum + item.runningTaskNum + item.succeedTaskNum
        })
        res.shaoGuanAiCentersTask.sort(this.compare('total'))
        this.Data = {
          waitTaskNum: [],
          runningTaskNum: [],
          succeedTaskNum: [],
          centerName: []
        }
        res.shaoGuanAiCentersTask.forEach((item) => {
          if (item.runningTaskNum !== 0 || item.waitTaskNum !== 0) {
            this.Data.centerName.push(item.centerName)
            this.Data.runningTaskNum.push(item.runningTaskNum)
            this.Data.succeedTaskNum.push(item.succeedTaskNum)
            this.Data.waitTaskNum.push(item.waitTaskNum)
          }
        })

        this.show()
      }
    })
    window.addEventListener('resize', this.resize)
    window.addEventListener('visibilitychange', this.Refresh)
    this.timer2 = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('visibilitychange', this.Refresh)
    clearInterval(this.timer)
    clearInterval(this.timer2)
    this.timer = null
    this.timer2 = null
  },
  methods: {
    compare (val) {
      return compare(val)
    },
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      if (!this.myChart) {
        this.myChart = this.$echarts.init(this.$refs.echart)
      }
      var option
      option = {
        title: {
          text:
            this.Data.waitTaskNum.length === 0 &&
            this.Data.runningTaskNum.length === 0 &&
            this.Data.centerName.length === 0
              ? '暂无数据'
              : '',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10]
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            show: false
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        legend: {
          right: '0',
          textStyle: { color: '#fff' },
          itemWidth: 25,
          itemHeight: 8
        },
        grid: {
          right: '5%',
          bottom: '18%',
          top: '12%'
        },
        xAxis: {
          type: 'category',
          data: this.Data.centerName,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(1, 145, 255, 0.3)' // 设置坐标轴颜色
            }
          },
          axisLabel: {
            show: true,
            interval: 0,
            fontSize: '12',
            lineHeight: 40,
            color: 'rgba(255, 255, 255,0.9)',
            fontFamily: 'Microsoft YaHei',
            fontWeight: 'bold',
            formatter: function (value) {
              if (value.length > 5) {
                return value.substr(0, 5) + '..'
              } else {
                return value
              }
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              type: 'dotted',
              color: 'rgba(1, 145, 255, 0.3)'
            }
          },
          name: '单位:个',
          data: [],
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255,0.9)'
            }
          },
          axisLabel: {
            show: true,
            fontSize: '10',
            color: 'rgba(255, 255, 255,0.9)',
            fontWeight: 'bold'
          },
          scale: true,
          min: 0,
          splitNumber: 3,
          max: function (value) {
            return value.max + 3
          }
        },
        series: [
          {
            name: '运行中',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.runningTaskNum,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(3, 118, 210,0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(3, 118, 210)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          {
            name: '等待任务',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.waitTaskNum,
            itemStyle: {
              // 柱状颜色和圆角
              color: 'rgb(41,159,209)'
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 100
        },
        animationEasing: 'backln'
      }
      option && this.myChart.setOption(option)
    },
    show () {
      clearInterval(this.timer)
      const Data = {
        centerName: [],
        runningTaskNum: [],
        succeedTaskNum: [],
        waitTaskNum: []
      }
      clearInterval(this.timer)
      const TempData = JSON.parse(JSON.stringify(this.Data))
      Data.centerName = TempData.centerName
      Data.runningTaskNum = TempData.runningTaskNum
        ? TempData.runningTaskNum
        : []
      Data.succeedTaskNum = TempData.succeedTaskNum
        ? TempData.succeedTaskNum
        : []
      Data.waitTaskNum = TempData.waitTaskNum ? TempData.waitTaskNum : []
      if (TempData.centerName.length <= 5) {
        this.drawLine()
      } else {
        this.Data.centerName = Data.centerName.slice(0, 5)
        this.Data.runningTaskNum = Data.runningTaskNum.slice(0, 5)
        this.Data.succeedTaskNum = Data.succeedTaskNum.slice(0, 5)
        this.Data.waitTaskNum = Data.waitTaskNum.slice(0, 5)
        this.drawLine()
        this.timer = setInterval(() => {
          const centerName = Data.centerName.shift()
          const runningTaskNum = Data.runningTaskNum.shift()
          const succeedTaskNum = Data.succeedTaskNum.shift()
          const waitTaskNum = Data.waitTaskNum.shift()
          Data.centerName.push(centerName)
          Data.runningTaskNum.push(runningTaskNum)
          Data.succeedTaskNum.push(succeedTaskNum)
          Data.waitTaskNum.push(waitTaskNum)
          this.Data.centerName = Data.centerName.slice(0, 5)
          this.Data.runningTaskNum = Data.runningTaskNum.slice(0, 5)
          this.Data.succeedTaskNum = Data.succeedTaskNum.slice(0, 5)
          this.Data.waitTaskNum = Data.waitTaskNum.slice(0, 5)
          this.drawLine()
        }, 3000)
      }
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    }
  }
}
</script>
