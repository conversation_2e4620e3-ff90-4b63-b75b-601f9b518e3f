<template>
  <div>
    <el-row type="flex" justify="center"> </el-row>
    <div :id="id" style="width: 100%; height: 250px" ref="echart"></div>
  </div>
</template>
<script>
import { jobDetail } from '@/api/screenService.js'
import { formatDuring2 } from '@/utils/index'
// import { filterData } from "@/utils/index";
export default {
  name: 'lineChart',
  data () {
    return {
      tableData: [],
      timer: null,
      total: 0,
      count: 0,
      Data: { xAxis: [], yAxis: [] }
    }
  },
  watch: {
    taskId (newValue, oldValue) {
      // this.getJobDetail(newValue);
      clearInterval(this.timer)
      this.timer = null
      this.getJobDetail(newValue)
      // this.query();
    }
  },
  computed: {
    taskId () {
      return this.$store.state.id
    }
  },
  mounted () {
    this.getJobDetail(this.taskId)
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    config: {
      type: Object,
      default: () => ({})
    },
    type: { type: Number }
  },
  watch: {},
  methods: {
    // getJobDetail(id) {
    //   let data = { id: id, resolution_n_hours: 10 };
    //   jobDetail(data).then((res) => {
    //     this.total = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length;
    //     if (this.total == 0) {
    //       return;
    //     }
    //     this.count = 0;
    //     this.Data.xAxis = [];
    //     this.Data.yAxis = [];
    //     res.CenterInfoToWebList.forEach((item) => {
    //       this.Data.xAxis[this.count] = this.formatDuring(
    //         item.SnapshotInfoToWebList[this.count].Time
    //       );

    //       this.Data.yAxis[this.count] =
    //         item.SnapshotInfoToWebList[this.count].SubmitJob;
    //       // this.Data.xAxis.push(item.SnapshotInfoToWebList[this.count].Time);
    //       // this.Data.yAxis.push(
    //       //   item.SnapshotInfoToWebList[this.count].SubmitJob
    //       // );
    //     });
    //     this.drawLine();
    //     let that = this;
    //     this.timer = setInterval(() => {
    //       // Data.xData = [];
    //       that.count++;
    //       if (that.count >= that.total) {
    //         that.count = 0;
    //         clearTimeout(that.timer);
    //         that.timer = null;
    //         that.getJobDetail(that.taskId);
    //       } else {
    //         res.CenterInfoToWebList.forEach((item) => {
    //           this.Data.xAxis[that.count] = this.formatDuring(
    //             item.SnapshotInfoToWebList[that.count].Time
    //           );

    //           this.Data.yAxis[that.count] =
    //             item.SnapshotInfoToWebList[that.count].SubmitJob;
    //           // this.Data.xAxis.push(item.SnapshotInfoToWebList[this.count].Time);
    //           // this.Data.yAxis.push(
    //           //   item.SnapshotInfoToWebList[this.count].SubmitJob
    //           // );
    //         });
    //         this.drawLine();
    //       }
    //     }, 5000);
    //   });
    // },
    drawLine () {
      if (this.myChart) {
        this.myChart && this.myChart.dispose()
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        title: {
          text: '',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10]
        },
        color: '#32c5ff ',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid',
              color: 'rgba(0, 0, 0, 0)'
            }
          },
          formatter: function (params) {
            params = [params[0]]
            let htmlStr = ''
            htmlStr += '<div>'
            htmlStr += '<div>'
            htmlStr += '第' + params[0].axisValue + '天'
            htmlStr += '</div>'
            htmlStr +=
              '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
              '#1890ff' +
              ';"></span>'
            htmlStr += params[0].seriesName + params[0].value
            htmlStr += '</div>'
            return htmlStr
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        grid: {
          right: '4%',
          bottom: '25%',
          top: '12%',
          left: '10%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.Data.xAxis,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(1, 145, 255, 0.3)' // 设置坐标轴颜色
            }
          },
          axisLabel: {
            show: true,
            showMaxLabel: true,
            interval: 1,
            fontSize: '12',
            lineHeight: 40,
            color: 'rgba(255, 255, 255,1)',
            fontFamily: 'Microsoft YaHei',
            fontWeight: 'normal'
          },
          axisTick: {
            // x轴刻度相关设置
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '',
          show: true,
          nameTextStyle: {
            // 关键代码
            padding: [0, 0, 0, 0]
          },
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              type: 'dotted',
              color: 'rgba(1, 145, 255, 0.3)'
            }
          },
          nameGap: 10,
          // data: [],
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255,0.9)'
            }
          },
          axisLabel: {
            show: true,
            interval: 10,
            align: 'right',
            fontSize: '10',
            fontWeight: 'bold',
            color: 'rgba(255, 255, 255,0.9)'
          },
          scale: true,
          min: 0,
          splitNumber: 4
        },
        series: [
          {
            name: '提交任务量',
            type: 'line',
            symbol: 'circle', // 数据点形状为矩形
            symbolSize: 6,
            smooth: true,
            label: {
              show: false
            },
            zlevel: 1,
            z: 1,
            data: this.Data.yAxis,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(50,197,255,0)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(22,93,255)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        legend: {
          data: ['提交任务量'], // 图例名称
          right: 185, // 调整图例位置
          top: 230, // 调整图例位置
          itemHeight: 12, // 修改icon图形大小
          textStyle: {
            // 图例文字的样式
            color: '#a1a1a1', // 图例文字颜色
            fontSize: 12 // 图例文字大小
          }
        },
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 500
        },
        animationEasing: 'backln'
      }
      option && this.myChart.setOption(option)
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    },
    formatDuring (val) {
      //  console.log(val)
      return formatDuring2(val)
    }
  }
}
</script>
<style scoped>
.button {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.row-bg {
  padding-top: 5px;
}
.check {
  color: rgb(50, 197, 255);
  font-family: 思源黑体;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0px;
}
.bg {
  box-sizing: border-box;
  background: radial-gradient(
    65% 100% at 50% 0%,
    rgba(1, 145, 255, 0.33),
    rgba(255, 1, 246, 0) 100%
  );
  border: 1px solid rgba(68, 103, 215, 0.5);
  padding: 5px;
}
.bg2 {
  border: 1px solid rgb(50, 197, 255);
}
</style>
