import { ref, computed, watch } from 'vue'
import { TaskService } from '@/services/TaskService'

/**
 * 仿真控制组合式函数
 * 封装仿真状态管理和控制逻辑
 */
export function useSimulation () {
  // ========== 响应式状态 ==========
  const currentFrame = ref(0)
  const totalFrames = ref(0)
  const isPaused = ref(true)
  const isDisabled = ref(false)
  const showPlayButton = ref(true)
  const showControlButtons = ref(false)
  const timer = ref(null)
  const runningTime = ref(0)

  // 任务数据缓存
  const mainTaskData = ref(null)
  const compareTaskData = ref(null)
  const isDataLoaded = ref(false)

  // ========== 计算属性 ==========
  const progressPercentage = computed({
    get () {
      if (currentFrame.value === 0 || totalFrames.value === 0) {
        return 0
      }
      return Number(((currentFrame.value / totalFrames.value) * 100).toFixed(0))
    },
    set (value) {
      const newFrame = Math.floor((value / 100) * totalFrames.value)
      currentFrame.value = Math.min(newFrame, totalFrames.value)
    }
  })

  const isAtEnd = computed(() => currentFrame.value >= totalFrames.value)
  const isPlaying = computed(() => !isPaused.value && timer.value !== null)

  // ========== 私有方法 ==========

  /**
   * 清理定时器
   */
  function clearTimer () {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  }

  /**
   * 重置状态
   */
  function resetState () {
    currentFrame.value = 0
    isPaused.value = true
    isDisabled.value = false
    showPlayButton.value = true
    showControlButtons.value = false
    clearTimer()
  }

  // ========== 公共方法 ==========

  /**
   * 开始仿真
   * @param {number} mainTaskId - 主任务ID
   * @param {number} compareTaskId - 对比任务ID
   * @param {number} intervalHours - 时间间隔
   * @param {Function} onFrameUpdate - 帧更新回调
   */
  async function startSimulation (mainTaskId, compareTaskId, intervalHours, onFrameUpdate) {
    try {
      // 准备开始
      isDisabled.value = true
      resetState()
      isDataLoaded.value = false

      // 获取数据
      const { mainTask, compareTask } = await TaskService.getMultipleTaskData(
        mainTaskId,
        compareTaskId,
        intervalHours
      )

      if (!mainTask) {
        throw new Error('无法获取主任务数据')
      }

      // 缓存数据
      mainTaskData.value = mainTask
      compareTaskData.value = compareTask

      // 提取基本信息
      const basicInfo = TaskService.extractBasicInfo(mainTask)
      const statisticsData = TaskService.extractStatisticsData(mainTask.CenterInfoToWebList)

      totalFrames.value = statisticsData.timeData.length
      isDataLoaded.value = true
      isDisabled.value = false

      // 开始动画
      startAnimation(onFrameUpdate, statisticsData)

      return { basicInfo, statisticsData, compareTask }
    } catch (error) {
      console.error('开始仿真失败:', error)
      isDisabled.value = false
      throw error
    }
  }

  /**
   * 启动动画定时器
   * @param {Function} onFrameUpdate - 帧更新回调
   * @param {Object} statisticsData - 统计数据
   */
  function startAnimation (onFrameUpdate, statisticsData) {
    clearTimer()
    isPaused.value = false
    showControlButtons.value = true

    timer.value = setInterval(() => {
      currentFrame.value++

      if (currentFrame.value >= totalFrames.value) {
        stopSimulation()
        return
      }

      // 调用帧更新回调
      if (onFrameUpdate) {
        onFrameUpdate(currentFrame.value, statisticsData)
      }
    }, 2000)
  }

  /**
   * 暂停仿真
   */
  function pauseSimulation () {
    clearTimer()
    showPlayButton.value = false
    isPaused.value = true
  }

  /**
   * 恢复仿真
   * @param {Function} onFrameUpdate - 帧更新回调
   */
  function resumeSimulation (onFrameUpdate) {
    if (isAtEnd.value || !isDataLoaded.value) {
      return
    }

    isPaused.value = false
    showPlayButton.value = true

    // 如果有缓存数据，从当前帧继续
    if (mainTaskData.value) {
      const statisticsData = TaskService.extractStatisticsData(mainTaskData.value.CenterInfoToWebList)

      // 立即更新当前帧
      if (onFrameUpdate) {
        onFrameUpdate(currentFrame.value, statisticsData)
      }

      // 继续动画
      startAnimation(onFrameUpdate, statisticsData)
    }
  }

  /**
   * 停止仿真
   */
  function stopSimulation () {
    clearTimer()
    currentFrame.value = totalFrames.value
    isPaused.value = true
    showControlButtons.value = false
  }

  /**
   * 跳转到指定帧
   * @param {number} frameIndex - 目标帧索引
   * @param {Function} onFrameUpdate - 帧更新回调
   */
  function seekToFrame (frameIndex, onFrameUpdate) {
    if (frameIndex < 0 || frameIndex >= totalFrames.value) {
      return
    }

    currentFrame.value = frameIndex

    if (onFrameUpdate && mainTaskData.value) {
      const statisticsData = TaskService.extractStatisticsData(mainTaskData.value.CenterInfoToWebList)
      onFrameUpdate(frameIndex, statisticsData)
    }
  }

  /**
   * 获取当前帧数据
   * @param {string} dataType - 数据类型 ('submitJob', 'pendingTime', 'resourceUsage')
   * @returns {Array} 当前帧的数据
   */
  function getCurrentFrameData (dataType) {
    if (!mainTaskData.value || currentFrame.value >= totalFrames.value) {
      return []
    }

    const statisticsData = TaskService.extractStatisticsData(mainTaskData.value.CenterInfoToWebList)
    const frameData = statisticsData.frameData[dataType]

    return frameData ? frameData[currentFrame.value] || [] : []
  }

  /**
   * 获取对比任务的当前帧数据
   * @param {string} dataType - 数据类型
   * @returns {Array} 对比任务当前帧的数据
   */
  function getCompareFrameData (dataType) {
    if (!compareTaskData.value || currentFrame.value >= totalFrames.value) {
      return []
    }

    const compareData = TaskService.processCompareTaskData(compareTaskData.value, totalFrames.value)
    if (!compareData) {
      return []
    }

    const frameData = compareData.frameData[dataType]
    return frameData ? frameData[currentFrame.value] || [] : []
  }

  /**
   * 清理资源
   */
  function cleanup () {
    clearTimer()
    mainTaskData.value = null
    compareTaskData.value = null
    isDataLoaded.value = false
  }

  // ========== 监听器 ==========
  watch(currentFrame, (newFrame) => {
    if (newFrame === totalFrames.value) {
      stopSimulation()
    }
  })

  // ========== 返回API ==========
  return {
    // 状态
    currentFrame,
    totalFrames,
    isPaused,
    isDisabled,
    showPlayButton,
    showControlButtons,
    runningTime,
    isDataLoaded,
    progressPercentage,
    isAtEnd,
    isPlaying,

    // 方法
    startSimulation,
    pauseSimulation,
    resumeSimulation,
    stopSimulation,
    seekToFrame,
    getCurrentFrameData,
    getCompareFrameData,
    cleanup
  }
}
