FROM node:12.18 AS builder

ENV PROJECT_ENV=production
ENV NODE_ENV=production

WORKDIR /app
COPY package.json package-lock.json ./
RUN npm config set registry https://registry.npmmirror.com  --global
RUN npm config get registry
RUN npm install cnpm -g
RUN npm install --dev

COPY . ./
RUN npm run build

FROM nginx:1.21.0-alpine
COPY --from=builder /app/dist/ /usr/share/nginx/html

# 设置默认环境变量
ENV BACKEND_HOST=c2net-backend-python
ENV BACKEND_PORT=8000
ENV NGINX_WORKER_PROCESSES=4

COPY nginx.conf /etc/nginx/nginx.conf.template
CMD ["/bin/sh", "-c", "envsubst '${BACKEND_HOST} ${BACKEND_PORT}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf && nginx -g 'daemon off;'"]
