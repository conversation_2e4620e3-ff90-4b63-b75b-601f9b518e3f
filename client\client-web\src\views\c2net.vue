<template>
  <div style="height: 100vh; overflow: hidden; position: relative;">
    <div class="background">
      <el-container>
        <el-header>
          <el-row justify="center" type="flex">
            <el-col :span="12">
              <div class="title">中国算力网实验场（仿真）</div>
            </el-col>
          </el-row>
        </el-header>

        <el-main>
          <el-row justify="space-around" type="flex">
            <!-- 左侧栏 - 仿真任务总览和列表 -->
            <el-col :span="7">
              <div class="showMt">
                <el-row class="linear-gradient" style="margin-bottom: 3px">
                  <el-col :span="14">
                    <el-row>
                      <el-col :span="5">
                        <div class="arrow"></div>
                      </el-col>
                      <el-col :span="19">
                        <div class="title1">仿真任务总览</div>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                <el-row justify="space-between" type="flex">
                  <el-col :span="24">
                    <div>
                      <CumulativeTask :Data="Data" :msg="msg2"></CumulativeTask>
                    </div>
                    <div>
                      <div key="demo1" ref="echart" style="width: 100%; height: 250px"></div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="showMt">
                <el-row class="linear-gradient">
                  <el-col :span="19">
                    <el-row align="middle" type="flex">
                      <el-col :span="3">
                        <div class="arrow"></div>
                      </el-col>
                      <el-col :span="16">
                        <div class="title1">仿真任务列表</div>
                      </el-col>
                    </el-row>
                  </el-col>
                  <el-col :span="5" style="text-align: right; padding-right: 15px;">
                    <div class="task-buttons-container">
                      <el-button size="small" type="primary" @click="openAddTaskDialog"
                        style="margin-right: 8px; position: relative; z-index: 1000;">新增任务
                      </el-button>
                      <el-button size="small" type="success" @click="openAddCooperativeTaskDialog"
                        style="position: relative; z-index: 1000;">新增协同任务
                      </el-button>
                    </div>
                  </el-col>
                </el-row>
                <Table ref="table" :Count="count" :Stop="Stop" :Total="total" class="index"
                  @row-click="openTaskDetailDialog"></Table>
              </div>
            </el-col>
            <el-col :span="14">
              <div>
                <el-row justify="center" type="flex">
                  <el-col :span="18">
                    <Total :msg="msg" :taskDetail="taskDetail"></Total>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <div style="max-height: 800px; overflow: hidden">
                      <chinaMap ref="chinaMap" :Count="count" :Stop="Stop" :intervalSetting="interval"
                        :taskResponse="taskIdRes" @need-job-detail="handleNeedJobDetail"></chinaMap>
                      <div class="progress">
                        <div style="width: 600px; margin: 0 auto">
                          <div style="
                              display: inline-block;
                              min-width: 150px;
                              margin-right: 10px;
                            ">
                            <el-slider v-if="showPer && intervalChange" v-model="percentage" :disabled="disable"
                              :format-tooltip="formatTooltip" class="progressStyle"
                              @change="changeProgress"></el-slider>
                          </div>
                          <div v-if="intervalChange" style="width: 80px; display: inline-block">
                            <el-button :disabled="disable" size="small" @click="start">开始
                            </el-button>
                          </div>
                          <div v-show="buttonShow && showButtom" style="width: 80px; display: inline-block">
                            <el-button :disabled="disable" size="small" @click="stop">暂停
                            </el-button>
                          </div>
                          <div v-show="!buttonShow && showButtom" style="width: 80px; display: inline-block">
                            <el-button :disabled="disable" size="small" @click="goOn">继续
                            </el-button>
                          </div>
                          <div style="display: inline-block">
                            <el-select v-if="intervalChange" v-model="value" :disabled="disable" class="interval"
                              placeholder="请选择仿真时间间隔" style="
                                width: 150px;
                                display: inline-block;
                                margin-left: 10px;
                              " @change="changeInterval">
                              <el-option v-for="item in options" :key="item.value" :label="item.label"
                                :value="item.value">
                              </el-option>
                            </el-select>
                          </div>
                        </div>
                      </div>
                      <!-- <dv-loading v-if="!show" class="loading"
                        ><span class="loading-text">Loading</span></dv-loading
                      > -->
                    </div>
                    <!-- <div class="statusWrapper" v-if="show1 || show3">
                      <div class="wrapper">
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type1,
                            title2: true,
                          }"
                          @click="type = 1"
                        >
                          <span class="img1 icon"></span
                          ><span class="text">智算中心</span>
                          <span class="text">{{ intelligenceTotal }}</span>
                        </div>
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type2,
                            title2: true,
                          }"
                          @click="type = 2"
                        >
                          <span class="img2 icon"></span
                          ><span class="text">超算中心</span>
                          <span class="text">{{ superTotal }}</span>
                        </div>
                        <div
                          :class="{
                            buttonStyle2: true,
                            check2: type3,
                            title2: true,
                          }"
                          @click="type = 3"
                        >
                          <span class="img3 icon"></span
                          ><span class="text">东数西算</span>
                          <span class="text">{{ eAdnwTotal }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img4 icon2"></span>
                          <span class="text">已接入</span>
                          <span class="text">{{ statusData.connected }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img5 icon2"></span>
                          <span class="text">接入中</span>
                          <span class="text">{{ statusData.accessing }}</span>
                        </div>
                        <div class="title3" @click="type = 3">
                          <span class="img6 icon2"></span>
                          <span class="text">待接入</span>
                          <span class="text">{{ statusData.pending }}</span>
                        </div>
                      </div>
                    </div> -->
                    <!-- <div class="statusWrapper2" v-if="show2">
                      <div class="wrapper">
                        <div class="arrow1">
                          <span class="ml-2">10TB全光网络互联</span>
                        </div>
                        <div class="arrow2">
                          <span class="ml-2">SD-WAN互联</span>
                        </div>
                        <div class="arrow3">
                          <span class="ml-2">MPLS互联</span>
                        </div>
                      </div>
                    </div> -->
                    <!-- <provinceMap
                      v-if="show1"
                      style="position: absolute; top: 0px"
                    ></provinceMap> -->
                    <provinceMap2 v-if="show2" style="position: absolute; top: 0px"></provinceMap2>
                    <provinceMap3 v-if="show3" :Data="tipData" style="position: absolute; top: 0px"></provinceMap3>
                    <el-row :gutter="30" justify="center" style="position: relative; top: 0px; left: 70px" type="flex">
                      <!-- <el-col :span="7">
                        <div
                          :class="{ buttonStyle: true, check: show1 }"
                          @click="changeType()"
                        >
                          仿真算力集群
                        </div>
                      </el-col>
                      <el-col :span="7">
                        <div
                          :class="{ buttonStyle: true, check: show2 }"
                          @click="changeType()"
                        >
                          仿真调度任务
                        </div>
                      </el-col> -->
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col :span="5">
              <!-- <div class="taskMsg">
                <div class="taskName">任务ID</div>
                <div class="taskValue">{{ taskDetail.ID }}</div>
              </div> -->
              <div class="taskDetailWrapper">
                <!--                <div>-->
                <!--                  <TASKDETAIL-->
                <!--                    :taskDetail="taskDetail"-->
                <!--                    :runningTime="runningTime"-->
                <!--                  ></TASKDETAIL>-->
                <!--                </div>-->
                <div>
                  <div class="mt">
                    <el-row justify="center" type="flex">
                      <el-col>
                        <div>
                          <el-row class="row-bg" justify="space-around" type="flex" :gutter="16">
                            <el-col :span="8" class="bg2" :class="{ 'active-tab': check1 }">
                              <el-link :class="{ button2: true, check3: check1 }" :disabled="disable" :underline="false"
                                @click="change(1)">提交任务量
                              </el-link>
                            </el-col>
                            <el-col :span="8" class="bg2" :class="{ 'active-tab': check2 }">
                              <el-link :class="{ button2: true, check3: check2 }" :disabled="disable" :underline="false"
                                @click="change(2)">
                                任务平均等待时长
                              </el-link>
                            </el-col>
                            <el-col :span="8" class="bg2" :class="{ 'active-tab': check3 }">
                              <el-link :class="{ button2: true, check3: check3 }" :disabled="disable" :underline="false"
                                @click="change(3)">资源利用率对比
                              </el-link>
                            </el-col>
                          </el-row>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div class="chart-scroll-container" :style="chartContainerStyle">
                    <div key="demo2" ref="echart2" style="width: 100%; height: 100%;"></div>
                  </div>
                </div>
              </div>

              <!-- <div class="showMt">
                <el-row class="linear-gradient" style="margin-
                m: 7px">
                  <el-col :span="2">
                    <div class="arrow"></div>
                  </el-col>
                  <el-col :span="20">
                    <div class="title1">算力使用趋势</div>
                  </el-col>
                </el-row>
                <TEND id="TEND" :config="tendConfig"></TEND>
              </div>
              <div class="showMt">
                <el-row class="linear-gradient">
                  <el-col :span="2">
                    <div class="arrow"></div>
                  </el-col>
                  <el-col :span="20">
                    <div class="title1">各中心算力使用情况</div>
                  </el-col>
                </el-row>
                <STATUS
                  id="Status"
                  :data="centerData"
                  :config="statusConfig"
                ></STATUS>
              </div>
              <div class="showMt">
                <el-row class="linear-gradient" style="margin-bottom: 20px">
                  <el-col :span="2">
                    <div class="arrow"></div>
                  </el-col>
                  <el-col :span="20">
                    <div class="title1">算力接入情况</div>
                  </el-col>
                </el-row>
                <INSERT
                  id="Imsert"
                  :data="connectedData"
                  :config="connectedConfig"
                ></INSERT>
              </div> -->
            </el-col>
          </el-row>
        </el-main>

      </el-container>
      <div class="footer"></div>
      <!-- <el-footer class="footer"></el-footer> -->
    </div>
    <!-- <div class="loading" v-if="stopClick">
      <dv-loading>Loading...</dv-loading>
    </div> -->
    <!-- 使用组件化的任务弹窗 -->
    <TaskDialog :visible.sync="addTaskDialogVisible" @close="handleCloseTaskDialog" @submit="submitTaskForm" />

    <!-- 协同任务弹窗 -->
    <CooperativeTaskDialog :visible.sync="addCooperativeTaskDialogVisible" @close="handleCloseCooperativeTaskDialog"
      @submit="submitCooperativeTaskForm" />

    <!-- 普通任务详情弹窗 -->
    <TaskDetailDialog v-if="!currentTaskIsCooperative" :visible.sync="taskDetailDialogVisible"
      :taskData="currentTaskData" @close="handleTaskDetailClose" @submit-success="handleTaskSubmitSuccess" />

    <!-- 协同任务详情弹窗 -->
    <CooperativeTaskDetailDialog v-if="currentTaskIsCooperative" :visible.sync="taskDetailDialogVisible"
      :taskData="currentTaskData" @close="handleTaskDetailClose" @submit-success="handleTaskSubmitSuccess" />
  </div>

</template>
<script>
// 保留使用的组件导入
import Total from '@/components/Total'
import chinaMap from '@/components/chinaMapDemo'
import Table from '@/components/Table'
import provinceMap2 from '@/components/provinceMap5'
import provinceMap3 from '@/components/provinceMap3'
import circle from '@/components/circle'
import TaskDialog from '@/components/TaskDialog'
import CooperativeTaskDialog from '@/components/CooperativeTaskDialog'
import { jobDetail } from '@/api/screenService.js'
import { loadTaskYaml } from '@/api/yamlService'
import TaskDetailDialog from '@/components/TaskDetailDialog'
import CooperativeTaskDetailDialog from '@/components/CooperativeTaskDetailDialog'
import MultiTaskService from '@/services/MultiTaskService'

export default {
  components: {
    Total,
    chinaMap,
    provinceMap2,
    provinceMap3,
    CumulativeTask: circle,
    Table,
    TaskDialog,
    CooperativeTaskDialog,
    TaskDetailDialog,
    CooperativeTaskDetailDialog
  },
  data () {
    return {
      // 对话框相关状态
      addTaskDialogVisible: false,
      addCooperativeTaskDialogVisible: false,
      taskDetailDialogVisible: false,
      currentTaskId: '',
      currentTaskIsCooperative: false, // 新增：标识当前任务是否为协同任务
      currentTaskData: null, // 新增：存储当前任务的详细数据

      // 图表数据
      taskData: {
        xData: [],
        used: [],
        used2: [],
        // 新增：支持多个任务的数据数组
        multiTaskData: [] // 格式: [{taskId, strategy, submitData, pendingData, resourceData}, ...]
      },
      Data: {
        xAxis: [],
        yAxis: []
      },

      // 任务状态信息
      msg: {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      },
      msg2: {
        totalNum: 0,
        execNum: 0
      },
      taskDetail: {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      },

      // 任务对比数据
      submitJobCompare0: null,
      pendingJobCompare0: null,
      resouceCompare0: null,
      submitJobCompare: null,
      pendingJobCompare: null,
      resouceCompare: null,

      // 计时和控制相关
      timer: null,
      count: 0,
      nowCount: 0,
      total: 0,
      total2: 0,
      runningTime: 0,

      // 图表和任务类型
      check1: true,
      check2: false,
      check3: false,
      taskType: 1,
      name: '提交任务量',
      myChart: null,
      myChart2: null,

      // UI控制状态
      buttonShow: true,
      disable: false,
      Stop: true,
      showButtom: true,
      showPer: true,
      intervalChange: true,
      showStart: false,

      // 时间配置
      options: [
        { value: 24, label: '24h' },
        { value: 12, label: '12h' },
        { value: 6, label: '6h' }
      ],
      value: 24,
      percentage: 0,

      // 数据缓存
      temp: null,
      taskIdRes: null,
      compareIdRes: null,
      CenterInfoToWebList: null,
      dataAlreadyLoaded: false,

      // 地图相关
      show2: false,
      show3: false,
      tipData: {}
    }
  },
  created () {
    // this.drawLine()
    // this.taskCenter = centerMsg().points;
    // this.getJob();
  },
  computed: {
    taskId () {
      return this.$store.state.id
    },
    compareId () {
      return this.$store.state.compareId
    },
    Strategy1 () {
      return this.$store.state.strategy1
    },
    Strategy2 () {
      return this.$store.state.strategy2
    },
    lastTime () {
      return this.$store.state.lastTime
    },
    interval () {
      return this.$store.state.interval
    },
    // 新增：多任务相关的computed属性
    selectedTasks () {
      return this.$store.state.selectedTasks
    },
    selectedTaskIds () {
      return this.$store.state.selectedTaskIds
    },
    // 动态计算图表容器样式
    chartContainerStyle () {
      return {
        width: '100%',
        height: '700px', // 固定容器高度
        maxHeight: '700px',
        overflowY: 'auto', // 添加垂直滚动条
        overflowX: 'hidden' // 隐藏水平滚动条
      }
    },
    // 计算图表实际高度
    chartHeight () {
      const cityCount = this.taskData.xData ? this.taskData.xData.length : 0
      const taskCount = this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0
        ? this.taskData.multiTaskData.length
        : (this.compareId !== 0 && this.taskData.used2 && this.taskData.used2.length > 0 ? 2 : 1)
      return this.calculateChartHeight(cityCount, taskCount)
    }
    // percentage: {
    //   get() {
    //     if (this.count == 0 || this.total == 0) {
    //       return 0;
    //     } else {
    //       // console.log(this.count / this.total)
    //       return Number(((this.count / this.total) * 100).toFixed(0));
    //     }
    //   },
    //   set() {
    //     if (this.count == 0 || this.total == 0) {
    //       return 0;
    //     } else {
    //       return Number(((this.count / this.total) * 100).toFixed(0));
    //     }
    //   },
    // },
  },
  watch: {
    taskId (newValue) {
      // 保持地图状态
      if (this.$refs.chinaMap) {
        this.$refs.chinaMap.stopped = false

        // 如果地图上有数据，保留它们
        if (this.$refs.chinaMap.nowConfig &&
          this.$refs.chinaMap.nowConfig.points &&
          this.$refs.chinaMap.nowConfig.points.length > 0) {
          // 地图数据已存在，不清空
        } else {
          // 如果没有数据，初始化演示数据
          this.$refs.chinaMap.initDemoData()
          this.$refs.chinaMap.showDot()
        }
      }

      // 重置数据
      this.$nextTick(() => {
        this.resetAllChartData()
        this.intervalChange = this.taskId !== 0
      })
    },

    compareId () {
      // 保持地图状态
      if (this.$refs.chinaMap) {
        this.$refs.chinaMap.stopped = false
      }

      // 重置图表数据
      this.$nextTick(() => {
        this.resetAllChartData()
      })
    },

    count (newValue) {
      // 更新进度百分比
      if (this.count === this.total) {
        this.percentage = 100
      } else if (this.count === 0) {
        this.percentage = 0
      } else {
        this.percentage = Number(((this.count / this.total) * 100).toFixed(0))
      }

      // 更新数据和状态
      if (newValue === this.total) {
        this.Data.yAxis = this.temp
        this.drawLine()
        clearInterval(this.timer)
        this.percentage = 100
        this.Stop = true
      } else {
        // 更新折线图数据
        this.temp.forEach((item, key) => {
          if (key <= this.count + 1) {
            this.Data.yAxis[key] = this.temp[key]
          } else {
            this.Data.yAxis[key] = ''
          }
        })
      }
    }
  },
  mounted () {
    // 初始化图表
    this.$nextTick(() => {
      // 初始化图表实例
      if (this.myChart) {
        this.myChart.dispose()
      }
      if (this.myChart2) {
        this.myChart2.dispose()
      }

      this.myChart = this.$echarts.init(this.$refs.echart)
      this.myChart2 = this.$echarts.init(this.$refs.echart2)

      // 初始化空数据
      this.resetAllChartData()

      // 添加窗口大小调整监听
      window.addEventListener('resize', this.resize)
    })
  },
  methods: {
    // 新增任务相关方法
    openAddTaskDialog () {
      this.addTaskDialogVisible = true
    },
    handleCloseTaskDialog () {
      this.addTaskDialogVisible = false
    },
    // 新增协同任务相关方法
    openAddCooperativeTaskDialog () {
      this.addCooperativeTaskDialogVisible = true
    },
    handleCloseCooperativeTaskDialog () {
      this.addCooperativeTaskDialogVisible = false
    },
    submitTaskForm (responseOrFormData) {
      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自TaskDialog的generateYaml响应
        this.$message.success('任务创建成功')
        this.addTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          if (this.$refs.table) {
            this.$refs.table.init()
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/tasks', responseOrFormData)
          .then(response => {
            this.$message.success('任务创建成功')
            this.addTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              if (this.$refs.table) {
                this.$refs.table.init()
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建任务失败: ' + error.message)
          })
      }
    },
    submitCooperativeTaskForm (responseOrFormData) {
      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自CooperativeTaskDialog的generateYaml响应
        this.$message.success('协同任务创建成功')
        this.addCooperativeTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          if (this.$refs.table) {
            this.$refs.table.init()
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/cooperative-tasks', responseOrFormData)
          .then(response => {
            this.$message.success('协同任务创建成功')
            this.addCooperativeTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              if (this.$refs.table) {
                this.$refs.table.init()
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建协同任务失败: ' + error.message)
          })
      }
    },
    // 打开任务详情弹窗
    openTaskDetailDialog (row) {
      // 打开任务详情: row
      this.currentTaskId = row.ID

      // 通过API获取任务配置，判断是否为协同任务
      // 正在请求任务配置以判断任务类型...

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在加载任务信息...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      loadTaskYaml(this.currentTaskId)
        .then(response => {
          // 获取任务配置成功: response

          // 根据配置内容判断是否为协同任务
          const config = response.yaml_data || {}

          // 判断标准：根据c2netJobConfig.useJsonFile字段
          // useJsonFile为false时是协同任务，为true或不存在时是普通任务
          let isCooperativeTask = false
          if (config.c2netJobConfig && config.c2netJobConfig.hasOwnProperty('useJsonFile')) {
            isCooperativeTask = (config.c2netJobConfig.useJsonFile === false)
          } else {
            // 如果没有useJsonFile字段，则根据其他特征判断（降级逻辑）
            isCooperativeTask = !!(
              config.c2netComputingCenterList ||
              (config.c2netJobConfig && config.c2netJobConfig.c2netJobGroupList) ||
              config.scheduleConfig ||
              (config.networkConfig && (config.networkConfig.construct || config.networkConfig.accelerator))
            )
          }

          this.currentTaskIsCooperative = isCooperativeTask
          // 将完整的数据存储到currentTaskData中，供弹窗组件使用
          this.currentTaskData = {
            taskId: this.currentTaskId,
            taskInfo: response.task || {},
            yamlData: config,
            rawResponse: response
          }
          // 任务类型判断结果: taskId, isCooperativeTask, useJsonFile, hasUseJsonFile, configStructure

          // 确保DOM更新后再显示弹窗
          this.$nextTick(() => {
            this.taskDetailDialogVisible = true
            // 当前任务ID: this.currentTaskId, 协同任务: this.currentTaskIsCooperative, 对话框可见性: this.taskDetailDialogVisible
          })
        })
        .catch(error => {
          // 获取任务配置失败: error

          // 如果API请求失败，降级使用row中的数据（如果存在）
          if (row.hasOwnProperty('useJsonFile')) {
            // API请求失败，降级使用row数据判断任务类型
            this.currentTaskIsCooperative = row.useJsonFile === true || row.useJsonFile === 1
            // 降级判断结果 - 协同任务: this.currentTaskIsCooperative
          } else {
            // 默认当作普通任务处理
            // 无法判断任务类型，默认作为普通任务处理
            this.currentTaskIsCooperative = false
          }

          // 确保DOM更新后再显示弹窗
          this.$nextTick(() => {
            this.taskDetailDialogVisible = true
          })
        })
        .finally(() => {
          loading.close()
        })
    },
    // 处理任务详情弹窗关闭
    handleTaskDetailClose () {
      this.taskDetailDialogVisible = false
      // 清空任务数据
      this.currentTaskData = null
      this.currentTaskId = ''
      this.currentTaskIsCooperative = false
    },
    // 处理任务提交成功
    handleTaskSubmitSuccess () {
      // 任务提交/重新提交成功事件被触发
      // 刷新任务列表
      if (this.$refs.table) {
        // 调用table.init()刷新任务列表
        this.$refs.table.init()
      } else {
        // table引用不存在，无法刷新任务列表
      }
    },

    // 处理地图组件请求任务详情的事件
    handleNeedJobDetail (taskId) {
      // 接收到地图组件请求任务详情，taskId: taskId
      if (taskId && taskId !== 0) {
        this.getJobDetail(taskId)
      }
    },
    // 基础数据操作方法

    /**
     * 重置图表和任务数据
     */
    resetChartData () {
      // 初始化为空数据，仅保持图表框架
      this.Data = {
        xAxis: [],
        yAxis: []
      }

      this.taskData = {
        xData: [],
        used: [],
        used2: [],
        multiTaskData: []
      }

      this.drawLine()
      this.drawLine2()
      this.count = 0
      this.buttonShow = true
      // 保持showButtom为true，以便显示暂停/继续按钮
      // this.showButtom = false
    },

    /**
     * 重置主要信息和任务详情
     */
    resetTaskInfo () {
      this.msg = {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      }
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
      this.taskDetail = {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      }
    },

    /**
     * 初始化任务类型数据对象
     */
    initTaskTypeData () {
      return {
        used: null,
        xData: [],
        used2: null
      }
    },

    // 获取和处理数据方法

    /**
     * 处理多个任务的数据
     * @param {Array} tasksData - 多个任务的数据数组
     */
    processMultipleTasksData (tasksData) {
      // 开始处理多任务数据: tasksData.length 个任务

      // 使用第一个任务作为主任务，折线图显示主任务的数据
      const mainTask = tasksData[0]

      // 设置基本任务信息（使用主任务的信息）
      this.initBaseTaskInfo(mainTask)

      // 使用主任务的数据设置折线图（与单任务模式保持一致）
      if (
        mainTask.CenterInfoToWebList != null &&
        mainTask.CenterInfoToWebList[0] &&
        mainTask.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = mainTask.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } = this.extractJobData(mainTask.CenterInfoToWebList)

      // 设置折线图数据（使用主任务的数据，与单任务模式一致）
      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]

      // 初始化主任务的对比数据
      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

      // 处理右侧多任务对比图表数据
      this.processMultipleTasksCompareData(tasksData)

      // 绘制图表
      this.drawLine()
      this.drawLine2()
      this.disable = false

      // 确保显示暂停/继续按钮
      this.showButtom = true

      // 启动动画定时器（使用主任务的数据）
      this.startMultiTaskAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis, tasksData)
    },

    /**
     * 处理多任务对比数据
     * @param {Array} tasksData - 多个任务的数据数组
     */
    processMultipleTasksCompareData (tasksData) {
      // processMultipleTasksCompareData 被调用，tasksData: tasksData.length 个任务
      const compareData = MultiTaskService.getMultipleTasksCompareData(tasksData, this.count)
      // MultiTaskService.getMultipleTasksCompareData 返回: compareData

      // 设置x轴数据（城市列表）
      this.taskData.xData = compareData.xData

      // 存储所有任务的数据
      this.taskData.multiTaskData = compareData.series.map(series => ({
        taskId: series.taskId,
        strategy: series.strategy,
        submitData: series.submitData,
        pendingData: series.pendingData,
        resourceData: series.resourceData
      }))

      // 设置后的 taskData.multiTaskData: this.taskData.multiTaskData

      // 兼容原有的双任务模式（保持向后兼容）
      if (compareData.series.length > 0) {
        const firstSeries = compareData.series[0]

        if (this.taskType === 1) {
          this.taskData.used = firstSeries.submitData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].submitData
          }
        } else if (this.taskType === 2) {
          this.taskData.used = firstSeries.pendingData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].pendingData
          }
        } else if (this.taskType === 3) {
          this.taskData.used = firstSeries.resourceData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].resourceData
          }
        }
      }
    },

    /**
     * 启动多任务动画定时器
     * @param {Array} Time - 时间数组
     * @param {Array} historySubmitJob - 历史提交任务数组
     * @param {Array} historyCompleteJob - 历史完成任务数组
     * @param {Array} city - 城市数组
     * @param {Array} submitJobUsed - 提交任务使用数据
     * @param {Array} pendingJobUsed - 等待任务使用数据
     * @param {Array} resouceCompareUsed - 资源对比使用数据
     * @param {Array} xAxis - X轴数据
     * @param {Array} tasksData - 任务数据数组
     */
    startMultiTaskAnimationTimer (Time, historySubmitJob, historyCompleteJob, city, submitJobUsed, pendingJobUsed, resouceCompareUsed, xAxis, tasksData) {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count == that.total) {
          clearInterval(that.timer)
          return
        }

        // 更新动画计时器数据（与单任务模式保持一致）
        that.updateAnimationTimerData(Time, historySubmitJob, historyCompleteJob, xAxis)

        // 更新右侧多任务对比图表
        that.processMultipleTasksCompareData(tasksData)

        // 重绘图表
        that.drawLine2()
        that.drawLine()
        that.showStart = true
      }, 2000)
    },

    getJobDetail (id) {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      const data = {
        id: id,
        resolution_n_hours: this.interval
      }
      this.disable = true
      this.showButtom = true
      this.showPer = true
      jobDetail(data).then((res) => {
        this.processMainJobData(res)
      })
    },

    getCompare (id) {
      if (!id == 0) {
        const data = {
          id: id,
          resolution_n_hours: this.interval
        }
        this.disable = true
        this.showButtom = true
        this.showPer = true
        jobDetail(data).then((res) => {
          this.processCompareJobData(res)
        })
      } else {
        this.taskData.used2 = []
        this.CenterInfoToWebList = null
      }
    },

    // 将getJobDetail中的数据处理逻辑抽离为独立方法
    processMainJobData (res) {
      this.initBaseTaskInfo(res)

      if (
        res.CenterInfoToWebList != null &&
        res.CenterInfoToWebList[0] &&
        res.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } =
        this.extractJobData(res.CenterInfoToWebList)

      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]
      this.updateTaskDataByType(city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used)

      this.drawLine()
      this.drawLine2()
      this.disable = false

      // 确保显示暂停/继续按钮
      this.showButtom = true

      if (this.compareId !== 0) {
        this.getCompare(this.compareId)
      }

      this.startAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis)
    },

    /**
     * 初始化基本任务信息
     */
    initBaseTaskInfo (res) {
      this.msg.SnapshotTime = 0
      this.msg.NPops = res.NPops
      this.msg.NCenters = res.NCenters
      this.taskDetail.SnapshotTime = res.SnapshotTime
      this.taskDetail.ID = res.ID
      this.taskDetail.strategy = res.Strategy
      this.taskDetail.NJobs = res.NJobs
      this.taskDetail.CompletedFlag = res.CompletedFlag
    },

    /**
     * 初始化主对比数据
     */
    initMainCompareData (city, submitJobCompare, pendingJobCompare, resouceCompare) {
      this.submitJobCompare0 = this.initTaskTypeData()
      this.pendingJobCompare0 = this.initTaskTypeData()
      this.resouceCompare0 = this.initTaskTypeData()

      this.submitJobCompare0.xData = city
      this.pendingJobCompare0.xData = city
      this.resouceCompare0.xData = city
      this.submitJobCompare0.used = submitJobCompare.used
      this.pendingJobCompare0.used = pendingJobCompare.used
      this.resouceCompare0.used = resouceCompare.used
    },

    // 将getCompare中的数据处理逻辑抽离为独立方法
    processCompareJobData (res) {
      this.CenterInfoToWebList = res.CenterInfoToWebList
      this.total2 = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length

      this.submitJobCompare = this.initTaskTypeData()
      this.pendingJobCompare = this.initTaskTypeData()
      this.resouceCompare = this.initTaskTypeData()

      const {
        city,
        data1,
        data2,
        data3
      } = this.processCompareSnapshots(res)

      this.submitJobCompare.xData = city
      this.pendingJobCompare.xData = city
      this.resouceCompare.xData = city
      this.submitJobCompare.used2 = data1
      this.pendingJobCompare.used2 = data2
      this.resouceCompare.used2 = data3
      this.taskData.xData = this.submitJobCompare0.xData
    },

    /**
     * 处理对比任务的快照数据
     */
    processCompareSnapshots (res) {
      const city = []
      const data1 = []
      const data2 = []
      const data3 = []
      this.disable = false

      res.CenterInfoToWebList.forEach((item) => {
        city.push(item.InfoName)
      })

      if (this.total2 <= this.total) {
        this.CenterInfoToWebList.forEach((item) => {
          item.SnapshotInfoToWebList.length = this.total
        })
      }

      for (let i = 0; i < this.total; i++) {
        data1[i] = []
        data2[i] = []
        data3[i] = []
        res.CenterInfoToWebList.forEach((item, key) => {
          const HistorySubmitJob = item.SnapshotInfoToWebList[this.total2 - 1].HistorySubmitJob
          const AveragePendingTime = item.SnapshotInfoToWebList[this.total2 - 1].AveragePendingTime
          const AverageMachineUse = item.SnapshotInfoToWebList[this.total2 - 1].AverageMachineUse

          data1[i][key] = item.SnapshotInfoToWebList[i] != undefined
            ? item.SnapshotInfoToWebList[i].HistorySubmitJob
            : HistorySubmitJob
          data2[i][key] = item.SnapshotInfoToWebList[i] != undefined
            ? item.SnapshotInfoToWebList[i].AveragePendingTime
            : AveragePendingTime
          data3[i][key] = item.SnapshotInfoToWebList[i] != undefined
            ? item.SnapshotInfoToWebList[i].AverageMachineUse
            : AverageMachineUse
        })
      }

      return {
        city,
        data1,
        data2,
        data3
      }
    },

    // 从CenterInfoToWebList中提取各种任务数据的通用方法
    extractJobData (centerInfoList) {
      const historySubmitJob = []
      const historyCompleteJob = []
      const submitJob = []
      const completeJob = []
      const Time = []
      this.Data.xAxis = []
      this.Data.yAxis = []

      const submitJobCompare = this.initTaskTypeData()
      const pendingJobCompare = this.initTaskTypeData()
      const resouceCompare = this.initTaskTypeData()

      const city = []
      const data1 = []
      const data2 = []
      const data3 = []

      centerInfoList.forEach((item) => {
        city.push(item.InfoName)
      })

      for (let i = 0; i < this.total; i++) {
        let HistorySubmitJob = 0
        let HistoryCompleteJob = 0
        let SubmitJob = 0
        let CompleteJob = 0
        data1[i] = []
        data2[i] = []
        data3[i] = []

        centerInfoList.forEach((item, key) => {
          if (item.SnapshotInfoToWebList[i]) {
            HistorySubmitJob += item.SnapshotInfoToWebList[i].HistorySubmitJob
            HistoryCompleteJob += item.SnapshotInfoToWebList[i].HistoryCompleteJob
            SubmitJob += item.SnapshotInfoToWebList[i].SubmitJob
            CompleteJob += item.SnapshotInfoToWebList[i].CompleteJob
            data1[i][key] = item.SnapshotInfoToWebList[i].HistorySubmitJob
            data2[i][key] = item.SnapshotInfoToWebList[i].AveragePendingTime
            data3[i][key] = item.SnapshotInfoToWebList[i].AverageMachineUse
          }
        })

        historySubmitJob[i] = HistorySubmitJob
        historyCompleteJob[i] = HistoryCompleteJob
        submitJob[i] = SubmitJob
        completeJob[i] = CompleteJob
      }

      submitJobCompare.xData = JSON.parse(JSON.stringify(city))
      pendingJobCompare.xData = JSON.parse(JSON.stringify(city))
      resouceCompare.xData = JSON.parse(JSON.stringify(city))
      submitJobCompare.used = data1
      pendingJobCompare.used = data2
      resouceCompare.used = data3

      centerInfoList[0].SnapshotInfoToWebList.forEach((item) => {
        // 使用修改后的formatDuring确保返回纯小时数
        const timeValue = this.formatDuring(item.Time)
        Time.push(timeValue)
      })

      return {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      }
    },

    // 根据任务类型更新任务数据的通用方法
    updateTaskDataByType (city, submitJobUsed, pendingJobUsed, resouceCompareUsed, index) {
      const idx = index !== undefined ? index : this.count

      if (this.taskType == 1) {
        this.updateTaskDataForType(city, submitJobUsed[idx], this.submitJobCompare?.used2?.[idx])
      }
      if (this.taskType == 2) {
        this.updateTaskDataForType(city, pendingJobUsed[idx], this.pendingJobCompare?.used2?.[idx])
      }
      if (this.taskType == 3) {
        this.updateTaskDataForType(city, resouceCompareUsed[idx], this.resouceCompare?.used2?.[idx])
      }
    },

    /**
     * 更新指定类型的任务数据
     */
    updateTaskDataForType (city, usedData, usedData2) {
      this.taskData.xData = city
      this.taskData.used = usedData
      if (usedData2) {
        this.taskData.used2 = usedData2
      }
    },

    // 开始动画计时器的通用方法
    startAnimationTimer (Time, historySubmitJob, historyCompleteJob, city, submitJobUsed, pendingJobUsed, resouceCompareUsed, xAxis) {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count == that.total) {
          clearInterval(that.timer)
          return
        }

        that.updateAnimationTimerData(Time, historySubmitJob, historyCompleteJob, xAxis)
        that.updateTaskDataByType(city, submitJobUsed, pendingJobUsed, resouceCompareUsed)

        that.drawLine2()
        that.drawLine()
        that.showStart = true
      }, 2000)
    },

    /**
     * 更新动画计时器数据
     */
    updateAnimationTimerData (Time, historySubmitJob, historyCompleteJob, xAxis) {
      // 注意：这里传入的Time已经是小时值了，因此不需要再处理
      this.msg.SnapshotTime = Time[this.count] + 'h'
      this.msg2.totalNum = historySubmitJob[this.count]
      this.msg2.execNum = historyCompleteJob[this.count]
      this.Data.xAxis = xAxis
      this.runningTime = Time[this.count] + 'h'
    },

    async getJobData () {
      const promises = []
      this.taskIdRes = null
      this.compareIdRes = null

      if (this.taskId !== 0) {
        const data1 = {
          id: this.taskId,
          resolution_n_hours: this.interval
        }
        promises.push(
          jobDetail(data1).then(res => {
            this.taskIdRes = res
          })
        )
      }

      if (this.compareId !== 0) {
        const data2 = {
          id: this.compareId,
          resolution_n_hours: this.interval
        }
        promises.push(
          jobDetail(data2).then(res => {
            this.compareIdRes = res
          })
        )
      }

      await Promise.all(promises)
      return {
        taskIdRes: this.taskIdRes,
        compareIdRes: this.compareIdRes
      }
    },

    processJobData (taskRes, compareRes) {
      this.prepareForProcessing()

      if (!taskRes) return

      // 处理主任务数据
      this.initBaseTaskInfo(taskRes)

      if (
        taskRes.CenterInfoToWebList != null &&
        taskRes.CenterInfoToWebList[0] &&
        taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } =
        this.extractJobData(taskRes.CenterInfoToWebList)

      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]
      this.updateTaskDataByType(city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used)

      // 处理对比任务数据
      if (compareRes) {
        this.processCompareData(compareRes, city)
      }

      this.finalizeProcessing()
      this.startAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis)
    },

    /**
     * 准备处理数据
     */
    prepareForProcessing () {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      this.disable = true
      this.showButtom = true
      this.showPer = true
    },

    /**
     * 完成数据处理
     */
    finalizeProcessing () {
      this.drawLine()
      this.drawLine2()
      this.disable = false
    },

    processCompareData (compareRes, city) {
      if (!compareRes) return

      this.CenterInfoToWebList = compareRes.CenterInfoToWebList
      this.total2 = compareRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length

      this.submitJobCompare = this.initTaskTypeData()
      this.pendingJobCompare = this.initTaskTypeData()
      this.resouceCompare = this.initTaskTypeData()

      const {
        city: compareCity,
        data1,
        data2,
        data3
      } = this.processCompareSnapshots(compareRes)

      this.submitJobCompare.xData = compareCity
      this.pendingJobCompare.xData = compareCity
      this.resouceCompare.xData = compareCity
      this.submitJobCompare.used2 = data1
      this.pendingJobCompare.used2 = data2
      this.resouceCompare.used2 = data3
      this.taskData.xData = city
    },

    formatDuring (val) {
      // 修改为只返回小时数，不使用天(d)为单位
      const totalHours = Math.floor(val * 1000 / (1000 * 60 * 60))
      return totalHours
    },

    formatDuring2 (val) {
      // 确保只返回小时数
      var hours = Math.floor(val * 3600 / 3600)
      return hours
    },

    formatDuring3 (val) {
      // 修改为只返回小时为单位的时间
      if (val < (1 / 3600)) {
        return (val * 3600).toFixed(2) + 's'
      }

      // 转换为小时，包括小数部分
      const totalHours = val
      return totalHours.toFixed(2) + 'h'
    },

    // 图表相关方法

    drawLine () {
      const option = this.createLineChartOption()
      this.myChart && this.myChart.setOption(option, true)
    },

    /**
     * 创建折线图配置
     */
    createLineChartOption () {
      // 确保即使没有数据也显示图表框架
      const hasData = this.Data.xAxis && this.Data.xAxis.length > 0

      return {
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10]
        },
        color: '#32c5ff ',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid',
              color: 'rgba(0, 0, 0, 0)'
            }
          },
          formatter: function (params) {
            params = [params[0]]
            let htmlStr = ''
            htmlStr += '<div>'
            htmlStr += '<div>'
            htmlStr += params[0].axisValue + 'h'
            htmlStr += '</div>'
            htmlStr +=
              '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
              '#1890ff' +
              ';"></span>'
            htmlStr += params[0].seriesName + params[0].value
            htmlStr += '</div>'
            return htmlStr
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        grid: {
          right: '5%',
          bottom: '25%',
          top: '12%',
          left: '10%'
        },
        xAxis: this.createXAxisConfig(),
        yAxis: this.createYAxisConfig(),
        series: [
          {
            name: '提交任务量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: false
            },
            zlevel: 1,
            z: 1,
            data: this.Data.yAxis || [],
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(50,197,255,0)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(22,93,255)'
                  }
                ],
                global: false
              }
            }
          }
        ],
        legend: {
          data: ['提交任务量'],
          left: 255,
          top: 225,
          itemHeight: 12,
          textStyle: {
            color: '#a1a1a1',
            fontSize: 12
          }
        },
        animation: true,
        animationDuration: function (idx) {
          return idx * 500
        },
        animationEasing: 'backln'
      }
    },

    /**
     * 创建X轴配置
     */
    createXAxisConfig () {
      return {
        type: 'category',
        boundaryGap: false,
        data: this.Data.xAxis || [],
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(1, 145, 255, 0.3)'
          }
        },
        axisLabel: {
          show: true,
          showMaxLabel: true,
          interval: 'auto',
          fontSize: '12',
          lineHeight: 40,
          color: 'rgba(255, 255, 255,1)',
          fontFamily: 'Microsoft YaHei',
          fontWeight: 'normal',
          formatter: function (value) {
            // 确保值显示为数字+h格式，无论输入是什么格式
            return value + 'h'
          }
        },
        axisTick: {
          show: false
        }
      }
    },

    /**
     * 创建Y轴配置
     */
    createYAxisConfig () {
      return {
        type: 'value',
        name: '',
        show: true,
        nameTextStyle: {
          padding: [0, 0, 0, 0]
        },
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            type: 'dotted',
            color: 'rgba(1, 145, 255, 0.3)'
          }
        },
        nameGap: 10,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255,0.9)'
          }
        },
        axisLabel: {
          show: true,
          interval: 'auto',
          align: 'right',
          fontSize: '10',
          fontWeight: 'bold',
          color: 'rgba(255, 255, 255,0.9)'
        },
        scale: true,
        min: 0,
        splitNumber: 4
      }
    },

    change (val) {
      this.taskType = val
      this.taskData = {
        xData: this.taskData.xData || [],
        used: [],
        used2: [],
        multiTaskData: this.taskData.multiTaskData || []
      }

      this.updateUIByTaskType(val)
      this.drawLine2()
    },

    /**
     * 根据任务类型更新UI
     */
    updateUIByTaskType (type) {
      const isAtEnd = this.count == this.total
      const currentIndex = isAtEnd ? this.count - 1 : this.count

      // 更新选中状态
      this.check1 = type == 1
      this.check2 = type == 2
      this.check3 = type == 3

      // 设置任务名称
      const nameMap = {
        1: '提交任务量',
        2: '任务平均等待时长',
        3: '资源利用率对比'
      }
      this.name = nameMap[type]

      // 更新任务数据
      // 如果有多任务数据，优先使用多任务数据
      if (this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0) {
        // 多任务模式：数据已经在multiTaskData中，不需要额外处理
        // createBarSeriesConfig会直接从multiTaskData中获取数据
      } else {
        // 兼容原有的双任务模式
        if (type == 1) {
          this.updateTaskDataForType(
            this.resouceCompare0?.xData || [],
            this.submitJobCompare0?.used?.[currentIndex],
            this.compareId !== 0 ? this.submitJobCompare?.used2?.[currentIndex] : undefined
          )
        } else if (type == 2) {
          this.updateTaskDataForType(
            this.pendingJobCompare0?.xData || [],
            this.pendingJobCompare0?.used?.[currentIndex],
            this.compareId !== 0 ? this.pendingJobCompare?.used2?.[currentIndex] : undefined
          )
        } else if (type == 3) {
          this.updateTaskDataForType(
            this.resouceCompare0?.xData || [],
            this.resouceCompare0?.used?.[currentIndex],
            this.compareId !== 0 ? this.resouceCompare?.used2?.[currentIndex] : undefined
          )
        }
      }
    },

    drawLine2 () {
      // drawLine2 被调用
      const option = this.createBarChartOption()
      // createBarChartOption 返回的 option.series 长度: option.series?.length
      // createBarChartOption 调试信息:
      // - compareId: this.compareId
      // - selectedTasks: this.selectedTasks
      // - selectedTasks.length: this.selectedTasks?.length
      // - taskData.multiTaskData: this.taskData.multiTaskData
      // - taskData.multiTaskData.length: this.taskData.multiTaskData?.length

      if (this.myChart2) {
        // 设置图表容器的实际高度
        const calculatedHeight = this.chartHeight
        // 设置图表高度: calculatedHeight

        // 动态设置图表容器的内容高度
        this.$refs.echart2.style.height = `${calculatedHeight}px`

        this.myChart2.setOption(option, true)
        // 确保图表在容器大小变化后能正确调整
        this.$nextTick(() => {
          this.myChart2.resize()
        })
      }
    },

    // 动作控制方法

    /**
     * 开始或重新开始动画
     */
    start () {
      this.prepareForStart()

      // 检查是否有选中的任务
      if (this.selectedTaskIds.length > 1) {
        // 多任务模式：使用多任务服务获取数据，但折线图显示主任务数据
        // 开始处理多个任务: this.selectedTaskIds

        // 使用新的多任务服务获取数据
        MultiTaskService.getMultipleTasksData(this.selectedTaskIds, this.interval)
          .then(tasksData => {
            if (tasksData && tasksData.length > 0) {
              // 获取到多个任务数据: tasksData.map(t => t.ID)

              // 处理多任务数据（折线图显示主任务数据，右侧图表支持多任务对比）
              this.processMultipleTasksData(tasksData)

              // 设置数据已加载标记
              this.dataAlreadyLoaded = true

              // 确保地图更新
              this.$nextTick(() => {
                if (this.$refs.chinaMap) {
                  // 多任务数据已加载完成，通知地图组件刷新
                  this.$refs.chinaMap._preventReinitOnNextUpdate = false

                  // 使用第一个任务的数据更新地图
                  if (tasksData[0]) {
                    this.taskIdRes = tasksData[0]
                  }
                }
              })
            }
          })
          .catch(error => {
            // 获取多任务数据失败: error
            this.$message.error('获取任务数据失败，请稍后重试')
            this.disable = false
          })
      } else if (this.taskId !== 0) {
        // 单任务模式：使用原有的单任务处理逻辑
        this.getJobData().then(() => {
          if (this.taskIdRes) {
            this.processJobData(this.taskIdRes, this.compareIdRes)
            this.dataAlreadyLoaded = true

            this.$nextTick(() => {
              if (this.$refs.chinaMap) {
                // 数据已加载完成，通知地图组件刷新
                this.$refs.chinaMap._preventReinitOnNextUpdate = false

                if (!this.taskIdRes) {
                  this.$refs.chinaMap.getJobDetail(this.taskId)
                }
              }
            })
          }
        })
      } else {
        // 没有选中任何任务
        this.disable = false
      }
    },

    /**
     * 准备开始
     */
    prepareForStart () {
      this.disable = true
      clearInterval(this.timer)
      this.count = 0
      // 重置数据加载状态
      this.dataAlreadyLoaded = false

      // 重要：使用nextTick机制确保DOM更新后再修改状态
      this.$nextTick(() => {
        // 如果chinaMap组件存在，确保它的状态保持
        if (this.$refs.chinaMap) {
          // 设置chinaMap的内部状态，但不重建地图
          this.$refs.chinaMap.stopped = false
        }

        // 使用空数据结构，仅保持图表框架
        this.taskData = {
          xData: [],
          used: [],
          used2: [],
          multiTaskData: []
        }

        this.Data = {
          xAxis: [],
          yAxis: []
        }

        // 在设置Stop状态前先绘制图表
        this.drawLine()
        this.drawLine2()

        // 最后才设置Stop状态
        this.Stop = false
        this.buttonShow = true
        this.showButtom = true // 确保显示暂停/继续按钮
      })
    },

    stop () {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.nowCount = this.count
      this.buttonShow = false
      this.Stop = true
    },

    goOn () {
      if (this.count === this.total) {
        return
      }

      // 设置基本状态标记
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 通知地图组件恢复动画
      if (this.$refs.chinaMap) {
        // 通知地图组件恢复动画
      }

      // 检查是否已经有加载的数据
      if (this.dataAlreadyLoaded && this.temp && this.Data.xAxis.length > 0) {
        // 使用已缓存的数据恢复动画，不需要重新请求

        // 立即更新当前帧数据 - 这是关键修改部分
        const currentIndex = this.count

        // 立即更新环图数据，确保继续时环图立即更新
        if (this.taskIdRes && this.taskIdRes.CenterInfoToWebList) {
          const centerInfoList = this.taskIdRes.CenterInfoToWebList
          let historySubmitJob = 0
          let historyCompleteJob = 0

          // 从当前快照收集数据
          centerInfoList.forEach(center => {
            if (center.SnapshotInfoToWebList &&
              center.SnapshotInfoToWebList[currentIndex]) {
              historySubmitJob += center.SnapshotInfoToWebList[currentIndex].HistorySubmitJob
              historyCompleteJob += center.SnapshotInfoToWebList[currentIndex].HistoryCompleteJob
            }
          })

          // 更新环图数据
          this.msg2.totalNum = historySubmitJob
          this.msg2.execNum = historyCompleteJob
        }

        // 根据当前选择的任务类型立即更新右侧图表
        if (this.taskType === 1 && this.submitJobCompare0) {
          this.updateTaskDataForType(
            this.submitJobCompare0.xData,
            this.submitJobCompare0.used[currentIndex],
            this.compareId !== 0 && this.submitJobCompare?.used2
              ? this.submitJobCompare.used2[currentIndex] : undefined
          )
        } else if (this.taskType === 2 && this.pendingJobCompare0) {
          this.updateTaskDataForType(
            this.pendingJobCompare0.xData,
            this.pendingJobCompare0.used[currentIndex],
            this.compareId !== 0 && this.pendingJobCompare?.used2
              ? this.pendingJobCompare.used2[currentIndex] : undefined
          )
        } else if (this.taskType === 3 && this.resouceCompare0) {
          this.updateTaskDataForType(
            this.resouceCompare0.xData,
            this.resouceCompare0.used[currentIndex],
            this.compareId !== 0 && this.resouceCompare?.used2
              ? this.resouceCompare.used2[currentIndex] : undefined
          )
        }

        // 立即重绘右侧图表
        this.drawLine2()

        // 清除任何可能存在的定时器
        clearInterval(this.timer)

        // 从当前帧重启定时器
        const that = this
        this.timer = setInterval(() => {
          that.count++
          if (that.count >= that.total) {
            clearInterval(that.timer)
            that.count = that.total
            return
          }

          // 更新数据
          // 更新折线图数据
          if (that.temp && that.count < that.temp.length) {
            that.Data.yAxis[that.count] = that.temp[that.count]
          }

          // 更新时间信息
          if (that.Data.xAxis && that.count < that.Data.xAxis.length) {
            that.msg.SnapshotTime = that.Data.xAxis[that.count] + 'h'
            that.runningTime = that.Data.xAxis[that.count] + 'h'
          }

          // 更新环图数据 - 添加对msg2对象的更新，解决环图不更新问题
          if (that.taskIdRes && that.taskIdRes.CenterInfoToWebList) {
            const centerInfoList = that.taskIdRes.CenterInfoToWebList
            let historySubmitJob = 0
            let historyCompleteJob = 0

            // 从当前快照收集数据
            centerInfoList.forEach(center => {
              if (center.SnapshotInfoToWebList &&
                center.SnapshotInfoToWebList[that.count]) {
                historySubmitJob += center.SnapshotInfoToWebList[that.count].HistorySubmitJob
                historyCompleteJob += center.SnapshotInfoToWebList[that.count].HistoryCompleteJob
              }
            })

            // 更新环图数据
            that.msg2.totalNum = historySubmitJob
            that.msg2.execNum = historyCompleteJob
          }

          // 更新右侧任务统计图
          const currentTaskType = that.taskType || 1
          if (currentTaskType === 1 && that.submitJobCompare0) {
            that.updateTaskDataForType(
              that.submitJobCompare0.xData,
              that.submitJobCompare0.used[that.count],
              that.compareId !== 0 && that.submitJobCompare?.used2
                ? that.submitJobCompare.used2[that.count] : undefined
            )
          } else if (currentTaskType === 2 && that.pendingJobCompare0) {
            that.updateTaskDataForType(
              that.pendingJobCompare0.xData,
              that.pendingJobCompare0.used[that.count],
              that.compareId !== 0 && that.pendingJobCompare?.used2
                ? that.pendingJobCompare.used2[that.count] : undefined
            )
          } else if (currentTaskType === 3 && that.resouceCompare0) {
            that.updateTaskDataForType(
              that.resouceCompare0.xData,
              that.resouceCompare0.used[that.count],
              that.compareId !== 0 && that.resouceCompare?.used2
                ? that.resouceCompare.used2[that.count] : undefined
            )
          }

          // 重绘图表
          that.drawLine()
          that.drawLine2()
        }, 2000)
      } else {
        // 没有缓存数据，使用原来的方法
        this.prepareForResume()

        if (this.taskId !== 0) {
          this.getJobData().then(() => {
            if (this.taskIdRes) {
              this.processJobData(this.taskIdRes, this.compareIdRes)
              this.dataAlreadyLoaded = true
            }
          })
        }
      }
    },

    /**
     * 准备恢复
     */
    prepareForResume () {
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 使用空数据结构，仅保持图表框架
      this.Data = {
        xAxis: [],
        yAxis: []
      }

      this.taskData = {
        xData: [],
        used: [],
        used2: [],
        multiTaskData: []
      }

      // 重置环图数据，避免显示旧数据
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
    },

    changeProgress () {
      this.count = Math.floor((this.percentage / 100) * this.total)
      this.nowCount = this.count
    },

    changeInterval () {
      this.$store.commit('changeInterval', this.value)
      this.Stop = true
      this.start()
    },

    formatTooltip (val) {
      return val + '%'
    },

    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
      if (this.myChart2) {
        this.myChart2.resize()
      }
    },

    handleTaskDetailClose () {
      this.taskDetailDialogVisible = false
    },

    handleTaskSubmitSuccess () {
      // 刷新任务列表
      this.$refs.table && this.$refs.table.init()
    },

    // 数据初始化方法 =============================

    /**
     * 初始化空图表数据结构
     * @returns {Object} 包含初始化后的图表数据结构
     */
    initEmptyChartData () {
      return {
        // 折线图数据
        chartData: {
          xAxis: [],
          yAxis: []
        },
        // 柱状图数据
        barData: {
          xData: [],
          used: [],
          used2: [],
          multiTaskData: []
        },
        // 任务信息
        taskInfo: {
          SnapshotTime: 0,
          NCenters: 0,
          NPops: 0
        },
        // 任务统计
        taskStats: {
          totalNum: 0,
          execNum: 0
        },
        // 任务详情
        taskDetail: {
          ID: undefined,
          NJobs: undefined,
          SnapshotTime: undefined,
          CompletedFlag: undefined,
          strategy: undefined
        }
      }
    },

    /**
     * 重置所有图表数据
     */
    resetAllChartData () {
      const emptyData = this.initEmptyChartData()

      // 重置各数据结构
      this.Data = emptyData.chartData
      this.taskData = emptyData.barData
      this.msg = emptyData.taskInfo
      this.msg2 = emptyData.taskStats
      this.taskDetail = emptyData.taskDetail

      // 重绘图表
      this.drawLine()
      this.drawLine2()

      // 重置状态
      this.count = 0
      this.buttonShow = true
      // 保持showButtom为true，以便显示暂停/继续按钮
      // this.showButtom = false; // 注释掉这行，保持按钮可见
      clearInterval(this.timer)
    },

    /**
     * 初始化任务类型数据对象
     * @returns {Object} 空任务数据对象
     */
    initTaskTypeData () {
      return {
        used: null,
        xData: [],
        used2: null
      }
    },

    // 图表配置方法 =============================

    /**
     * 创建折线图配置
     * @returns {Object} 折线图配置对象
     */
    createLineChartOption () {
      // 确保即使没有数据也显示图表框架
      const hasData = this.Data.xAxis && this.Data.xAxis.length > 0

      return {
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10]
        },
        color: '#32c5ff',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid',
              color: 'rgba(0, 0, 0, 0)'
            }
          },
          formatter: function (params) {
            params = [params[0]]
            let htmlStr = ''
            htmlStr += '<div>'
            htmlStr += '<div>'
            htmlStr += params[0].axisValue + 'h'
            htmlStr += '</div>'
            htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:#1890ff;"></span>'
            htmlStr += params[0].seriesName + params[0].value
            htmlStr += '</div>'
            return htmlStr
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        grid: {
          right: '5%',
          bottom: '25%',
          top: '12%',
          left: '10%'
        },
        xAxis: this.createXAxisConfig(),
        yAxis: this.createYAxisConfig(),
        series: [{
          name: '提交任务量',
          type: 'line',
          symbol: 'circle',
          symbolSize: 6,
          smooth: true,
          label: { show: false },
          zlevel: 1,
          z: 1,
          data: this.Data.yAxis || [],
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                { offset: 0, color: 'rgba(50,197,255,0)' },
                { offset: 1, color: 'rgb(22,93,255)' }
              ],
              global: false
            }
          }
        }],
        legend: {
          data: ['提交任务量'],
          left: 255,
          top: 225,
          itemHeight: 12,
          textStyle: {
            color: '#a1a1a1',
            fontSize: 12
          }
        },
        animation: true,
        animationDuration: function (idx) {
          return idx * 500
        },
        animationEasing: 'backln'
      }
    },

    /**
     * 创建X轴配置
     * @returns {Object} X轴配置对象
     */
    createXAxisConfig () {
      return {
        type: 'category',
        boundaryGap: false,
        data: this.Data.xAxis || [],
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(1, 145, 255, 0.3)'
          }
        },
        axisLabel: {
          show: true,
          showMaxLabel: true,
          interval: 'auto',
          fontSize: '12',
          lineHeight: 40,
          color: 'rgba(255, 255, 255,1)',
          fontFamily: 'Microsoft YaHei',
          fontWeight: 'normal',
          formatter: function (value) {
            return value + 'h'
          }
        },
        axisTick: {
          show: false
        }
      }
    },

    /**
     * 创建Y轴配置
     * @returns {Object} Y轴配置对象
     */
    createYAxisConfig () {
      return {
        type: 'value',
        name: '',
        show: true,
        nameTextStyle: {
          padding: [0, 0, 0, 0]
        },
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            type: 'dotted',
            color: 'rgba(1, 145, 255, 0.3)'
          }
        },
        nameGap: 10,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255,0.9)'
          }
        },
        axisLabel: {
          show: true,
          interval: 'auto',
          align: 'right',
          fontSize: '10',
          fontWeight: 'bold',
          color: 'rgba(255, 255, 255,0.9)'
        },
        scale: true,
        min: 0,
        splitNumber: 4
      }
    },

    /**
     * 创建柱状图配置
     * @returns {Object} 柱状图配置对象
     */
    createBarChartOption () {
      // 确保即使没有数据也显示图表框架
      const hasData = this.taskData.xData && this.taskData.xData.length > 0

      const option = {
        darkMode: true, // 启用暗黑模式
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.6)', // 调整颜色
            fontSize: 14
          },
          left: 'center', // 居中
          top: 'center' // 居中
        },
        tooltip: {
          trigger: 'axis',
          appendToBody: true,
          axisPointer: {
            type: 'shadow', // 更适合柱状图
            shadowStyle: {
              color: 'rgba(0, 190, 255, 0.05)'
            }
          },
          backgroundColor: 'rgba(0, 25, 50, 0.9)', // 深蓝背景
          borderColor: 'rgba(0, 190, 255, 0.7)', // 科技蓝边框
          borderWidth: 1,
          padding: [10, 15], // 调整内边距
          textStyle: {
            color: 'rgba(255, 255, 255, 0.95)',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 3px 10px rgba(0, 150, 255, 0.35); border-radius: 4px;'
        },
        legend: {
          show: hasData,
          top: '5px', // 进一步减少顶部间距
          left: 'center',
          orient: 'horizontal',
          itemWidth: 14,
          itemHeight: 10,
          itemGap: 8,
          icon: 'rect',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.9)',
            fontSize: 11
          },
          inactiveColor: 'rgba(255, 255, 255, 0.4)',
          // 减少图例的内边距
          padding: [0, 0, 5, 0] // 上右下左，减少上下边距
        },
        grid: {
          left: '1%',
          right: '3%',
          top: '50px', // 减少顶部空间，紧凑布局
          bottom: '10px',
          containLabel: true
        },
        yAxis: {
          type: 'category',
          data: this.taskData.xData || [],
          show: hasData, // MODIFIED: Dynamically show/hide yAxis
          axisLine: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLine
            lineStyle: {
              color: 'rgba(0, 190, 255, 0.4)',
              width: 1
            }
          },
          axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide yAxis.axisTick
          axisLabel: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLabel
            interval: 0,
            textStyle: {
              color: 'rgba(220, 220, 220, 0.95)',
              fontSize: 12,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
            },
            margin: 12
          },
          splitLine: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.splitLine
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          },
          // 添加斑马线背景 - 使用splitArea实现交替背景
          splitArea: {
            show: hasData,
            areaStyle: {
              color: [
                'rgba(0, 25, 50, 0.15)', // 第一种背景：深蓝色半透明，增加层次感
                'rgba(0, 120, 200, 0.06)' // 第二种背景：淡蓝色，降低透明度更加柔和
              ]
            }
          }
        },
        xAxis: {
          type: 'value',
          show: hasData, // MODIFIED: Dynamically show/hide xAxis
          axisLine: {
            show: hasData, // MODIFIED: Dynamically show/hide xAxis.axisLine
            lineStyle: {
              color: 'rgba(0, 190, 255, 0.4)',
              width: 1
            }
          },
          axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide xAxis.axisTick
          axisLabel: {
            show: hasData, // MODIFIED: Dynamically show/hide xAxis.axisLabel
            textStyle: {
              color: 'rgba(220, 220, 220, 0.85)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
            },
            margin: 10
          },
          splitLine: {
            show: hasData, // MODIFIED: Dynamically show/hide xAxis.splitLine
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed',
              width: 1
            }
          },
          scale: true,
          min: 0,
          splitNumber: 5
        },
        series: hasData ? this.createBarSeriesConfig() : [], // MODIFIED: Provide empty series array if no data
        // 柱状图间距配置 - 使用固定像素间距
        barCategoryGap: '15px', // 固定15px栏目间距，不随任务数量变化
        barGap: this.calculateBarGap(), // 动态计算同一类别内系列间距
        animation: true,
        animationDurationUpdate: 300,
        animationEasingUpdate: 'cubicInOut'
      }

      // 修复：正确处理多任务模式的系列数量限制
      // 如果是多任务模式（multiTaskData.length > 1），则不限制系列数量
      // 只有在真正的单任务模式下才限制为1个系列
      if (hasData &&
        (!this.taskData.multiTaskData || this.taskData.multiTaskData.length <= 1) &&
        this.compareId == 0 &&
        (!this.selectedTasks || this.selectedTasks.length <= 1)) {
        option.series.length = 1
      }

      return option
    },

    /**
     * 计算同一类别内系列间距
     * @returns {string} 系列间距百分比
     */
    calculateBarGap () {
      // 获取当前任务数量
      let taskCount = 1
      if (this.selectedTasks && this.selectedTasks.length > 0 && this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0) {
        taskCount = this.taskData.multiTaskData.length
      } else if (this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0) {
        taskCount = 2
      }

      // 根据任务数量调整系列间距
      // 控制同一栏目内多个固定宽度柱子之间的间距
      const baseGap = 5 // 减少基础系列间距
      const reductionFactor = Math.max(0, (taskCount - 2) * 0.5) // 每增加一个任务减少0.5%间距
      const finalGap = Math.max(1, baseGap - reductionFactor) // 最小间距1%

      // 计算系列间距: taskCount, baseGap, reductionFactor, finalGap

      return finalGap + '%'
    },

    /**
     * 获取固定的柱子宽度
     * @param {number} taskCount - 任务数量
     * @returns {number} 固定的柱子宽度
     */
    getFixedBarWidth (taskCount) {
      // 设置固定宽度，保证良好的视觉效果
      const fixedWidth = 14 // 固定14px宽度，既美观又不会太宽

      // 使用固定柱子宽度: taskCount, fixedWidth

      return fixedWidth
    },

    /**
     * 计算图表需要的高度
     * @param {number} cityCount - 城市数量
     * @param {number} taskCount - 任务数量
     * @returns {number} 计算出的图表高度
     */
    calculateChartHeight (cityCount, taskCount) {
      // 基础高度配置
      const baseHeight = 40 // 进一步减少基础高度

      // 根据任务数量动态调整每个城市项的高度
      // 任务越多，每个城市需要的高度越大
      let itemHeight = 30 // 减少单任务时的基础高度
      if (taskCount >= 2) {
        itemHeight = 30 + (taskCount - 1) * 15 // 进一步减少每个任务增加的高度到15px
      }

      const legendHeight = 30 // 进一步减少图例高度

      // 计算总高度
      const contentHeight = cityCount * itemHeight
      const totalHeight = baseHeight + contentHeight + legendHeight

      // 图表高度计算: cityCount, taskCount, itemHeight, contentHeight, totalHeight

      return Math.max(350, totalHeight) // 减少最小高度到350px
    },

    /**
     * 创建柱状图系列配置
     * @returns {Array} 包含系列配置的数组
     */
    createBarSeriesConfig () {
      const formatter = (param) => {
        if (this.taskType === 1) {
          return param.data || 0
        } else if (this.taskType === 2) {
          // 确保formatDuring2返回的是纯数字或带单位的字符串，以便后续可能的样式处理
          const value = this.formatDuring2(param.data || 0)
          return typeof value === 'number' ? value : value + 'h' // 假设formatDuring2可能返回数字或已格式化的字符串
        } else {
          return (param.data || 0).toFixed(2) + '%'
        }
      }

      const series = []

      // 调试信息
      // createBarSeriesConfig 调试信息:
      // selectedTasks: this.selectedTasks
      // selectedTasks.length: this.selectedTasks?.length
      // taskData.multiTaskData: this.taskData.multiTaskData
      // taskData.multiTaskData.length: this.taskData.multiTaskData?.length
      // taskType: this.taskType
      // 条件判断结果: this.selectedTasks && this.selectedTasks.length > 0 && this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0

      // 支持多个任务的显示
      if (this.selectedTasks && this.selectedTasks.length > 0 && this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0) {
        // 进入多任务模式，将创建 this.taskData.multiTaskData.length 个系列

        // 使用固定柱子宽度
        const taskCount = this.taskData.multiTaskData.length
        const cityCount = this.taskData.xData ? this.taskData.xData.length : 0
        const fixedBarWidth = this.getFixedBarWidth(taskCount)

        // 多任务模式：为每个选中的任务创建一个系列
        this.taskData.multiTaskData.forEach((taskData, index) => {
          // 创建第 index+1 个系列，任务ID: taskData.taskId, 策略: taskData.strategy
          const colors = [
            { start: 'rgba(30, 144, 255, 1)', end: 'rgba(100, 200, 255, 1)', border: 'rgba(130, 220, 255, 0.9)' },
            { start: 'rgba(255, 140, 0, 1)', end: 'rgba(255, 180, 80, 1)', border: 'rgba(255, 200, 120, 0.9)' },
            { start: 'rgba(50, 205, 50, 1)', end: 'rgba(100, 240, 100, 1)', border: 'rgba(130, 255, 130, 0.9)' },
            { start: 'rgba(138, 43, 226, 1)', end: 'rgba(180, 100, 255, 1)', border: 'rgba(200, 140, 255, 0.9)' },
            { start: 'rgba(0, 191, 255, 1)', end: 'rgba(80, 230, 255, 1)', border: 'rgba(120, 250, 255, 0.9)' },
            { start: 'rgba(255, 215, 0, 1)', end: 'rgba(255, 240, 80, 1)', border: 'rgba(255, 250, 120, 0.9)' },
            { start: 'rgba(64, 224, 208, 1)', end: 'rgba(120, 255, 240, 1)', border: 'rgba(150, 255, 250, 0.9)' },
            { start: 'rgba(106, 90, 205, 1)', end: 'rgba(150, 140, 240, 1)', border: 'rgba(180, 170, 255, 0.9)' }
          ]

          const colorSet = colors[index % colors.length]

          // 根据当前任务类型获取对应数据
          let data = []
          if (this.taskType === 1) {
            data = taskData.submitData || []
          } else if (this.taskType === 2) {
            data = taskData.pendingData || []
          } else if (this.taskType === 3) {
            data = taskData.resourceData || []
          }

          series.push({
            name: `${taskData.taskId}-${taskData.strategy}`,
            type: 'bar',
            barWidth: fixedBarWidth, // 保持固定柱子宽度
            z: index + 1,
            label: {
              show: true,
              position: 'right',
              distance: 3, // 减少标签距离，为更多柱子腾出空间
              textStyle: {
                color: 'rgba(255, 255, 255, 0.95)',
                fontSize: Math.max(8, Math.min(11, 12 - Math.floor(taskCount / 3))), // 根据任务数量动态调整字体大小
                fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              },
              formatter
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderColor: colorSet.border,
                shadowColor: colorSet.border,
                shadowBlur: 8
              }
            },
            data: data,
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: colorSet.start },
                { offset: 1, color: colorSet.end }
              ]),
              borderRadius: [0, 3, 3, 0], // 减少圆角，为细柱子优化
              borderColor: colorSet.border,
              borderWidth: 0.5
            }
          })
        })
      } else {
        // 进入兼容模式（单任务/双任务）
        // 兼容原有的单任务/双任务模式
        const taskCount = this.compareId !== 0 && this.taskData.used2 && this.taskData.used2.length > 0 ? 2 : 1
        const cityCount = this.taskData.xData ? this.taskData.xData.length : 0
        const fixedBarWidth = this.getFixedBarWidth(taskCount)

        series.push({
          name: this.taskId === 0 ? '' : this.taskId + '-' + this.Strategy1,
          type: 'bar',
          barWidth: fixedBarWidth, // 保持固定柱子宽度
          z: 1,
          label: {
            show: true,
            position: 'right',
            distance: 5,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.95)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              // fontWeight: 'bold' // 可以考虑加粗
            },
            formatter
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderColor: 'rgba(0, 230, 255, 1)',
              shadowColor: 'rgba(0, 230, 255, 0.8)',
              shadowBlur: 10
            }
          },
          data: this.taskData.used || [],
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(30, 144, 255, 1)' },
              { offset: 1, color: 'rgba(100, 200, 255, 1)' }
            ]),
            borderRadius: [0, 5, 5, 0], // 右侧圆角
            borderColor: 'rgba(130, 220, 255, 0.9)',
            borderWidth: 1,
            shadowColor: 'rgba(80, 180, 255, 0.7)',
            shadowBlur: 5
          }
        })

        if (this.compareId !== 0 && this.taskData.used2 && this.taskData.used2.length > 0) {
          series.push({
            name: this.compareId === 0 ? '' : this.compareId + '-' + this.Strategy2, // 使用短横线连接
            type: 'bar',
            barWidth: fixedBarWidth, // 保持固定柱子宽度
            z: 2,
            label: {
              show: true,
              position: 'right',
              distance: 5,
              textStyle: {
                color: 'rgba(255, 255, 255, 0.95)',
                fontSize: 11,
                fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              },
              formatter
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderColor: 'rgba(170, 190, 255, 1)',
                shadowColor: 'rgba(170, 190, 255, 0.8)',
                shadowBlur: 10
              }
            },
            data: this.taskData.used2 || [],
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: 'rgba(255, 140, 0, 1)' },
                { offset: 1, color: 'rgba(255, 180, 80, 1)' }
              ]),
              borderRadius: [0, 5, 5, 0],
              borderColor: 'rgba(255, 200, 120, 0.9)',
              borderWidth: 1,
              shadowColor: 'rgba(255, 160, 60, 0.7)',
              shadowBlur: 5
            }
          })
        } else if (this.compareId !== 0) { // 如果有compareId但数据为空，也画一个空的系列占位，使得图例正确显示
          series.push({
            name: this.compareId + '-' + this.Strategy2, // 使用短横线连接
            type: 'bar',
            data: []
          })
        }
      }

      // 最终创建的系列数量: series.length
      // 系列详情: series.map(s => ({ name: s.name, dataLength: s.data?.length, barWidth: s.barWidth }))
      return series
    }

  },
  beforeDestroy () {
    // 清理资源
    if (this.myChart) {
      this.myChart.dispose()
    }
    if (this.myChart2) {
      this.myChart2.dispose()
    }
    window.removeEventListener('resize', this.resize)
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>
<style scoped>
/* 确保页面没有默认边距和溢出 */
:deep(body) {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

:deep(html) {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.coopera-pic {
  margin: 0px 220px 0px 220px;
  background-image: url("../static/screen1/cooperativeUnit.jpg");
  background-repeat: no-repeat;
  background-size: 105%;
  height: 185px;
  width: 100%;
  margin-top: -150px;
  /* background-position-x: 290px; */
}

.el-header {
  height: 125px !important;
  background-image: url("../static/screen1/header.png");
  background-size: 100%;
}

.background {
  background-image: url("../static/screen1/background.png");
  background-size: 100% 100%;
  height: 130vh;
  /* 考虑装饰图片空间，略微增加高度 */
  transform-origin: top left;
  transform: scale(0.8);
  width: 125%;
  /* 100 / 0.8 */
  overflow: hidden;
}

.title {
  margin-top: 20px;
  font-size: 3rem;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  /* transform: translateY(-50%); */
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  text-align: center;
  margin-left: 20px;
}

/* .subTitle {
    margin-top: 1px;
    font-family: "思源黑体 CN-Regular";
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0px;
    line-height: 20px;
    text-align: center;
    width: 100%;
    font-style: italic;
    font-weight: normal;
    filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  } */

.subTitle span {
  letter-spacing: 1px;
}

.title1 {
  color: rgb(255, 255, 255);
  font-size: 20px;
  text-align: left;
  /* box-shadow: 0px 2px 6px 0px rgba(217, 235, 255, 0.64); */
  font-family: SourceHanSansSC-medium;
}

.province {
  width: 100px;
  height: 100px;
}

.arrow {
  background-size: 100% 100%;
  background-image: url("../static/screen1/arrow2.png");
  width: 50px;
  height: 35px;
  position: relative;
  top: -5px;
  background-repeat: no-repeat;
}

.linear-gradient {
  background: linear-gradient(to right,
      rgba(255, 0, 0, 0),
      rgba(255, 255, 255, 0.2));
  margin: 6px 0 6px 0;
  box-sizing: content-box;
  background-clip: content-box;
}

.el-main {
  /* height: 900px; */
  z-index: 20;
  overflow: hidden;
  /* padding: 10px; */
  /* padding-top: 30px; */
  padding: 0px 20px 100px 20px !important; /* 为footer留出空间 */
}

.el-container.is-vertical {
  height: 100%;
}

.showMt {
  margin-top: 0px !important;
  width: 570px;
}

.buttonStyle {
  height: 50px;
  color: white;
  line-height: 50px;
  text-align: center;
  background-color: rgba(255, 255, 255, 10%);
  cursor: pointer;
  min-width: 200px;
  font-weight: 800;
}

.check {
  font-weight: 800;
  background-image: linear-gradient(rgba(30, 231, 231, 40%),
      rgba(255, 255, 255, 10%),
      rgba(255, 255, 255, 0%));
}

.footer {
  background-image: url("../assets/footer.svg");
  background-repeat: no-repeat;
  /* min-height: 20px !important; */
  background-size: 100% 100%;
  width: 100%;
  height: 90px;
  /* 确保footer在缩放后正确显示在底部 */
  position: absolute;
  bottom: 40px; /* 向上移动20px，让footer更明显 */
  left: 0;
  z-index: 10;
}

.wrapper {
  position: absolute;
  left: 25px;
}

.icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.icon2 {
  display: inline-block;
  width: 25px;
  height: 25px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.text {
  display: inline-block;
  line-height: 18px;
  height: 18px;
  position: relative;
  top: -2px;
  margin-right: 2px;
}

.img1 {
  background-image: url("../static/screen1/intellectual.svg");
}

.img2 {
  background-image: url("../static/screen1/super.svg");
}

.img3 {
  background-image: url("../static/screen1/eandw.svg");
}

.img4 {
  background-image: url("../assets/statusEd.svg");
}

.img5 {
  background-image: url("../assets/statusIng.svg");
}

.img6 {
  background-image: url("../assets/statusUn.svg");
}

.buttonStyle2 {
  /* height: 50px; */
  color: white;
  /* line-height: 50px; */
  /* text-align: center; */
  /* background-color: rgba(255, 255, 255, 10%); */
  cursor: pointer;
  min-width: 150px;
  margin-bottom: 10px;
}

.check2 {
  color: #fff !important;
  background-image: linear-gradient(to right,
      rgba(24, 144, 255, 0%),
      rgba(24, 144, 255, 10%),
      rgba(24, 144, 255, 40%));
}

.statusWrapper {
  position: absolute;
  right: 220px;
  top: 560px;
}

.statusWrapper2 {
  position: absolute;
  right: 220px;
  top: 650px;
}

.title2 {
  color: rgba(255, 255, 255, 0.7);
  line-height: 40px;
}

.title3 {
  line-height: 40px;
  color: rgba(255, 255, 255, 0.7);
  width: 140px;
}

.arrow1 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrow10TB.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow2 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowSD.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow3 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowMPLS.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.mt-2 {
  margin-top: 20px;
}

.ml-2 {
  margin-left: 65px;
}

.smalltitle {
  letter-spacing: 0px;
  font-size: 2.6rem;
}

.dropdown {
  width: 225px;
  position: fixed;
  right: 30px;
  top: 20px;
  z-index: 200;
}

.passwordPos {
  margin-top: 40vh;
}

.passConfirm {
  text-align: center;
}

.loading {
  position: relative;
  top: 250px;
}

.loading-text {
  color: #fff;
  font-weight: 800;
}

.taskDetailWrapper {
  /* width: 550px;
  height: 280px; */
  /* background-color: #0A1E37; */
  background-color: rgba(10, 30, 55, 0.25);
  background-size: cover;
  background-position: center center;
  /* overflow: hidden;
  filter: unset; */
  padding: 15px;
  position: absolute;
  right: 0px;
  top: 150px;
  min-height: 820px;
  border: 1px solid rgba(0, 190, 255, 0.4);
  /* 调整边框颜色 */
  width: 425px;
  box-shadow: inset 0 0 20px rgba(18, 137, 221, 0.2);
  /* 增加内阴影 */
  border-radius: 6px;
  /* 轻微圆角 */
}

.button {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
}

.mt {
  margin-top: 10px;
  /* 调整上边距 */
  margin-bottom: 20px;
  /* 调整下边距 */
}

.loading {
  width: 100%;
  height: 100%;
  position: absolute;
  /* top: 0px; */
  z-index: 200;
}

.dv-loading {
  position: relative;
  top: -20%;
}

/* .check {
  color: rgb(50, 197, 255);
  font-family: 思源黑体;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0px;
} */
.check3 {
  color: #FFFFFF !important;
  /* Changed for selected text */
  font-family: 思源黑体;
  font-size: 15px;
  font-weight: 700;
  letter-spacing: 0;
  text-shadow: 0 0 7px rgba(0, 180, 255, 0.7);
  /* Adjusted glow for selected text */
}

.button2 {
  color: #CFD8DC;
  /* Changed for non-selected text */
  font-family: 思源黑体;
  font-size: 15px;
  text-align: center;
  cursor: pointer;
  font-weight: 700;
  display: block;
  width: 100%;
  padding: 12px 5px;
  text-decoration: none;
  transition: color 0.2s ease-in-out, text-shadow 0.2s ease-in-out;
  /* Updated transition */
  white-space: nowrap;
  /* Kept for single line */
  /* overflow: hidden; */
  /* Removed */
  /* text-overflow: ellipsis; */
  /* Removed */
}

.bg2 {
  box-sizing: border-box;
  background: linear-gradient(to bottom, #303E4D, #242F3A);
  /* New background */
  border: 1px solid rgba(0, 140, 190, 0.3);
  /* New border */
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.25);
  /* New box-shadow */
  padding: 0;
  text-align: center;
  margin-bottom: 2px;
  border-radius: 3px;
  /* New border-radius */
  transition: background 0.2s ease-in-out, border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  /* New transition */
  cursor: pointer;
  /* transform: none; */
  /* Ensure no transform */
}

.bg2:hover {
  background: linear-gradient(to bottom, #38495A, #293643);
  /* New hover background */
  border-color: rgba(0, 170, 220, 0.5);
  /* New hover border-color */
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 5px rgba(0, 170, 220, 0.2);
  /* New hover box-shadow */
  /* transform: none; */
  /* Ensure no transform */
}

.bg2:active {
  background: linear-gradient(to bottom, #25303D, #1E2730);
  /* New active background */
  box-shadow: inset 0 2px 3px rgba(0, 0, 0, 0.35);
  /* New active box-shadow */
  /* transform: none; */
  /* Ensure no transform */
  border-color: rgba(0, 140, 190, 0.2);
  /* Slightly subdued border on active */
}

.bg2:hover .button2 {
  color: #FFFFFF;
  /* New hover text color */
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
  /* New hover text-shadow */
}

/* Selected tab styles */
.bg2.active-tab {
  background: linear-gradient(to bottom, #1A4E6E, #10354F);
  /* New selected background */
  border-color: #00AEEF;
  /* New selected border-color */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 174, 239, 0.4);
  /* New selected box-shadow */
  /* transform: none; */
  /* Ensure no transform */
}

.bg2.active-tab:hover {
  background: linear-gradient(to bottom, #205A7C, #16405A);
  /* New selected hover background */
  border-color: #00C0FF;
  /* New selected hover border-color */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15), 0 0 12px rgba(0, 191, 255, 0.5);
  /* New selected hover box-shadow */
}

/* Ensure selected text style priority - already covered by .check3 with !important and specific text-shadow */
.bg2.active-tab .button2.check3 {
  color: #FFFFFF !important;
  /* Explicitly ensuring for active tab text */
  text-shadow: 0 0 7px rgba(0, 180, 255, 0.7);
  /* Explicitly ensuring for active tab text */
  font-weight: 700;
  font-size: 15px;
}

.progress {
  /* position: fixed;
  bottom: 20px;
  right: 0px; */
  width: 100%;
  z-index: 100;
  height: 120px;
  padding: 20px 5px 20px 5px;
  border-radius: 5%;
  background: transparent;
  position: absolute;
  top: 770px;
  /* left: 200px; */
  /* margin:0 auto */
}

.el-button--small {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

.progressStyle {
  width: 220px;
  display: inline-block;
  height: 20px;
  margin-right: 10px;
  margin-left: 20px;
}

.interval {
  margin-top: 0px;
  max-height: 30px;
  width: 100px;
  /* overflow: hidden; */
}

.el-select {
  width: 80px;
}

.el-select>.el-input {
  background: transparent !important;
}

:deep(.el-input__inner) {
  background: transparent !important;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

:deep(.el-progress__text) {
  color: #fff !important;
}

.index {
  z-index: 120;
}

.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
}

:deep(.el-input__inner) {
  height: 30px;
}

:deep(.el-select .el-input .el-select__caret) {
  position: absolute;
  top: 6px;
  right: 10px;
}

:deep(.el-select .el-input .el-select__caret.is-reverse) {
  position: absolute;
  top: -5px !important;
  right: 10px;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.3);
}

.taskName {
  display: inline-block;
  margin-right: 20px;
  font-size: 45px;
  color: #fff;
}

.taskValue {
  display: inline-block;
  font-size: 45px;
  color: #fff;
}

.taskMsg {
  position: absolute;
  right: 150px;
  /* width: 120px; */
}

/* 图表滚动容器样式 */
.chart-scroll-container {
  margin-top: 15px;
  /* 减少与按钮组的间距 */
  background-color: rgba(10, 30, 55, 0.25);
  border-radius: 6px;
  /* 圆角 */
  padding: 8px 5px;
  /* 进一步减少内边距，增加图表可用空间 */
  /* 移除边框，减少视觉干扰 */
  /* border: 1px solid rgba(0, 190, 255, 0.15); */
  box-shadow: inset 0 0 10px rgba(0, 25, 50, 0.15);
  /* 减轻内阴影 */
}

/* echart2 容器的样式调整 */
div[ref="echart2"] {
  /* 移除之前的样式，现在由父容器控制 */
}

/* Element UI 弹窗缩放样式 - 与主窗口保持一致的80%缩放 */
:deep(.el-dialog__wrapper) {
  zoom: 0.8;
}

/* 如果上面的选择器不生效，使用更具体的选择器 */
:deep(.el-overlay) {
  zoom: 0.8;
}

/* 自定义滚动条样式 */
.chart-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.chart-scroll-container::-webkit-scrollbar-track {
  background: rgba(0, 25, 50, 0.3);
  border-radius: 4px;
}

.chart-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(0, 190, 255, 0.6);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.chart-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 190, 255, 0.8);
}

/* Firefox 滚动条样式 */
.chart-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 190, 255, 0.6) rgba(0, 25, 50, 0.3);
}

.chart-container {
  display: flex;
  align-items: center;
  height: 240px;
  /* background: linear-gradient(135deg,
    rgba(0, 12, 30, 0.97),
    rgba(0, 18, 40, 0.95)
  ); */
  /* background-color: #0A1E37; */
  /* Previous opaque color */
  background-color: rgba(10, 30, 55, 0.25);
  /* Restored transparency */
  position: relative;
  border-radius: 12px;
  border: 1px solid rgba(0, 217, 255, 0.25);
  box-shadow:
    0 0 25px rgba(0, 0, 0, 0.3),
    inset 0 0 20px rgba(0, 217, 255, 0.05);
  overflow: hidden;
  /* backdrop-filter: blur(10px); */
  /* 毛玻璃效果已移除 */
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

/* 任务按钮容器样式 */
.task-buttons-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.data-card {
  /* background: linear-gradient(135deg,
    rgba(0, 15, 35, 0.85),
    rgba(0, 25, 55, 0.8)
  ); */
  /* background-color: #0A1E37; */
  /* Previous opaque color */
  background-color: rgba(10, 30, 55, 0.25);
  /* Restored transparency */
  border-radius: 10px;
  padding: 12px 10px;
  text-align: center;
  position: relative;
  box-shadow:
    0 5px 15px rgba(0, 0, 0, 0.25),
    inset 0 0 15px rgba(0, 217, 255, 0.05);
  border: 1px solid rgba(0, 217, 255, 0.2);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  /* backdrop-filter: blur(5px); */
  /* 毛玻璃效果已移除 */
  margin-bottom: 10px;
  transform: translateZ(0);
}
</style>
