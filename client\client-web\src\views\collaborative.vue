<template>
  <div id="screen3" class="Body">
    <div class="background" @click="hiddenNavbar()">
      <el-container>
        <el-header>
          <el-row type="flex" justify="center">
            <el-col :span="12">
              <div class="title">
                <span class="linear-gradient-text"
                  >基于全网调度能力的协同计算示范</span
                >
              </div>
            </el-col>
          </el-row>
        </el-header>
        <dropdown class="dropdown" :type="'collaborative'"></dropdown>
        <el-main>
          <el-row type="flex" justify="space-around">
            <el-col :span="5">
              <div class="side-title">鹏城·脑海两千亿参数</div>
              <div class="side-subtitle">PengCheng-Mind 200B</div>
              <div class="side-desc">
                <div>基于：MindSpore</div>
                <div>模型分片：3456</div>
              </div>

              <div class="side-data-desc">
                <span>0-150B Tokens 主要数据分布</span>
              </div>
              <div style="height: 200px">
                <div id="150B" style="width: 100%; height: 100%"></div>
              </div>
              <div class="side-data-desc">
                <span>150B-330B Tokens 主要数据分布</span>
              </div>
              <div style="height: 200px">
                <div id="330B" style="width: 100%; height: 100%"></div>
              </div>
              <div class="side-data-desc">
                <span>330B-530B Tokens 主要数据分布</span>
              </div>
              <div style="height: 250px">
                <div id="530B" style="width: 100%; height: 100%"></div>
              </div>
            </el-col>

            <el-col :span="14">
              <div style="position: relative; top: -200px">
                <el-row type="flex" justify="center" align="middle">
                  <el-col :span="14">
                    <div class="circle">
                      <div
                        style="
                          position: absolute;
                          top: 40%;
                          width: 300px;
                          height: 300px;
                          margin: -50px 0 0 0;
                          text-align: center;
                        "
                      >
                        <div class="chatmind">脑海大模型</div>
                        <div class="train">已持续训练</div>
                        <div class="cardTime">
                          <numByte :num="cardTime"></numByte>
                          <span style="font-size: 1.2rem">卡时</span>
                        </div>
                        <div class="token">Tokens</div>
                        <div class="tokenNum">
                          <numByte :num="tokensNum"></numByte>
                          <span style="font-size: 1.2rem">B</span>
                        </div>
                      </div>
                      <div class="center-item1">
                        >
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">鹏城云脑Ⅱ</div>
                      </div>
                      <div class="center-item2">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">西安智算中心</div>
                      </div>
                      <div class="center-item3">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">沈阳智算中心</div>
                      </div>
                      <div class="center-item4">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">武汉智算中心</div>
                      </div>
                      <div class="center-item5">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">大连智算中心</div>
                      </div>
                      <div class="center-item6">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">天津智算中心</div>
                      </div>
                      <div class="center-item7">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">北京智算中心</div>
                      </div>
                      <div class="center-item8">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">长春智算中心</div>
                      </div>
                      <div class="center-item9">
                        <div
                          style="width: 140px; height: 140px"
                          class="center-item-pic"
                        ></div>
                        <div class="center-item-text">成都智算中心</div>
                      </div>

                      <div class="center-subtitle1">模型部署及体验</div>
                      <div class="center-subtitle2">模型预训练</div>
                      <div class="center-subtitle3">模型评测</div>
                      <div class="center-subtitle4">模型微调</div>
                    </div>
                    <!-- <div id="coreCircleOne"></div> -->
                    <!-- <div id="coreCircleTwo"></div> -->
                  </el-col>
                </el-row>
                <!-- <el-row type="flex" justify="center" align='middle'>
                  <el-col :span="14"><div id="coreCircleOne"></div>

                  </el-col>
                </el-row>

                <el-row type="flex" justify="center" align='middle'>
                  <el-col :span="14"><div id="coreCircleTwo"></div></el-col>
                </el-row> -->

                <el-row type="flex" justify="center" align="middle">
                  <div
                    class="wrapper"
                    style="position: absolute; transform: translate(-16px)"
                  >
                    <dv-flyline-chart-enhanced
                      :config="config"
                      style="width: 1000px; height: 800px"
                      class="flyWrapper"
                    />
                  </div>
                  <el-col :span="24">
                    <div class="circle1">
                      <div class="arrow"></div>

                      <!-- <div class="arrow" style="transform: rotate(120deg);"></div> -->
                      <!-- <div class="arrow" style="transform: rotate(240deg);"></div> -->
                    </div>
                  </el-col>
                </el-row>

                <!-- <el-row type="flex" justify="center" align='middle'>
                  <div style="width:160px;height:200px" class="center-item1">1212</div>
                  <div></div>
                </el-row> -->
                <!-- <div id="coreCircleOne"></div>
                <div id="coreCircleTwo"></div> -->
                <!-- <div id="sencondCircle" class="holoLayer">
                  <div class="slider a">
                    <div class="sliderPoint a"></div>
                  </div>
                </div> -->
                <!-- <div class="circle1">
                    <div class="arrow"></div>
                </div> -->
              </div>
            </el-col>

            <el-col :span="5">
              <div class="side-title">鹏城·脑海七十亿参数</div>
              <div class="side-subtitle">PengCheng-Mind 7B</div>
              <div class="side-desc">
                <div>支持 NPU 版本到 GPU 版本的转换</div>
              </div>
              <div class="side-data-desc">
                <span>36.5B-123.5B Tokens 主要数据分布</span>
              </div>
              <div style="height: 160px; margin-bottom: -25px">
                <div id="123B" style="width: 100%; height: 100%"></div>
              </div>
              <div class="side-data-desc">
                <span>123.5B-197.5B Tokens 主要数据分布</span>
              </div>
              <div style="height: 160px; margin-bottom: -25px">
                <div id="197B" style="width: 100%; height: 100%"></div>
              </div>
              <div class="side-data-desc">
                <span>197.5B-284.5B Tokens 主要数据分布</span>
              </div>
              <div style="height: 160px; margin-bottom: -25px">
                <div id="284B" style="width: 100%; height: 100%"></div>
              </div>
              <div class="side-data-desc">
                <span>284.5B-344.5B Tokens 主要数据分布</span>
              </div>
              <div style="height: 160px; margin-bottom: -25px">
                <div id="344B" style="width: 100%; height: 100%"></div>
              </div>
            </el-col>
          </el-row>
        </el-main>
      </el-container>
    </div>
  </div>
</template>

<script>
// import * as echarts from 'echarts';
// // import * as echarts from 'echarts/core';
// import { BarChart } from 'echarts/charts';
import numByte from '@/components/numByte'
import dropdown from '@/components/Dropdown'
export default {
  components: {
    numByte,
    dropdown: dropdown
  },
  data () {
    return {
      cardTime: 11064936.96,
      tokensNum: 1757.47,
      category200BList: [
        ['医学', '教育学', '法律', '经济', '文学'],
        ['历史   ', '医学', '法律', '经济', '文学'],
        ['教育学', '医学', '法律', '经济', '文学']
      ],
      data200B: [
        [37.04, 11.98, 7.03, 4.83, 4.8],
        [28.42, 9.59, 7.44, 5.53, 4.23],
        [24.94, 10.29, 7.73, 5.17, 3.97]
      ],
      category7BList: [
        ['教育学', '历史', '法律', '经济', '文学'],
        ['教育学', '医学', '历史', '经济', '文学'],
        ['医学   ', '政治', '法律', '经济', '文学'],
        ['历史   ', '医学', '经济', '法律', '文学'],
        ['政治', '教育学', '经济', '法律', '文学']
      ],
      data7B: [
        [15.38, 13.63, 6.64, 4.72, 4.67],
        [20.93, 11.42, 5.48, 5.02, 4.91],
        [19.42, 11.95, 8.62, 5.43, 4.82],
        [29.72, 20.27, 7.29, 3.47, 3.44],
        [18.27, 12.97, 10.65, 4.16, 4.12]
      ],
      config: {
        points: [
          {
            name: '大连智算',
            coordinate: [0.76, 0.88],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型大连',
            coordinate: [0.62, 0.63],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '天津智算中心',
            coordinate: [0.52, 0.99],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型天津',
            coordinate: [0.52, 0.685],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '北京智算中心',
            coordinate: [0.24, 0.85],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型北京',
            coordinate: [0.42, 0.65],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '长春智算中心',
            coordinate: [0.12, 0.55],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型长春',
            coordinate: [0.37, 0.5],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '成都智算中心',
            coordinate: [0.18, 0.23],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型成都',
            coordinate: [0.39, 0.4],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型鹏城云脑',
            coordinate: [0.48, 0.31],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '鹏城云脑',
            coordinate: [0.4, 0.035],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型西安智算',
            coordinate: [0.61, 0.35],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '西安智算',
            coordinate: [0.68, 0.058],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型沈阳智算',
            coordinate: [0.64, 0.4],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '沈阳智算',
            coordinate: [0.87, 0.28],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '脑海大模型武汉智算',
            coordinate: [0.66, 0.55],
            halo: {
              show: false,
              radius: 80
            }
          },
          {
            name: '武汉智算',
            coordinate: [0.89, 0.65],
            halo: {
              show: false,
              radius: 80
            }
          }
        ],
        lines: [
          {
            source: '脑海大模型大连',
            target: '大连智算',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型天津',
            target: '天津智算中心',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型北京',
            target: '北京智算中心',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型长春',
            target: '长春智算中心',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型成都',
            target: '成都智算中心',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型鹏城云脑',
            target: '鹏城云脑',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型西安智算',
            target: '西安智算',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型沈阳智算',
            target: '沈阳智算',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '脑海大模型武汉智算',
            target: '武汉智算',
            width: 1,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '大连智算',
            target: '脑海大模型大连',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211,.4)'
          },
          {
            source: '天津智算中心',
            target: '脑海大模型天津',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          },
          {
            source: '北京智算中心',
            target: '脑海大模型北京',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          },
          {
            source: '长春智算中心',
            target: '脑海大模型长春',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          },
          {
            source: '成都智算中心',
            target: '脑海大模型成都',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          },
          {
            source: '鹏城云脑',
            target: '脑海大模型鹏城云脑',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          },
          {
            source: '西安智算',
            target: '脑海大模型西安智算',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          },
          {
            source: '沈阳智算',
            target: '脑海大模型沈阳智算',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          },
          {
            source: '武汉智算',
            target: '脑海大模型武汉智算',
            width: 1,
            color: '#0184d3',
            duration: [10, 20],
            orbitColor: 'rgba(1, 132, 211, .4)'
          }
        ],
        k: 10,
        curvature: 100
      }
      // timer: null,
    }
  },

  mounted () {
    this.drawEcharts('150B', this.category200BList[0], this.data200B[0])
    this.drawEcharts('330B', this.category200BList[1], this.data200B[1])
    this.drawEcharts('530B', this.category200BList[2], this.data200B[2])

    // this.drawEcharts("36B", this.category7BList[0], this.data7B[0]);
    this.drawEcharts('123B', this.category7BList[1], this.data7B[1])
    this.drawEcharts('197B', this.category7BList[2], this.data7B[2])
    this.drawEcharts('284B', this.category7BList[3], this.data7B[3])
    this.drawEcharts('344B', this.category7BList[4], this.data7B[4])
    this.addnum()
    // window.addEventListener("visibilitychange", function () {
    //   if (document.visibilityState == "visible") {
    //     window.location.reload();
    //   }
    // });
  },
  methods: {
    addnum () {
      setInterval(() => {
        this.cardTime = this.cardTime + 6.9
      }, 5000)
      setInterval(() => {
        this.tokensNum = this.tokensNum + 0.01
      }, 60000)
    },
    drawEcharts (id, categoryList, data) {
      // var chart150B = this.$echarts.init(document.getElementById('150B'));
      var chart150B = this.$echarts.init(document.getElementById(id))
      var option = {
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'shadow'
        //   }
        // },
        legend: {},
        grid: {
          left: '-13%',
          // right: '4%',
          bottom: 0,
          top: 8,
          containLabel: true,
          width: '85%'
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          show: false
        },
        yAxis: {
          type: 'category',
          // data: ['医学', '教育学', '法律', '经济', '文学'],
          data: categoryList,
          // show: false,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 65,
            align: 'left',
            fontSize: '20',
            color: '#C2C3C3'
          }
        },
        series: [
          {
            type: 'bar',
            // data: [37.04, 11.98, 7.03, 4.83, 4.8].sort(function(a,b){return a-b}),
            data: data.sort(function (a, b) {
              return a - b
            }),
            label: {
              show: true,
              position: 'right',
              formatter: '{c}%',
              fontSize: '22',
              color: '#C2C3C3'
            },
            barWidth: '25%',
            barCategoryGap: '0%',
            barGap: '5%',
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(1, 1, 0, 0, [
                {
                  offset: 0,
                  color: '#0184D3'
                },
                {
                  offset: 1,
                  color: '#0F0A1E'
                }
              ])
            }
          }
        ]
      }
      chart150B.setOption(option)
      window.addEventListener('visibilitychange', () => {
        if (document.visibilityState == 'visible') {
          if (chart150B) {
            chart150B.resize()
          }
        }
      })
      window.addEventListener('resize', () => {
        if (chart150B) {
          chart150B.resize()
        }
      })
    },
    hiddenNavbar () {
      var target = document.getElementById('navbarToggleExternalContent')
      target.className = 'collapse'
    }
  }
}
</script>

<style scoped>
.circle1 {
  margin: 0 auto;
  width: 800px;
  height: 800px;
  border: 10px solid rgb(16, 31, 65);
  border-radius: 50%;
  /* position:absolute; */
  animation: rotate 4s linear infinite;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.arrow::before,
.arrow::after {
  content: "";
  width: 0;
  height: 0;
  border: 10px solid rgb(16, 31, 65);
  position: absolute;
}

.arrow::before {
  border-top: none;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  top: -10px;
  left: 0;
}

.arrow::after {
  border-bottom: none;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  bottom: -10px;
  left: 0;
}

@keyframes rotate {
  0% {
    transform: translateX(0) rotate(0deg);
  }
  100% {
    transform: translateX(0) rotate(360deg);
  }
}

.center-item1 {
  position: relative;
  right: 15px;
  bottom: 370px;
  z-index: 999;
}
.center-item2 {
  position: relative;
  left: 270px;
  bottom: 520px;
  z-index: 999;
}
.center-item3 {
  position: relative;
  left: 490px;
  bottom: 510px;
  z-index: 999;
}
.center-item4 {
  position: relative;
  left: 520px;
  bottom: 360px;
  z-index: 999;
}
.center-item5 {
  position: relative;
  left: 390px;
  bottom: 280px;
  z-index: 999;
}
.center-item6 {
  position: relative;
  left: 100px;
  bottom: 380px;
  z-index: 999;
}
.center-item7 {
  position: relative;
  right: 180px;
  bottom: 670px;
  z-index: 999;
}
.center-item8 {
  position: relative;
  right: 300px;
  bottom: 1100px;
  z-index: 999;
}
.center-item9 {
  position: relative;
  right: 250px;
  bottom: 1550px;
  z-index: 999;
}
.center-item-pic {
  background-image: url("../static/screen3/center-item.jpg");
  background-repeat: no-repeat;
  background-size: 50%;
}
.center-item-text {
  color: white;
  font-size: 1.2rem;
}
.center-subtitle1 {
  position: relative;
  right: 120px;
  bottom: 1600px;
  z-index: 999;
  color: rgb(1, 145, 255);
  font-family: 思源黑体;
  font-size: 2.2rem;
  font-weight: 900;
}
.center-subtitle2 {
  position: relative;
  left: 230px;
  bottom: 1650px;
  z-index: 999;
  color: rgb(1, 145, 255);
  font-family: 思源黑体;
  font-size: 2.2rem;
  font-weight: 900;
}
.center-subtitle3 {
  position: relative;
  right: 100px;
  bottom: 1250px;
  z-index: 999;
  color: rgb(1, 145, 255);
  font-family: 思源黑体;
  font-size: 2.2rem;
  font-weight: 900;
}
.center-subtitle4 {
  position: relative;
  left: 250px;
  bottom: 1300px;
  z-index: 999;
  color: rgb(1, 145, 255);
  font-family: 思源黑体;
  font-size: 2.2rem;
  font-weight: 900;
}

.title {
  margin-top: 20px;
  font-size: 3rem;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  /* transform: translateY(-50%); */
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  margin-bottom: 120px;
}
.linear-gradient-text {
  background: linear-gradient(
    to bottom,
    rgb(255, 255, 255),
    rgb(207, 242, 255)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 3rem;
  padding-left: 5px;
}
.el-main {
  margin: 20px 70px 0 70px;
  overflow: hidden;
  position: relative;
  /* padding-top:0px; */
  padding-bottom: 0px;
}

.side-title {
  /* margin-top: 70px; */
  color: rgb(50, 197, 255);
  font-family: "思源黑体 CN-Regular";
  font-size: 1.8rem;
  font-weight: 900;
  line-height: 52px;
  letter-spacing: 0px;
  text-align: left;
}

.side-subtitle {
  /* margin-top: 2.3rem; */
  color: rgb(255, 255, 255);
  font-family: "思源黑体 CN-Regular";
  font-size: 2.1rem;
  font-weight: 900;
  line-height: 70px;
  letter-spacing: 0px;
  text-align: left;
}

.side-desc {
  color: rgb(194, 195, 195);
  font-family: "思源黑体 CN-Regular";
  font-size: 1.2rem;
  font-weight: 500;
  /* line-height: 52px; */
  letter-spacing: 0px;
  text-align: left;
}

.side-data-desc {
  background: linear-gradient(
    90deg,
    rgba(40, 12, 171, 0.52),
    rgba(40, 12, 171, 0) 100%
  );
  color: rgb(255, 255, 255);
  font-family: "思源黑体 CN-Regular";
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 52px;
  letter-spacing: 0px;
  text-align: left;
  padding-left: 20px;
  margin-top: 20px;
}

.circle {
  top: 550px;
  position: relative;
  margin: 0 auto;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  box-shadow: 0 0 20px #fff, -20px 0 80px #f0f, 20px 0 80px #0ff,
    inset 0 0 50px #fff, inset 50px 0 80px #f0f, inset -50px 0 80px #0ff,
    inset 50px 0 300px #f0f, inset -50px 0 300px #0ff;
  animation: pulsate 6s linear infinite;
}

@keyframes pulsate {
  50% {
    box-shadow: 0 0 20px #fff, 20px 0 80px #f0f, -20px 0 80px #0ff,
      inset 0 0 50px #fff, inset -50px 0 80px #f0f, inset 50px 0 80px #0ff,
      inset -50px 0 300px #f0f, inset 50px 0 300px #0ff;
  }
}

.chatmind {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 2.2rem;
  font-weight: 900;
  /* line-height: 116px; */
  letter-spacing: 5px;
}
.train,
.token {
  color: rgb(1, 1, 1);
  font-family: 思源黑体;
  font-size: 1.2rem;
  font-weight: 500;
  /* line-height: 52px; */
  letter-spacing: 0px;
}
.cardTime,
.tokenNum {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 1.6rem;
  font-weight: 500;
  /* line-height: 70px; */
  letter-spacing: 0px;
}

.holoLayer {
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0) 0%,
    rgba(202, 226, 255, 0) 65%,
    rgba(147, 196, 255, 1) 100%
  );
  box-shadow: 0px 0px 39px 0px rgba(148, 255, 241, 0.3);
  border-radius: 360px;
  transition: all 0.5s ease-in-out 0s;
}
#coreCircleTwo {
  /* position:absolute; */
  top: 0px;
  width: 100%;
  height: 100%;
  /* margin:-5%; */
  border: 1px dashed var(--main-white-color);
  border-radius: 180%;
}
#coreCircleOne {
  /* position:absolute; */
  /* top:-100px; */
  width: 100%;
  height: 100%;
  /* margin:-5%; */
  border: 1px dashed var(--main-white-color);
  border-radius: 180%;
}
#coreCircleOne:before {
  content: "";
  position: absolute;
  border-radius: 180%;
  width: 20%;
  display: block;
  height: 20%;
  margin: 40%;
  top: -5px;
  left: -5px;
  border: 1px solid var(--main-white-color);
  box-shadow: 0px 0px 1px 1px white, inset 0 0px 1px white;
  transition: all 0.5s ease-in-out 0.4s;
  animation: rotationY1 4s infinite linear;
}
#coreCircleTwo:before {
  content: "";
  position: absolute;
  border-radius: 180%;
  width: 20%;
  display: block;
  height: 20%;
  margin: 40%;
  top: -5px;
  left: -5px;
  border: 1px solid var(--main-white-color);
  box-shadow: 0px 0px 1px 1px white, inset 0 0px 1px white;
  transition: all 0.5s ease-in-out 0.4s;
  animation: rotationY2 4s infinite linear;
}

#coreCircleOne:after {
  content: "";
  position: absolute;
  display: block;
  width: 20%;
  height: 20%;
  border-radius: 180%;
  margin: 40%;
  top: -5px;
  left: -5px;
  box-shadow: 0px 0px 1px 1px var(--main-white-color),
    inset 0 0px 1px var(--main-white-color);
  border: 1px solid var(--main-white-color);
  transition: all 0.5s ease-in-out 0.4s;
  animation: rotationX1 8s infinite linear;
}
#coreCircleTwo:after {
  content: "";
  position: absolute;
  display: block;
  width: 20%;
  height: 20%;
  border-radius: 180%;
  margin: 40%;
  top: -5px;
  left: -5px;
  box-shadow: 0px 0px 1px 1px var(--main-white-color),
    inset 0 0px 1px var(--main-white-color);
  border: 1px solid var(--main-white-color);
  transition: all 0.5s ease-in-out 0.4s;
  animation: rotationX2 8s infinite linear;
}
@keyframes rotationX1 {
  0% {
    transform: rotateX(0deg) rotateY(0deg) scale(1);
  }
  100% {
    transform: rotateX(360deg) rotateY(-360deg) scale(1);
  }
}
@keyframes rotationY1 {
  0% {
    transform: rotateY(0deg) rotateX(0deg) scale(1);
  }
  100% {
    transform: rotateY(360deg) rotateX(360deg) scale(1);
  }
}
@keyframes rotationX2 {
  0% {
    transform: rotateX(180deg) rotateY(180deg) scale(1);
  }
  100% {
    transform: rotateX(360deg) rotateY(-360deg) scale(1);
  }
}
@keyframes rotationY2 {
  0% {
    transform: rotateY(180deg) rotateX(180deg) scale(1);
  }
  100% {
    transform: rotateY(360deg) rotateX(360deg) scale(1);
  }
}
.flyWrapper {
  margin: 0 auto;
  /* position: absolute;
  top: 0px;
  left: 26px; */
}
/* #holoLoader.active {
  transform: perspective(1000px) rotateX(30deg) rotateY(-50deg) rotateZ(0deg);
  transition:all 0.5s ease-in-out;
}
#core {
  width:100%;
  height:100%;
  background:radial-gradient(circle, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 17%, rgba(147,196,255,0.1) 24%, rgba(255,255,255,0) 30%)
} */
.Body {
  background-image: url("../static/screen3/background-100-03.jpg");
  background-size: 100% 120%;
  min-width: 1700px;
  width: 100%;
  position: relative;
  /* padding-top: 20px; */
  min-height: 100vh;
  overflow: hidden;
}
.background {
  /* background-image: url("../static/screen3/background-100-03.jpg");
  background-size: 100% 120%;
  min-width: 1600px;
  width: 100%;
  position: relative;
  height: 110vh;
  padding-top: 20px; */
  height: 100%;
  /* max-height: 1100px; */
  /* overflow: hidden; */
}
.dropdown {
  width: 225px;
  position: fixed;
  right: 30px;
  top: 5px;
  z-index: 200;
}
</style>
