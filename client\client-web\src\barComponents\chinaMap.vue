<!-- eslint-disable vue/require-v-for-key -->
<template>
  <div class="mapWrapper">
    <div class="chinaMap" @click="cancelName">
      <div class="districtWrapper" v-if="computing">
        <span class="north district" v-if="!showTitle">华北地区</span>
        <span class="east district" v-if="!showTitle">华东地区</span>
        <span class="westsouth district" v-if="!showTitle">西南地区</span>
        <span class="south district" v-if="!showTitle">华南地区</span>
        <span class="middle district" v-if="!showTitle">华中地区</span>
      </div>
      <!-- 坐标点 -->
      <div class="pointWrapper">
        <div
          class="point"
          v-for="item in mapData"
          :style="{
            top: item.coordinateY + 'px',
            left: item.coordinateX + 'px',
          }"
          :key="item.name"
        >
          <!-- 已接入 -->
          <div
            v-if="item.connectionState == 3"
            @click.stop="showName(item, $event)"
          >
            <img
              src="../barAssets/superEd.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '超算中心'"
              ref="refimage"
            />
            <img
              src="../barAssets/EWed.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '东数西算'"
              ref="refimage"
            />
            <img
              src="../barAssets/ed.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '智算中心'"
              ref="refimage"
            />
            <transition name="fade">
              <div class="colorType1 bg" v-show="showTitle">
                {{ item.name }}
              </div>
            </transition>
          </div>
          <!-- 接入中 -->
          <div
            v-if="item.connectionState == 1"
            @click.stop="showName(item, $event)"
          >
            <img
              src="../barAssets/superIng.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '超算中心'"
              ref="refimage"
            />
            <img
              src="../barAssets/EWing.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '东数西算'"
              ref="refimage"
            />
            <img
              src="../barAssets/ing.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '智算中心'"
              ref="refimage"
            />
            <transition name="fade">
              <div class="colorType2 bg" v-show="showTitle">
                {{ item.name }}
              </div>
            </transition>
          </div>
          <!-- 待接入 -->
          <div
            v-if="item.connectionState == 2"
            @click.stop="showName(item, $event)"
          >
            <img
              src="../barAssets/superUn.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '超算中心'"
              ref="refimage"
            />
            <img
              src="../barAssets/EWun.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '东数西算'"
              ref="refimage"
            />
            <img
              src="../barAssets/un.svg"
              alt=""
              style="width: 25px; height: 25px"
              v-if="item.centerType == '智算中心'"
              ref="refimage"
            />
            <transition name="fade">
              <div class="colorType3 bg" v-show="showTitle">
                {{ item.name }}
              </div>
            </transition>
          </div>
        </div>
        <div
          class="point2 bg"
          :style="{
            top: y + 35 + 'px',
            left: x - 10 + 'px',
          }"
          v-if="tipName"
        >
          {{ name }}
        </div>
      </div>
      <div class="compute">
        <div class="StyleWrapper">
          <span class="pcStyle">鹏城云脑</span>
          <span class="gzStyle">广州超算</span>
          <span class="jnStyle">济南超算</span>
          <span class="zkdStyle">中科类脑</span>
          <span class="SDStyle">SD-WAN互联</span>
          <span class="MPLSStyle">MPLS互联</span>
          <div class="dot pcDot"></div>
          <div class="dot gzDot"></div>
          <div class="dot jnDot"></div>
          <div class="dot zkdDot"></div>
          <div class="commonPoint cdPoint">
            <div class="commonPoint2"></div>
          </div>
          <div class="commonPoint nxPoint">
            <div class="commonPoint2"></div>
          </div>
          <div class="commonPoint bjPoint">
            <div class="commonPoint2"></div>
          </div>
          <div class="commonPoint fzPoint">
            <div class="commonPoint2"></div>
          </div>
        </div>
        <dv-flyline-chart-enhanced
          :config="config2"
          style="width: 100%; height: 950px"
        />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'chinaMap',
  props: {
    mapData: {
      type: Array,
      default: []
    },
    show: {
      type: Number
    },
    desMap: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    mapData: {
      handler (newValue, oldValue) {
        this.mapData = newValue
        if (this.mapData.length == 0) {
          this.computing = false
        } else {
          this.computing = true
        }
      }
    },
    show: {
      handler (newValue, oldValue) {
        this.cancelName()
        this.tipName = false
        if (newValue == 4 || newValue == 1) {
          this.showTitle = false
          this.name = ''
        } else {
          this.showTitle = true
        }
        this.desMap.lines.forEach((item) => {
          this.mapData.forEach((Item) => {
            if (Item.name === item.target) {
              item.centerType = Item.centerType
            }
          })
        })
        if (newValue == 1) {
          const config = JSON.parse(JSON.stringify(this.desMap))
          config.lines = this.desMap.lines.filter((item) => {
            if (item.centerType == '智算中心') {
              return {
                duration: item.duration,
                source: item.source,
                target: item.target,
                width: item.width
              }
            }
          })
          this.config = config
        } else if (newValue == 2) {
          const config = JSON.parse(JSON.stringify(this.desMap))
          config.lines = this.desMap.lines.filter((item) => {
            if (item.centerType == '超算中心') {
              return {
                duration: item.duration,
                source: item.source,
                target: item.target,
                width: item.width
              }
            }
          })

          this.config = config
        } else if (newValue == 3) {
          const config = JSON.parse(JSON.stringify(this.desMap))
          config.lines = this.desMap.lines.filter((item) => {
            if (item.centerType == '东数西算') {
              return {
                duration: item.duration,
                source: item.source,
                target: item.target,
                width: item.width
              }
            }
          })
          this.config = config
        }
      }
    }
  },
  data () {
    return {
      name: '',
      showTitle: true,
      tipName: false,
      x: undefined,
      y: undefined,
      computing: true,
      config: undefined,
      desConfig: undefined,
      config2: {
        points: [
          {
            name: '广州超算',
            coordinate: [0.565, 0.6],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '鹏城云脑',
            coordinate: [0.59, 0.61],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '济南超算',
            coordinate: [0.64, 0.38],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '中科类脑',
            coordinate: [0.625, 0.45],
            halo: {
              show: true,
              radius: 80
            }
          },
          // 普通网络
          {
            name: '成都',
            coordinate: [0.435, 0.48]
          },
          {
            name: '宁夏',
            coordinate: [0.476, 0.35]
          },
          {
            name: '北京',
            coordinate: [0.615, 0.3]
          },
          {
            name: '福州',
            coordinate: [0.68, 0.48]
          }
        ],
        lines: [
          {
            source: '鹏城云脑',
            target: '广州超算',
            width: 8,
            color: 'yellow',
            duration: [20.3],
            orbitColor: 'rgba(255, 255, 0, .6)'
          },
          {
            source: '鹏城云脑',
            target: '济南超算',
            width: 8,
            color: '#00C7FF',
            duration: [20.3],
            orbitColor: 'rgba(0, 199, 255, .6)'
          },
          {
            source: '鹏城云脑',
            target: '中科类脑',
            width: 8,
            color: '#FF7272',
            duration: [20.3],
            orbitColor: 'rgba(255, 114, 114, .6)'
          },

          {
            source: '鹏城云脑',
            target: '成都',
            width: 3,
            color: 'rgb(0,255,0)',
            duration: [20.3],
            orbitColor: 'rgba(0,255,0, .2)'
          },
          {
            source: '鹏城云脑',
            target: '宁夏',
            width: 3,
            color: 'rgb(0,255,0)',
            duration: [20.3],
            orbitColor: 'rgba(0,255,0, .2)'
          },
          {
            source: '鹏城云脑',
            target: '北京',
            width: 3,
            color: 'rgb(0,255,0)',
            duration: [20.3],
            orbitColor: 'rgba(0,255,0, .2)'
          },
          {
            source: '鹏城云脑',
            target: '福州',
            width: 3,
            color: 'rgb(0,255,0)',
            duration: [20.3],
            orbitColor: 'rgba(0,255,0, .2)'
          }
        ],
        k: 0.3,
        curvature: 4
      }
    }
  },
  created () {
    //  this.desMap.lines.forEach(item=>{
    //   this.mapData.forEach(
    //     Item=>{
    //       if(Item.name===item.target){
    //         item.centerType=Item.centerType
    //       }
    //     }
    //   )
    // })
    this.config = this.desMap
  },
  mounted () {
    if (this.show == '4' || this.show == '1') {
      this.showTitle = false
    } else {
      this.showTitle = true
    }
  },
  methods: {
    showName (item, e) {
      item.showTip = true
      this.y = item.coordinateY
      this.x = item.coordinateX
      this.name = item.name
      const data = this.$refs.refimage
      data.forEach((item) => {
        item.style.scale = '1'
        item.style.scale = '1'
      })
      const child = e.currentTarget.firstElementChild
      child.style.scale = '2'
      child.style.scale = '2'
      if (this.show == 1 || this.show == 4) {
        (this.tipName = true), (this.showTitle = false)
      } else {
        (this.tipName = false), (this.showTitle = true)
      }
      this.$emit('showTip', item)
    },
    cancelName () {
      const data = this.$refs.refimage
      data.forEach((item) => {
        item.style.scale = '1'
        item.style.scale = '1'
      })
      this.name = ''
      this.$emit('hiddenTip', false)
    }
  }
}
</script>
<style scoped>
.point {
  position: absolute;
  z-index: 1;
  height: 25px;
}

.tipTitle {
  font-size: 18px;
  color: yellow;
  font-weight: 800;
}

.tipTitle:hover {
  cursor: pointer;
}

.colorType1 {
  font-size: 10px;
  color: rgba(243, 202, 69, 0.95);
}

.colorType2 {
  font-size: 10px;
  color: rgba(30, 231, 231, 0.95);
}

.colorType3 {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.95);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 2s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.district {
  color: #fff;
  z-index: 1;
  position: absolute;
  font-weight: 600;
  font-size: 16px;
  opacity: 0.6;
}

.north {
  top: 315px;
  left: 650px;
}

.east {
  top: 470px;
  left: 710px;
}

.westsouth {
  top: 480px;
  left: 470px;
}

.south {
  top: 535px;
  left: 590px;
}

.middle {
  top: 408px;
  left: 625px;
}
@keyframes change {
  from {
    /* background-color: yellow; */
    transform: translate(-50%, -50%) scale(1);
  }

  to {
    /* background-color: red; */
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.Point {
  position: absolute;
  width: 500px;
  height: 250px;
  background-image: url("../barAssets/ninePoint.svg");
  background-repeat: no-repeat;
  left: 315px;
  bottom: 65px;
  background-size: 110% 100%;
}

.chinaMap {
  height: 950px;
  background-image: url("../barStatic/screen1/china-3d-V2.png");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 100%;
  /* min-width: 1100px; */
  background-position-y: 20px;
  z-index: 100px;
  /* margin-left: 50px; */
}

.mapWrapper {
  /* max-height: 800px;
    overflow: hidden; */
  margin: 0 auto;
  width: 1100px;
}

.districtWrapper,
.pointWrapper {
  position: relative;
  top: 40px;
  left: 40px;
}

.pcStyle {
  top: 600px;
  left: 650px;
  position: absolute;
  color: yellow;
  font-weight: 800;
  font-size: 18px;
}

.gzStyle {
  top: 540px;
  left: 590px;
  position: absolute;
  color: white;
  font-weight: 800;
  font-size: 18px;
}

.jnStyle {
  top: 328px;
  left: 710px;
  position: absolute;
  color: white;
  font-weight: 800;
  font-size: 18px;
}

.zkdStyle {
  top: 420px;
  left: 610px;
  position: absolute;
  color: white;
  font-weight: 800;
  font-size: 18px;
}
.SDStyle {
  top: 430px;
  left: 750px;
  position: absolute;
  color: #00c7ff;
  font-weight: 800;
  font-size: 14px;
}

.MPLSStyle {
  top: 472px;
  left: 640px;
  position: absolute;
  color: #ff7272;
  font-weight: 800;
  font-size: 14px;
}

.StyleWrapper {
  position: relative;
}

.dot {
  position: absolute;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  /* background: rgb(0, 255, 31); */
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.pcDot {
  top: 573px;
  left: 671px;
  position: absolute;
  background-color: yellow;
}

.gzDot {
  top: 564px;
  left: 643px;
  position: absolute;
  background-color: white;
}

.jnDot {
  top: 353px;
  left: 728px;
  position: absolute;
  background-color: #00c7ff;
}

.zkdDot {
  top: 420px;
  left: 712px;
  position: absolute;
  background-color: #ff7272;
}

.commonPoint {
  width: 15px;
  height: 15px;
  background-image: url("../barAssets/dot.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.point2 {
  position: absolute;
  z-index: 1;
  font-size: 10px;
  color: rgb(255, 211, 0);
  font-weight: 800;
}

.cdPoint {
  position: absolute;
  top: 445px;
  left: 490px;
}

.nxPoint {
  position: absolute;
  top: 328px;
  left: 540px;
}

.bjPoint {
  position: absolute;
  top: 275px;
  left: 700px;
}

.fzPoint {
  position: absolute;
  top: 453px;
  left: 776px;
}

.compute {
  position: relative;
  top: 145px;
  left: 50px;
}
.bg {
  /* 矩形 9 */

  /* width: 182px;
  height: 70px; */
  background: linear-gradient(
    180deg,
    rgba(5, 16, 67, 0.78),
    rgba(26, 22, 26, 0) 100%
  );
  padding: 5px;
}
</style>
