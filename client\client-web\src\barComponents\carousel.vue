<template>
    <div>
        <el-row type="flex" justify="center">
            <el-col :span="18">
                <div class="messageWrapper">
                    <el-row>
                        <el-col :span="2" style="width:40px;margin-top: 2px;">
                            <div class="arrow" style="width:12px"></div>
                            <div class="arrow" style="width:12px"></div>
                            <div class="arrow" style="width:12px"></div>
                        </el-col>
                        <el-col :span="4">
                            <div>
                                {{name}}
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="unversity" v-if="unversity">
                                <div>高校</div>
                            </div>
                            <div class="business" v-if="business">
                                <div>企业</div>
                            </div>
                            <div class="center" v-if="center">
                                <div>超算中心</div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <div>
                                <el-row>
                                    <el-col :span="6">
                                        <div class="">所属省市/单位</div>
                                    </el-col>
                                    <el-col :span="6">
                                        <div class="">{{name}}</div>
                                    </el-col>
                                    <el-col :span="6">
                                        <div class="">当前分中心繁忙程度</div>
                                    </el-col>
                                    <el-col :span="6">
                                        <div class="stateNormal" v-if="normal"><img src='../assets/point.png' alt=""
                                                class="point1"> </div>
                                        <div class="stateBusy" v-if="busy"><img src='../assets/point.png' alt=""
                                                class="point2">
                                        </div>
                                    </el-col>

                                </el-row>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </el-col>
        </el-row>

        <el-carousel :interval="4000" type="card" indicator-position="none" height="450px" arrow="never" autoplay
            @change="change">
            <el-carousel-item v-for="(item,index) in data" :key="index">
                <h3>{{ item.title }}</h3>
                <el-row>
                    <el-col :span="10">
                        <img :src="item.img" alt="" class="img">
                    </el-col>
                    <el-col :span="14">
                        <el-row type="flex" justify="center">
                            <el-col :span="20">
                                <div class="text">{{item.text}}</div>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
                </el-col>
                </el-row>
            </el-carousel-item>
        </el-carousel>
    </div>
</template>
<script scoped>
export default {
  name: 'carousel',
  props: {
    carouselData: {
      type: Array,
      default: () => []
    },
    Type: { type: Number, default: 0 }
  },
  watch: {
    carouselData: {
      handler (newValue, oldValue) {
        this.data = newValue
        this.name = this.data[this.index].title
        this.busyState = this.data[this.index].busyState
        this.type = this.data[this.index].type
      },
      deep: true
    },
    index: {
      handler (newValue, oldValue) {
        this.name = this.data[this.index].title
        this.busyState = this.data[this.index].busyState
      }
    },
    Type: {
      handler (newValue, oldValue) {
        this.Type = newValue
        switch (this.Type) {
          case 1:
            this.center = true
            this.unversity = false
            this.business = false
            break
          case 2:
            this.center = false
            this.unversity = true
            this.business = false
            break
          case 3:
            this.center = false
            this.unversity = false
            this.business = true
          default:
            this.center = false
            this.unversity = false
            this.business = false
        }
      }
    },
    busyState: {
      handler (newValue, oldValue) {
        this.busyState = newValue
        switch (this.busyState) {
          case 2:
            this.normal = true
            this.busy = false
            break
          case 1:
            this.busy = true
            this.normal = false
            break
          default:
            this.busy = false
            this.normal = false
        }
      }
    }
  },
  data () {
    return {
      data: [],
      index: 0,
      name: '',
      busyState: 0,
      normal: false,
      busy: false,
      unversity: false,
      business: false,
      center: false
    }
  },
  created () {
    this.data = this.carouselData
    switch (this.Type) {
      case 1:
        this.center = true
        this.unversity = false
        this.business = false
        break
      case 2:
        this.center = false
        this.unversity = true
        this.business = false
        break
      case 3:
        this.center = false
        this.unversity = false
        this.business = true
      default:
        this.center = false
        this.unversity = false
        this.business = false
    }
  },
  mounted () {
    if (this.data.length !== 0) {
      this.name = this.data[this.index].title, this.busyState = this.data[this.index].busyState
    } else { this.name = '', this.busyState = 0 }
  },
  methods: {
    change (index) {
      this.index = index
      this.$emit('indexChange', this.index)
      this.name = this.data[this.index].title
      this.busyState = this.data[this.index].busyState
    }
  }

}
</script>
<style>
    .messageWrapper {
        border: 1px solid rgba(18, 137, 221, 0.8);
        font-family: 庞门正道标题体3;
        font-size: 20px;
        color: rgb(255, 255, 255);
        letter-spacing: 2px;
        line-height: 48px;
        text-align: left;
        width: 100%;
        font-weight: normal;
        filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
        padding: 10px;
        border-radius: 10px;
        margin: 50px 0 50px 0;
    }

    .el-carousel__item h3 {
        color: #475669;
        font-size: 28px;
        opacity: 0.75;
        line-height: 40px;
        margin: 40px 0 10px 50px;
        text-align: left;
        color: rgb(30, 231, 231);
        font-weight: 400;

    }

    .el-carousel__item {
        background-image: url('../static/screen2/banner.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 320px;
        position: absolute;
        top: 130px
    }

    .el-carousel__item--card.is-active {
        height: 450px;
        position: absolute;
        top: 0px;

    }

    .el-carousel__item--card.is-active .img {
        width: 200px;
        min-height: 200px;
        display: inline-block;
        margin: 50px 15px 0 40px;

    }

    .el-carousel__item--card.is-active .text {
        margin-top: 45px;
        color: #fff;
        font-size: 14px;
        height: 280px;
    }

    .img {
        width: 200px;
        height: 120px;
        display: inline-block;
        min-height: 50px;
        margin: 50px 15px 0 40px;
    }

    .text {
        margin-top: 50px;
        color: #fff;
        font-size: 14px;
        height: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .el-carousel__mask {
        background-color: transparent;
    }

    .point1 {
        width: 40px;
        height: 40px;
        position: relative;
        top: -15px;
    }

    .point2 {
        width: 40px;
        height: 40px;
        position: relative;
        top: -15px;
        left: 100px;
    }

    .stateNormal {
        margin-top: 22px;
        background-image: url('../assets/normal.png');
        background-size: 100% 100%;
        width: 150px;
        height: 5px;
        background-repeat: no-repeat;
    }

    .stateBusy {
        margin-top: 22px;
        background-image: url('../assets/busy.png');
        background-size: 100% 100%;
        width: 150px;
        height: 5px;
        background-repeat: no-repeat;

    }

    .unversity {
        margin-top: 14px;
        background-size: 15% 90%;
        height: 24px;
        background-repeat: no-repeat;
        background-image: url('../assets/school.png');
        background-position: 10px 1px;
        padding-left: 55px;
        border: 1px solid rgb(30, 231, 231);
        border-radius: 40px;

    }

    .business {
        margin-top: 14px;
        background-size: 15% 90%;
        height: 24px;
        background-repeat: no-repeat;
        background-image: url('../assets/business.png');
        background-position: 10px 1px;
        padding-left: 55px;
        border: 1px solid rgb(30, 231, 231);
        border-radius: 40px;

    }

    .center {
        margin-top: 14px;
        background-size: 15% 90%;
        height: 24px;
        background-repeat: no-repeat;
        background-image: url('../assets/center.png');
        background-position: 10px 1px;
        padding-left: 55px;
        border: 1px solid rgb(30, 231, 231);
        border-radius: 40px;

    }

    .unversity div {
        position: relative;
        top: -12px;
        font-size: 18px;
    }

    .center div {
        position: relative;
        top: -12px;
        font-size: 18px;
    }

    .business div {
        position: relative;
        top: -12px;
        font-size: 18px;
    }

    .arrow {
        margin-top: 8px;
        height: 30px;
        background-image: url('../assets/arrow3.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: inline-block;
    }
</style>
