<template>
  <div class="cardTurner">
    <el-row>
      <el-col :span="6">
        <div class="numTitle">
          <div>迁移成功</div>
          <div></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div id="id" style="width: 100%; height: 125px" ref="echart"></div>
      </el-col>
      <el-col :span="6">
        <div class="numTitle">
          <div>迁移数据量</div>
          <div></div>
        </div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center">
      <el-col :span="24">
        <div>
          <el-carousel indicator-position="outside">
            <el-carousel-item v-for="item in 4" :key="item">
              <h3>{{ item }}</h3>
            </el-carousel-item>
          </el-carousel>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
// import { storageMon } from "@/api/screenService.js";
export default {
  name: 'storageMonitor',
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    config: {
      handler (newValue, oldValue) {},
      deep: true
    }
  },
  data () {
    return {
      total: 100,
      option: {
        // 提示框组件。开发实际中去掉了指针，提示框可以不用设置。
        // tooltip: {
        //   formatter: "{a} <br/>{b} : {c}%"
        // },
        // 下面属性才是仪表盘的核心！！反正我是这么认为的！！！
        series: [
          {
            // 类型
            type: 'gauge',
            // 半径
            radius: '140%',
            // 起始角度。圆心 正右手侧为0度，正上方为90度，正左手侧为180度。
            startAngle: 180,
            // 结束角度。
            endAngle: 0,
            center: ['50%', '80%'],
            progress: {
              show: true,
              width: 12,
              itemStyle: { color: 'rgb(80,205,137)' }
            },
            // 仪表盘轴线相关配置。
            axisLine: {
              show: true,
              lineStyle: {
                width: 12,
                color: [[1, 'rgba(1, 145, 255, 0.2)']]
              }
            },
            // 分隔线样式。
            splitLine: {
              show: false
            },
            // 刻度样式。
            axisTick: {
              show: false
            },
            // 刻度标签。
            axisLabel: {
              show: false
            },
            // 仪表盘指针。
            pointer: {
              // 这个show属性好像有问题，因为在这次开发中，需要去掉指正，我设置false的时候，还是显示指针，估计是BUG吧，我用的echarts-3.2.3；希望改进。最终，我把width属性设置为0，成功搞定！
              show: false,
              // 指针长度
              length: '90%',
              width: 0
            },
            // 仪表盘标题。
            title: {
              show: true,
              offsetCenter: [0, '-40%'], // x, y，单位px
              color: '#000',
              fontSize: 30
            },
            // 仪表盘详情，用于显示数据。
            detail: {
              show: true,
              offsetCenter: [0, '-30%'],
              formatter: (params) => {
                return `{a|总迁移任务}
                 {b|${this.total}}{a| 个}`
              },
              fontSize: 1,
              rich: {
                a: {
                  fontSize: 14,
                  color: 'rgb(222, 222, 222)'
                },
                b: {
                  fontSize: 14,
                  fontWeight: 'bold',
                  color: 'rgb(1, 145, 255)'
                }
              }
            },
            data: [
              {
                value: 50,
                name: '存储量'
              }
            ]
          }
        ]
      }
    }
  },
  created () {
    storageMon().then((res) => {
      this.drawLine()
    })
  },
  methods: {
    // drawLine() {
    //   // 基于准备好的dom，初始化echarts实例
    //   this.myChart = this.$echarts.init(this.$refs.echart);
    //   let option = this.option;
    //   option && this.myChart.setOption(option);
    // },
  }
}
</script>
<style scoped>
.wrapper {
  padding: 10px;
  position: relative;
  color: red;
}

.dv-digital-flop {
  margin: 0 auto;
}

.title {
  color: rgb(24, 144, 255);
  font-size: 18px;
  font-weight: 400;
  margin: 5px 0 5px 50px;
}

.rateTitle {
  color: rgb(24, 144, 255);
  font-size: 14px;
  font-weight: 400;
  margin: 5px 0 0px 50px;
}

.runtimeTitle {
  color: rgb(24, 144, 255);
  font-size: 14px;
  font-weight: 400;
  margin: 5px 0 0px 50px;
}

.borderBox {
  display: inline-block;
  width: 34px;
  height: 60px;
  background-color: rgba(24, 144, 255, 0.4);
  margin: 0px 5px 0px 0px;
}

.Border {
  display: inline-block;
  position: absolute;
  top: 45px;
}

.clearBorder {
  background-color: rgba(0, 0, 0, 0);
  margin: 0px;
}

.decimal {
  position: relative;
  left: -7px;
}

.borderWrapper {
  margin: 0 auto;
  width: 300px;
  height: 50px;
}

.rate {
  position: relative;
  top: -50px;
}

.runtime {
  position: relative;
  top: -50px;
}

.cardTurner {
  height: 235px;
  overflow: hidden;
}
.numTitle {
  color: rgb(222, 222, 222);
  font-size: 14px;
  text-align: center;
  padding-top: 40px;
  font-weight: 600;

}
 .el-carousel__item h3 {
    color: #475669;
    font-size: 18px;
    opacity: 0.75;
    line-height: 300px;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }

  .el-carousel__item:nth-child(2n+1) {
    background-color: #d3dce6;
  }
  /deep/.el-carousel__container{height:100px;}
</style>
