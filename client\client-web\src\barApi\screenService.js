import request from '@/barUtils/request'
export function getAicenter (params) {
  return request({
    url: '/openapi/v1/sharescreen/aicenter',
    method: 'get',
    params
  })
}
export function getTrainJob (params) {
  return request({
    url: '/openapi/v1/sharescreen/trainjob',
    method: 'get',
    params
  })
}
export function getCrossTask (params) {
  return request({
    url: '/openapi/v1/sharescreen/statistic',
    method: 'get',
    params
  })
}
// 每个中心累积任务情况
export function getAccrueCenter (params) {
  return request({
    url: '/openapi/v1/sharescreen/computepower/percenter',
    method: 'get',
    params
  })
}
// 算力累积趋势
export function getTend (params) {
  return request({
    url: '/openapi/v1/sharescreen/computepower/dailytrend',
    method: 'get',
    params
  })
}
// 算力接入情况
export function getConnected (params) {
  return request({
    url: '/openapi/v1/sharescreen/computepower/connected',
    method: 'get',
    params
  })
}
// 算力接入情况
export function getDynamic () {
  return request({
    url: '/openapi/v1/sharescreen/computepower/alljobinfo',
    method: 'get'
  })
}
// 任务分布情况
export function taskDist () {
  return request({
    url: '/openapi/v1/sharescreen/shaoguan_otjobstatistics',
    method: 'get'
  })
}
// 存储信息
export function storageMessage () {
  return request({
    url: '/openapi/v1/sharescreen/shaoguan_titleaicenterinfo?key=shaoguan_storage',
    method: 'get'
  })
}
// 大湾区信息
export function barMessage () {
  return request({
    url: '/openapi/v1/sharescreen/shaoguan_titleaicenterinfo?key=title',
    method: 'get'
  })
}
// 等待任务与运行任务
export function nodeTask () {
  return request({
    url: '/openapi/v1/sharescreen/shaoguan_aicenter',
    method: 'get'
  })
}
