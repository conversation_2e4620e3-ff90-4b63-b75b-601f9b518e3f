<template>
  <div>
    <nav class="navbar navbar-dark">
      <div class="container-fluid buttonPos">
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarToggleExternalContent"
          aria-controls="navbarToggleExternalContent"
          aria-expanded="true"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
      </div>
    </nav>
    <div
      class="collapse"
      id="navbarToggleExternalContent"
      @click.stop="showNavbar"
    >
      <div class="p-3 bg">
        <h5 :class="{ bigOption: true, check1: true }">总调度中心</h5>
        <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show1 }"></span>
          <span :class="{ check3: show1 }" @click="target('c2net')"
            >总指挥大屏</span
          >
        </h5>
        <h5 class="smallOption" v-if="show3D">
          <span :class="{ Dot: true, check2: show8 }"></span>
          <span :class="{ check3: show8 }" @click="target('c2net3D')"
            >总指挥大屏(3D)</span
          >
        </h5>
          <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show9 }"></span>
          <span
            :class="{ check3: show9 }"
            @click="target('intelligentJointScheduling')"
            >任务调度实例</span
          >
        </h5>
        <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show2 }"> </span>
          <span :class="{ check3: show2 }" @click="target('collaborative')"
            >协同计算范式</span
          >
        </h5>
        <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show7 }"> </span>
          <span :class="{ check3: show7 }" @click="target('c2netDemo')"
            >东数西算</span
          >
        </h5>
        <h5 :class="{ bigOption: true, check1: false }">成渝枢纽节点</h5>
        <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show3 }"></span>
          <span :class="{ check3: show3 }" @click="target('chengyu')"
            >成渝枢纽指挥中心</span
          >
        </h5>
        <h5 :class="{ bigOption: true, check1: false }">大湾区枢纽节点</h5>
        <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show4 }"></span>
          <span :class="{ check3: show4 }" @click="target('shaoguan1')"
            >大湾区枢纽指挥中心</span
          >
        </h5>
        <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show5 }"></span>
          <span :class="{ check3: show5 }" @click="target('shaoguan2')"
            >智能联合调度</span
          >
        </h5>
        <h5 class="smallOption">
          <span :class="{ Dot: true, check2: show6 }"></span>
          <span :class="{ check3: show6 }" @click="target('shaoguan3')"
            >算力撮合</span
          >
        </h5>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'navbar',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  watch: {},
  mounted () {
    if (this._isMobile()) {
      this.show3D = false
    } else {
      this.show3D = true
    }
    switch (this.type) {
      case 'c2net':
        this.show1 = true
        this.show2 = false
        this.show3 = false
        this.show4 = false
        this.show5 = false
        this.show6 = false
        this.show7 = false
        this.show8 = false
        this.show9 = false
        break
      case 'collaborative':
        this.show1 = false
        this.show2 = true
        this.show3 = false
        this.show4 = false
        this.show5 = false
        this.show6 = false
        this.show7 = false
        this.show8 = false
        this.show9 = false
        break
      case 'c2netDemo':
        this.show1 = false
        this.show7 = true
        this.show3 = false
        this.show4 = false
        this.show5 = false
        this.show6 = false
        this.show2 = false
        this.show8 = false
        this.show9 = false
        break
      case 'eastAndWest':
        this.show1 = false
        this.show2 = false
        this.show3 = false
        this.show4 = false
        this.show5 = false
        this.show6 = false
        this.show8 = false
        this.show9 = false
      case 'c2net3D':
        this.show1 = false
        this.show2 = false
        this.show3 = false
        this.show4 = false
        this.show5 = false
        this.show6 = false
        this.show7 = false
        this.show8 = true
        this.show8 = true
        this.show9 = false
      default:
        break
    }
  },
  data () {
    return {
      show1: false, // 总指挥大屏
      show8: false, // 总指挥大屏(3D)
      show2: false, // 协同计算范式
      show3: false, // 成渝枢纽指挥中心
      show4: false, // 大湾区枢纽指挥中心
      show5: false, // 智能联合调度
      show6: false, // 算力撮合
      show7: false, // 东数西算
      show9: false, // 智能联合调度
      show3D: true
    }
  },
  methods: {
    target (val) {
      if (val == 'shaoguan1') {
        window.location.href =
          'http://120.198.83.226:8989/clientweb/#/screen-1'
      } else if (val == 'shaoguan3') {
        window.location.href = 'https://dev.jointcloud.net/'
      } else if (val == 'chengyu') {
        window.location.href =
          'http://**************:50380/clientweb/#/screen-1'
      } else {
        this.$router.push({ name: val })
      }
    },
    showNavbar () {
      var target = document.getElementById('navbarToggleExternalContent')
      target.className = 'show'
    },
    _isMobile () {
      const flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      )
      return flag
    }
  }
}
</script>
<style scoped>
.bigOption {
  font-size: 16px;
  color: rgb(255, 255, 255);
  text-align: left;
  border: 1px solid rgba(1, 145, 255, 0.6);
  padding: 5px 0px 5px 0px;
  background: linear-gradient(rgba(1, 145, 255, 0.33), rgba(255, 1, 246, 0));
  /* cursor: pointer; */
  margin-bottom: 15px;
  font-weight: 600;
  padding-left: 35px;
}
.smallOption {
  font-size: 14px;
  color: rgb(255, 255, 255);
  text-align: left;
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 10px;
  padding-left: 20px;
}
.bg {
  background: rgba(8, 1, 17, 0.95) !important;
  border: 2px solid rgb(65, 53, 104);
  border-radius: 10px;
}
.buttonPos {
  display: flex;
  justify-content: flex-end;
}
.Dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(50, 197, 255, 0);
  margin-right: 8px;
}
.check1 {
  color: rgb(255, 211, 0);
  border: 3px solid rgb(50, 197, 255);
}
.check2 {
  background-color: rgb(50, 197, 255);
}
.check3 {
  color: rgb(50, 197, 255);
}
.navbar-toggler:focus {
  box-shadow: none;
}
.p-3 {
  padding: 1rem 0.5rem 1rem 0.5rem;
}
</style>
