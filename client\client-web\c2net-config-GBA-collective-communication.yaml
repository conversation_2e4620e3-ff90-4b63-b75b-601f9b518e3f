c2netComputingCenterList: # 计算中心列表
  - c2netComputingCenterSpecification: # 计算中心规格
      nodeSpecification: # 节点规格
        nGBMemoriesPerGraphicsCard: 40 # 每张显卡的内存大小(GB)
        nCpus: 64 # CPU核心数
        nGBMemories: 800 # 内存大小(GB)
        nGraphicsCards: 8 # 显卡数量
        graphicsCardType: A100 # 显卡类型
      nNodes: 100 # 节点数量
      pOPS: 128 # 计算能力(POPS)
      name: 鹏城云脑一 # 计算中心名称
    nCopiedC2netComputingCenters: 1 # 复制的计算中心数量
  - c2netComputingCenterSpecification: # 计算中心规格
      nodeSpecification: # 节点规格
        nGBMemoriesPerGraphicsCard: 32 # 每张显卡的内存大小(GB)
        nCpus: 512 # CPU核心数
        nGBMemories: 800 # 内存大小(GB)
        nGraphicsCards: 8 # 显卡数量
        graphicsCardType: ASCEND910 # 显卡类型
      nNodes: 100 # 节点数量
      pOPS: 1024 # 计算能力(POPS)
      name: 鹏城云脑二 # 计算中心名称
    nCopiedC2netComputingCenters: 1 # 复制的计算中心数量
  - c2netComputingCenterSpecification: # 计算中心规格
      nodeSpecification: # 节点规格
        nGBMemoriesPerGraphicsCard: 32 # 每张显卡的内存大小(GB)
        nCpus: 512 # CPU核心数
        nGBMemories: 800 # 内存大小(GB)
        nGraphicsCards: 8 # 显卡数量
        graphicsCardType: ASCEND910 # 显卡类型
      nNodes: 50 # 节点数量
      pOPS: 100 # 计算能力(POPS)
      name: 昇腾广州智算中心 # 计算中心名称
    nCopiedC2netComputingCenters: 1 # 复制的计算中心数量

c2netJobConfig: # 作业配置
  useJsonFile: false # 是否使用JSON文件
  jsonFileList: # JSON文件列表
    - task_info.json # 任务信息文件
  c2netJobGroupList: # 作业组列表
    - c2netJobGroupSpecification: # 作业组规格
        submissionTime: 1.0 # 提交时间
        subGraphList: # 子图列表
          - subGraphIndex: 0 # 子图索引
            collaborativeSchedule: true # 是否协同调度
            executeEpochs: 3 # 执行轮数
            subGraphTopology: # 子图拓扑
              - directedEdge: ->J0_0 #入口
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J0_0->J0_1 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J0_1->J0_2 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: ->J1_0 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J1_0->J1_1 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J1_1->J1_2 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: ->J2_0 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J2_0->J2_1 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J2_1->J2_2 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J0_1->C0 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J1_1->C0 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J2_1->C0 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: C0->C1 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J0_2->C1 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J1_2->C1 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: J2_2->C1 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: 0->J0_0 #标识循环
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: 1->J0_0 #标识循环
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: 1->J1_0 # 有向边
                nMDataset: 0 # 数据集大小(MB)
              - directedEdge: 2->J2_0 # 有向边
                nMDataset: 0 # 数据集大小(MB)
        collectiveCommunicationList: # 集体通信列表
          - collectiveCommunicationSpecification: # 集体通信规格
              name: allreduce # 通信类型名称
              algorithm: ring # 通信算法
            copiedCollectiveCommunicationSpecificationList: # 复制的集体通信规格列表
              - idInGroup: 0 # 组内ID
                nMDataset: 30 # 数据集大小(MB)
              - idInGroup: 1 # 组内ID
                nMDataset: 30 # 数据集大小(MB)
        c2netJobList: # 作业列表
          - c2netJobSpecification: # 作业规格
              nodeSpecification: # 节点规格
                nGBMemoriesPerGraphicsCard: 32 # 每张显卡的内存大小(GB)
                nCpus: 8 # CPU核心数
                nGBMemories: 100 # 内存大小(GB)
                nGraphicsCards: 4 # 显卡数量
                graphicsCardType: ASCEND910 # 显卡类型
              nNodes: 4 # 节点数量
              datasetSpecification: # 数据集规格
                nMDataset: 10 # 数据集大小(MB)
                srcCenterId: -1 # 源中心ID
              mandatoryComputingCenterName: "昇腾广州智算中心" # 指定计算中心名称
            copiedC2netJobSpecificationList: # 复制的作业规格列表
              - jobIdInGroup: 0 # 组内作业ID
                computingOperatorList: # 计算操作符列表
                  - idInJob: 0 # 作业内ID
                    runDurationTime: 200 # 运行时间
                  - idInJob: 1 # 作业内ID
                    runDurationTime: 200 # 运行时间
                  - idInJob: 2 # 作业内ID
                    runDurationTime: 200 # 运行时间
          - c2netJobSpecification: # 作业规格
              nodeSpecification: # 节点规格
                nGBMemoriesPerGraphicsCard: 32 # 每张显卡的内存大小(GB)
                nCpus: 8 # CPU核心数
                nGBMemories: 100 # 内存大小(GB)
                nGraphicsCards: 4 # 显卡数量
                graphicsCardType: ASCEND910 # 显卡类型
              nNodes: 4 # 节点数量
              datasetSpecification: # 数据集规格
                nMDataset: 10 # 数据集大小(MB)
                srcCenterId: -1 # 源中心ID
              mandatoryComputingCenterName: "鹏城云脑二" # 指定计算中心名称
            copiedC2netJobSpecificationList: # 复制的作业规格列表
              - jobIdInGroup: 1 # 组内作业ID
                computingOperatorList: # 计算操作符列表
                  - idInJob: 0 # 作业内ID
                    runDurationTime: 100 # 运行时间
                  - idInJob: 1 # 作业内ID
                    runDurationTime: 100 # 运行时间
                  - idInJob: 2 # 作业内ID
                    runDurationTime: 100 # 运行时间
          - c2netJobSpecification: # 作业规格
              nodeSpecification: # 节点规格
                nGBMemoriesPerGraphicsCard: 32 # 每张显卡的内存大小(GB)
                nCpus: 8 # CPU核心数
                nGBMemories: 100 # 内存大小(GB)
                nGraphicsCards: 4 # 显卡数量
                graphicsCardType: A100 # 显卡类型
              nNodes: 4 # 节点数量
              datasetSpecification: # 数据集规格
                nMDataset: 10 # 数据集大小(MB)
                srcCenterId: -1 # 源中心ID
              mandatoryComputingCenterName: "鹏城云脑一" # 指定计算中心名称
            copiedC2netJobSpecificationList: # 复制的作业规格列表
              - jobIdInGroup: 2 # 组内作业ID
                computingOperatorList: # 计算操作符列表
                  - idInJob: 0 # 作业内ID
                    runDurationTime: 100 # 运行时间
                  - idInJob: 1 # 作业内ID
                    runDurationTime: 100 # 运行时间
                  - idInJob: 2 # 作业内ID
                    runDurationTime: 100 # 运行时间
      nCopiedC2netJobGroups: 1 # 复制的作业组数量


scheduleConfig: # 调度配置
  cycleSchedule: true # 是否循环调度
  timeResolution: 2.0 # 时间分辨率
  mandatoryComputingCenterSchedule: true # 是否强制指定计算中心调度
  heteroComputingCenterSchedule: false # 是否异构计算中心调度
  heteroComputingCenterConversionRatioList: # 异构计算中心转换比率列表
    - graphicsCardType: ENFLAME-T20 # 显卡类型
      nCpus: 8 # CPU核心数
      runDurationTime: 1.5 # 运行时间
    - graphicsCardType: ASCEND910 # 显卡类型
      nCpus: 24 # CPU核心数
      runDurationTime: 0.75 # 运行时间
    - graphicsCardType: A100 # 显卡类型
      nCpus: 4 # CPU核心数
      runDurationTime: 0.5 # 运行时间
    - graphicsCardType: V100 # 显卡类型
      nCpus: 8 # CPU核心数
      runDurationTime: 1.0 # 运行时间

networkConfig: # 网络配置
  construct: false # 是否构建网络
  accelerator: false # 是否启用加速器
  monitor: false # 是否启用监控
  topologyTpye: topology01 # 拓扑类型


backendConfig: # 后端配置
  connect: false # 是否连接后端
  url: http://0.0.0.0:30009 # 后端URL
  taskId: 0 # 任务ID
  remark: "大湾区仿真任务图测试" # 备注


chooseComputingCenterForJobFuncName: default # 为作业选择计算中心的函数名
