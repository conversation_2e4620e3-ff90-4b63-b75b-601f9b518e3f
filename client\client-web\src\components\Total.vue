<template>
  <div class="mt">
    <div class="borderstyle">
      <el-row>
        <el-col :span="7"
          ><div>
            <span class="miantitle">任务ID:</span
            ><span class="miancontent">{{ taskDetail.ID }}</span>
          </div></el-col
        >
        <el-col :span="10"
          ><div>
            <span class="miantitle">任务数:</span
            ><span class="miancontent">{{ taskDetail.NJobs }}</span>
          </div></el-col
        >
        <el-col :span="7"
          ><div>
            <span class="miantitle">状态:</span>
             <span class="miancontent" v-if="!taskDetail.ID"
              ></span
            >
            <span class="miancontent" v-if="taskDetail.CompletedFlag&&taskDetail.ID"
              >已完成</span
            >
            <span class="miancontent" v-if="(!taskDetail.CompletedFlag)&&taskDetail.ID"
              >运行中</span
            >
          </div></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="7"
          ><div>
            <span class="miantitle">仿真集群:</span
            ><span class="miancontent">{{ msg.NCenters }}个</span>
          </div></el-col
        >
        <el-col :span="10"
          ><div>
            <span class="miantitle">仿真算力:</span
            ><span class="miancontent">{{ msg.NPops }}POps@FP16</span>
          </div></el-col
        >
        <el-col :span="7"
          ><div>
            <span class="miantitle">仿真时长:</span
            ><span class="miancontent">{{ msg.SnapshotTime }}</span>
          </div></el-col
        >
      </el-row>
      <!-- <div class="line1"></div>
      <div class="line2"></div> -->
    </div>
  </div>
</template>
<script>
import { jobDetail } from '@/api/screenService.js'
import { formatDuring2 } from '@/utils/index'
export default {
  name: 'total',
  data () {
    return {
      // msg: { SnapshotTime: "", NCenters: 0, NPops: 0 },
      // timer: null,
      // total: 0,
      // count: 0,
    }
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    statusData: {
      type: Object,
      default: () => {}
    },
    msg: {
      type: Object,
      default: () => {}
    },
    taskDetail: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    msg: {
      handler (newValue, oldValue) {
        // clearInterval(this.timer);
        // this.timer = null;
        // this.getJobDetail(newValue);
      },
      deep: true // 监听对象内部的属性变化
    }
  },
  computed: {
    taskId () {
      return this.$store.state.id
    }
  },
  mounted () {
    if (this.msg.CompletedFlag == null) {
      this.msg.CompletedFlag == ' '
    }
    // this.getJobDetail(this.taskId);
  },
  methods: {
    // getJobDetail(id) {
    //   let data = { id: id, resolution_n_hours: 10 };
    //   jobDetail(data).then((res) => {
    //     // this.msg.SnapshotTime = res.SnapshotTime;
    //     this.msg.NPops = res.NPops;
    //     this.msg.NCenters = res.NCenters;
    //     this.total = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length;

    //     if (this.total == 0) {
    //       return;
    //     }
    //     this.count = 0;
    //     this.msg.SnapshotTime = Math.round(
    //       res.CenterInfoToWebList[0].SnapshotInfoToWebList[this.count].Time
    //     );
    //     let that = this;
    //     this.timer = setInterval(() => {
    //       // Data.xData = [];
    //       that.count++;
    //       if (that.count >= that.total) {
    //         that.count = 0;
    //         clearTimeout(that.timer);
    //         that.timer = null;
    //         that.getJobDetail(that.taskId);
    //       }
    //       this.msg.SnapshotTime = Math.round(
    //         res.CenterInfoToWebList[0].SnapshotInfoToWebList[that.count].Time
    //       );
    //     }, 5000);
    //   });
    // },
    formatDuring (val) {
      //  console.log(val)
      return formatDuring2(val)
    }
  }
}
</script>
<style scoped>
.icon {
  background-image: url(../static/screen1/cloud.png);
  width: 80px;
  height: 80px;
  background-size: 100% 100%;
  background-position-y: 10px;
  background-repeat: no-repeat;
}

.title {
  font-family: "思源黑体 CN-Regular";
  font-size: 20px;
  color: rgb(211, 237, 255);
  letter-spacing: 1px;
  font-weight: normal;
  font-style: normal;
  transform: translate(0px, 0px);
  word-break: keep-all;
}

.content {
  padding-top: 3px;
  padding-left: 10px;
}

.num {
  color: #05bcfd;
  width: 24px;
  height: 28px;
  line-height: 28px;
  color: rgba(5, 188, 253, 1);
  font-size: 24px;
  text-align: left;
  font-family: PMZDBiaoTi-regular;
}

.unit {
  display: inline-flex;
  align-items: center;
  color: rgb(211, 237, 255);
  font-size: 20px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  padding-left: 2px;
}

.unitBg {
  color: rgb(211, 237, 255);
  font-size: 20px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  /* padding-left: 10px; */
}

.arrow {
  background-size: 100% 150%;
  background-image: url("../static/screen1/arrow.png");
  width: 50px;
  height: 30px;
  background-position: 0px -8px;
  background-repeat: no-repeat;
}

.mt {
  margin:0 auto
  /* margin-top: 0px !important;
  position: relative;
  left: -60px;
  width: 850px; */
}
.loading-tip1 {
  background-image: url("../assets/icon/riLine-map-pin-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip2 {
  background-image: url("../assets/icon/riLine-camera-lens-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip3 {
  background-image: url("../assets/icon/riLine-database-2-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.circle1 {
  border-radius: 50px;
  border: 1px dashed #1890ff;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 18px;
}
.circle2 {
  border-radius: 50px;
  border: 1px dashed #1890ff;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 3px;
}
.circle3 {
  border-radius: 50px;
  border: 1px dashed #1890ff;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 6px;
  left: 18px;
}
.dv-loading {
  width: 100%;
  height: 100%;
}
.miantitle {
  display: inline-block;
  /* color: #1890ff; */
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 0 3px rgba(0, 217, 255, 0.3);
  margin-right: 5px;
  font-size: 22px;
  text-align: right;
  width: 120px;
  height: 30px;
  line-height: 30px;
  /* font-weight: 600; */
}
.miancontent {
  display: inline-block;
  /* color: #fff; */
  color: rgba(0, 217, 255, 0.95);
  text-shadow: 0 0 15px rgba(0, 217, 255, 0.6);
  font-size: 24px;
  height: 30px;
  line-height: 30px;
  font-weight: 800;
}
.borderstyle {
  /* background-color: #0A1E37; */
  /* background-color: rgba(10, 30, 55, 0.25); */
  background-color: rgba(10, 30, 55, 0.15);
  border: 1px solid rgba(0, 190, 255, 0.3);
  min-height: 110px;
  padding: 20px 15px;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  border-radius: 6px;
  box-shadow: inset 0 0 10px rgba(0, 100, 150, 0.2);
  transition: all 0.3s ease;
}

.borderstyle:hover {
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.35), inset 0 0 25px rgba(0, 217, 255, 0.08);
  border-color: rgba(0, 217, 255, 0.35);
}

.line1 {
  position: absolute;
  width: 1px;
  height: 80px;
  background: rgba(37, 140, 255, 0.4);
  top: 15px;
  left: 215px;
}
.line2 {
  width: 1px;
  height: 80px;
  position: absolute;
  background: rgba(37, 140, 255, 0.4);
  top: 15px;
  left: 540px;
}
</style>
