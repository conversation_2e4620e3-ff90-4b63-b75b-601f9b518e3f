/**
 * 拓扑预览混入 - 封装拓扑预览相关操作逻辑
 */
export default {
  data () {
    return {
      topologyPreviewVisible: false,
      currentTopologyImage: ''
    }
  },
  methods: {
    /**
     * 显示拓扑预览
     */
    showTopologyPreview () {
      if (!this.taskForm.topologyTpye) {
        this.$message.warning('请先选择网络拓扑')
        return
      }

      try {
        this.currentTopologyImage = require(`@/assets/topology/${this.taskForm.topologyTpye}.png`)
        this.topologyPreviewVisible = true
      } catch (error) {
        console.error('加载拓扑图片失败:', error)
        this.$message.error('拓扑图片加载失败')
      }
    }
  }
}
