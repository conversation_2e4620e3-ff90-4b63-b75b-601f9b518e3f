<template>
    <div>
        <dt-srcoll :newData="dutyRateData" :menuData="menuData" :lineHeight="5" :tableHeight="40" v-if="fresh">
        </dt-srcoll>
    </div>
</template>
<script>
import DtSrcoll from '../barComponents/scroll'
export default {
  components: {
    DtSrcoll
  },
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        this.fresh = false
        this.dutyRateData = newValue
        this.$nextTick(() => {
          this.fresh = true
        })
      },
      deep: true
    }
  },
  data () {
    return {
      menuData: [ // 表格内容
        {
          name: '任务名称',
          prop: 'name'
        },
        {
          name: '状态',
          prop: 'status'
        },
        {
          name: '承接方',
          prop: 'undertaker'
        }

      ],
      dutyRateData: [],
      fresh: true

    }
  }
}
</script>
