<template>
  <div :id="id" style="width: 100%; height: 235px" ref="echart"></div>
</template>
<script>
export default {
  name: 'histogram2',
  data () {
    return {
      timer: null,
      timer2: null,
      Data: {
        unused: [],
        used: [],
        xData: []
      },
      BarWidth: 35,
      Max: 0
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        const Data = { unused: [], used: [], xData: [] }
        clearInterval(this.timer)
        const TempData = JSON.parse(JSON.stringify(newValue))
        this.barWidth = 15
        Data.xData = TempData.xData
        Data.used = TempData.used ? TempData.used : []
        Data.unused = TempData.unused ? TempData.unused : []
        if (TempData.xData.length <= 5) {
          this.Data = Data
          this.drawLine()
        } else {
          this.Data.unused = Data.unused.slice(0, 5)
          this.Data.used = Data.used.slice(0, 5)
          this.Data.xData = Data.xData.slice(0, 5)
          this.drawLine()
          this.timer = setInterval(() => {
            const unused = Data.unused.shift()
            const used = Data.used.shift()
            const xData = Data.xData.shift()
            Data.unused.push(unused)
            Data.used.push(used)
            Data.xData.push(xData)
            this.Data.unused = Data.unused.slice(0, 5).reverse()
            this.Data.used = Data.used.slice(0, 5).reverse()
            this.Data.xData = Data.xData.slice(0, 5).reverse()
            this.drawLine()
          }, 3000)
        }
      },
      deep: true
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.drawLine()
    })
    window.addEventListener('resize', this.resize)
    window.addEventListener('visibilitychange', this.Refresh)
    this.timer2 = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('visibilitychange', this.Refresh)
    clearInterval(this.timer)
    this.timer = null
    clearInterval(this.timer2)
    this.timer2 = null
  },
  // destroyed() {
  //   clearInterval(this.timer);
  // },
  methods: {
    drawLine () {
      if (this.myChart) {
        this.myChart && this.myChart.dispose()
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        title: {
          text:
            this.Data.unused.length === 0 &&
            this.Data.used.length === 0 &&
            this.Data.xData.length == 0
              ? '暂无数据'
              : '',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10]
        },
        // color: ["#32C5FF"],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            show: false
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        legend: {
          show: false,
          right: '0',
          itemWidth: 15,
          color: '#fff'
        },
        grid: {
          left: '30%',
          bottom: '18%',
          top: '12%'
        },
        yAxis: {
          type: 'category',
          nameTextStyle: {
            // 纵坐标单位文字
            // 字体样式
            color: '#FFF',
            padding: [5, 40, 0, 0] // 距离坐标位置的距离
          },
          data: this.Data.xData,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            interval: 0,
            fontSize: '14',
            lineHeight: 30,
            fontWeight: 'bold',
            color: '#fff',
            fontFamily: 'Microsoft YaHei',
            formatter: function (value, index) {
              if (value.length > 10) {
                return value.substr(0, 10) + '...'
              } else {
                return value
              }
            }
          },
          axisTick: {
            // x轴刻度相关设置
            alignWithLabel: true,
            show: false
          }
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: false,
            lineStyle: {
              width: 1,
              type: 'dotted'
            }
          },
          data: [],
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: false,
            fontSize: '8',
            fontWeight: 'bold',
            color: '#fff'
          },
          scale: true,
          min: 0,
          splitNumber: 1
        },
        series: [
          {
            name: this.config.status[0] ? this.config.status[0] : '',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: true, // 开启显示
              position: 'right', // 柱形上方
              // 数值样式
              color: '#fff',
              formatter: (param) => {
                return `{a|${param.data}}{b| 卡时}`
              },
              rich: {
                a: {
                  color: '#ffd408',
                  fontSize: 12,
                  fontWeight: 'bold'
                },
                b: {
                  color: '#DEDEDE',
                  fontSize: 10,
                  fontWeight: 'bold'
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            showBackground: true,

            backgroundStyle: {
              color: 'rgba(0,0,0,1)'
            },
            data: this.Data.used ? this.Data.used : [],
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(24, 144, 255,0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(24, 144, 255)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 100
        },
        animationEasing: 'backln'
      }
      if (this.Data.xData.length > 8) {
        option.xAxis.axisLabel.formatter = function (params) {
          var newParamsName = ''
          var paramsNameNumber = params.length
          var provideNumber = 4 // 一行显示几个字
          var rowNumber = Math.ceil(paramsNameNumber / provideNumber)
          if (paramsNameNumber > provideNumber) {
            for (var p = 0; p < rowNumber; p++) {
              var tempStr = ''
              var start = p * provideNumber
              var end = start + provideNumber
              if (p == rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber)
              } else {
                tempStr = params.substring(start, end) + '\n'
              }
              newParamsName += tempStr
            }
          } else {
            newParamsName = params
          }
          return newParamsName
        }
      }
      option && this.myChart.setOption(option)
    },
    digit (val) {
      let num = Math.trunc(val)
      const number = num.toString() // 把数字转换成字符串
      var temp = num
      var count = 0
      if (num == 0) {
        return 0
      } else {
        while (num != 0) {
          count++ // 统计位数；
          num = parseInt(num / 10) // 赋值给 num 以备下次循环使用；
        }
        return count
      }
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    }
  }
}
</script>
