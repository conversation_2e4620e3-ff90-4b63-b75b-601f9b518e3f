/**
 * 表单工具函数
 */

/**
 * 验证数字输入，只允许输入数字和小数点
 * @param {Event} e - 键盘事件对象
 * @returns {boolean} - 是否允许输入
 */
export function validateNumberInput (e) {
  // 允许数字、小数点、退格键、Tab键、方向键等控制键
  const keyCodes = [8, 9, 37, 38, 39, 40, 46]
  const keyCode = e.keyCode || e.which

  // 如果是数字键（0-9）
  if ((keyCode >= 48 && keyCode <= 57) || (keyCode >= 96 && keyCode <= 105)) {
    return true
  }

  // 如果是特殊控制键
  if (keyCodes.indexOf(keyCode) !== -1) {
    return true
  }

  // 如果是小数点
  if (keyCode === 110 || keyCode === 190) {
    // 防止输入多个小数点
    if (e.target.value.includes('.')) {
      e.preventDefault()
      return false
    }
    return true
  }

  // 阻止其他所有输入
  e.preventDefault()
  return false
}

/**
 * 获取表单校验规则
 * @returns {Object} - 表单校验规则
 */
export function getTaskFormRules () {
  return {
    scheduleType: [
      { required: true, message: '请选择调度策略', trigger: 'change' }
    ]
  }
}

/**
 * 获取计算中心表单校验规则
 * @returns {Object} - 计算中心表单校验规则
 */
export function getCenterFormRules () {
  return {
    name: [
      { required: true, message: '请输入计算中心名称', trigger: 'blur' }
    ],
    pOPS: [
      { required: true, message: '请输入算力规模', trigger: 'blur' },
      { type: 'number', message: '必须为数字值', trigger: 'blur' }
    ],
    nNodes: [
      { required: true, message: '请输入节点数', trigger: 'blur' },
      { type: 'number', message: '必须为数字值', trigger: 'blur' }
    ],
    graphicsCardType: [
      { required: true, message: '请选择加速卡型号', trigger: 'change' }
    ],
    nGraphicsCards: [
      { required: true, message: '请输入加速卡数', trigger: 'blur' },
      { type: 'number', message: '必须为数字值', trigger: 'blur' },
      { type: 'number', min: 1, max: 8, message: '加速卡数必须在1-8之间', trigger: 'blur' }
    ],
    nGBMemoriesPerGraphicsCard: [
      { required: true, message: '请输入显存', trigger: 'blur' },
      { type: 'number', message: '必须为数字值', trigger: 'blur' }
    ],
    nCpus: [
      { required: true, message: '请输入CPU数', trigger: 'blur' },
      { type: 'number', message: '必须为数字值', trigger: 'blur' }
    ],
    nGBMemories: [
      { required: true, message: '请输入内存', trigger: 'blur' },
      { type: 'number', message: '必须为数字值', trigger: 'blur' }
    ]
  }
}

/**
 * 获取任务表单的默认值
 * @returns {Object} - 任务表单默认值
 */
export function getDefaultTaskForm () {
  return {
    scheduleType: '本地调度',
    cycleSchedule: true,
    timeResolution: 2.0,
    useJsonFile: true, // 确保默认始终为 true
    jsonFileList: [],
    construct: false,
    topologyTpye: 'topology01',
    accelerator: true
  }
}

/**
 * 获取计算中心表单的默认值
 * @returns {Object} - 计算中心表单默认值
 */
export function getDefaultCenterForm () {
  return {
    name: '',
    pOPS: 100,
    nNodes: 100,
    graphicsCardType: 'ASCEND910',
    nGraphicsCards: 8,
    nGBMemoriesPerGraphicsCard: 32,
    nCpus: 512,
    nGBMemories: 800
  }
}
