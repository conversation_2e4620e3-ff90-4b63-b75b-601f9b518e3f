<template>
    <div>
        <el-row type="flex" justify="space-between">
            <el-col :span="12">
                <div id="id" :style="{width: '150px', height: '140px'}" ref="echart"></div>
            </el-col>
            <el-col :span="12">
                <div class="numWrapper">
                    <div><span class="num1">{{used}}</span><span class="state">/已使用</span></div>
                    <div><span class="num2">{{total}}</span><span class="state">/总数量</span></div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script>
import { formatSize } from '@/utils/index.js'
export default {
  name: 'pie',
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => { }
    },
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      used: undefined,
      total: undefined,
      gaugeData: [
        {
          value: this.data,
          name: this.title,
          title: {
            offsetCenter: ['0%', '25%']
          },
          detail: {
            valueAnimation: true,
            offsetCenter: ['0%', '-20%']
          }
        }
      ]
    }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        let num1
        let num2
        this.used = newValue.used
        this.total = newValue.total
        num1 = formatSize(newValue.used).replace(/[^0-9.]/ig, '')
        num2 = formatSize(newValue.total).replace(/[^0-9]/ig, '')
        if (num1 == 0 || num2 == 0) {
          this.gaugeData[0].value = 0
        } else { this.gaugeData[0].value = (((num1 / num2) * 100).toFixed(0)) }
        this.drawLine()
      },
      deep: true
    }
  },
  mounted () {
    this.drawLine()
  },
  methods: {
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      const myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        color: ['rgb(30, 231, 231)'],
        gradientColor: ['rgb(24, 144, 255)', 'rgb(30, 231, 231)', 'rgb(242,202,69)'],
        series: [
          {
            type: 'gauge',
            startAngle: 90,
            endAngle: -270,
            pointer: {
              show: false
            },
            radius: '90%',
            progress: {
              show: true,
              overlap: false,
              roundCap: true,
              clip: false,
              itemStyle: {
                borderWidth: 1,
                borderColor: '#464646'
              }
            },
            min: 0,
            axisLine: {
              lineStyle: {
                width: 10,
                color: [[1, 'rgba(24,151,253,0.3)']]
              }
            },
            splitLine: {
              show: false,
              distance: 0,
              length: 10
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false,
              distance: 50
            },
            data: this.gaugeData,
            title: {
              fontSize: 16,
              show: true,
              color: 'rgb(255, 255, 255)'
            },
            detail: {
              width: 50,
              height: 14,
              color: 'rgb(30, 231, 231)',
              borderWidth: 0,
              formatter: '{b|{value}%}',
              rich: {
                b: {
                  fontSize: 25
                }
              }
            }

          }
        ]
      }
      option && myChart.setOption(option)
    }

  }

}
</script>
<style scoped>
    .gauge {
        margin: 0 auto;
    }

    .numWrapper {
        font-size: 20px;
        margin-top: 50px;
        text-align: left;
        letter-spacing: 4px;
    }

    .num1 {
        display: inline-flex;
        align-items: center;
        color: rgb(255, 200, 24);
        font-size: 20px;
        font-family: DINPro-Medium;
        font-weight: normal;
        font-style: normal;
        text-shadow: none;
    }

    .num2 {
        display: inline-flex;
        align-items: center;
        color: rgb(0, 162, 255);
        font-size: 20px;
        font-family: DINPro-Medium;
        font-weight: normal;
        font-style: normal;
        text-shadow: none;
    }

    .state {
        display: inline-flex;
        align-items: center;
        color: rgba(250, 251, 252, 0.9);
        font-size: 20px;
        font-family: "思源黑体 CN-Regular";
        letter-spacing: 1px;
        font-weight: normal;
        font-style: normal;
        transform: translate(3px, 2px);
    }

    /* .gauge {
        margin: 0 auto;
    } */
</style>
