import { ref, computed, watch } from 'vue'
import { ChartService } from '@/services/ChartService'

/**
 * 图表数据管理组合式函数
 * 负责图表数据状态管理、配置生成、数据更新等
 */
export function useChartData () {
  // ============ 图表数据状态 ============
  const chartData = ref({
    xData: [],
    used: [],
    used2: []
  })

  const overviewData = ref({
    xAxis: [],
    yAxis: []
  })

  // 任务图表类型控制
  const chartTypeState = ref({
    check1: true,
    check2: false,
    check3: false,
    taskType: 1,
    name: '提交任务量'
  })

  // 图表实例引用
  const chartRefs = ref({
    myChart: null,
    myChart2: null
  })

  // ============ 图表配置 ============
  const chartConfigs = computed(() => ({
    taskConfig: {
      unit: '单位:个',
      status: ['运行任务数', '等待任务数']
    },
    statusConfig: {
      unit: '单位: 卡时',
      status: ['使用量']
    },
    tendConfig: {
      unit: '单位: 卡时',
      status: ['使用量']
    },
    connectedConfig: {
      unit: '单位: POps@FP16',
      status: ['接入数']
    }
  }))

  // ============ 图表类型切换 ============
  const switchChartType = (type) => {
    // 重置所有选中状态
    chartTypeState.value.check1 = false
    chartTypeState.value.check2 = false
    chartTypeState.value.check3 = false

    // 设置对应类型为选中
    switch (type) {
      case 1:
        chartTypeState.value.check1 = true
        chartTypeState.value.name = '提交任务量'
        break
      case 2:
        chartTypeState.value.check2 = true
        chartTypeState.value.name = '任务平均等待时长'
        break
      case 3:
        chartTypeState.value.check3 = true
        chartTypeState.value.name = '资源利用率对比'
        break
    }

    chartTypeState.value.taskType = type
  }

  // ============ 数据处理方法 ============
  const updateOverviewData = (taskStatistics) => {
    overviewData.value = {
      xAxis: ['总任务数', '执行完成'],
      yAxis: [taskStatistics.totalNum || 0, taskStatistics.execNum || 0]
    }
  }

  const updateChartData = (timeData, dataArray) => {
    chartData.value = {
      xData: timeData || [],
      used: dataArray || [],
      used2: [] // 可用于对比数据
    }
  }

  // 生成线性图表配置
  const generateLineChartConfig = (title, xData, yData, unit = '') => {
    return ChartService.createLineChartConfig({
      title,
      xData,
      yData,
      unit
    })
  }

  // 生成柱状图配置
  const generateBarChartConfig = (title, xData, yData, unit = '') => {
    return ChartService.createBarChartConfig({
      title,
      xData,
      yData,
      unit
    })
  }

  // 生成饼图配置
  const generatePieChartConfig = (title, data) => {
    return ChartService.createPieChartConfig({
      title,
      data
    })
  }

  // ============ ECharts 实例管理 ============
  const setChartInstance = (chartKey, instance) => {
    chartRefs.value[chartKey] = instance
  }

  const getChartInstance = (chartKey) => {
    return chartRefs.value[chartKey]
  }

  const disposeAllCharts = () => {
    Object.values(chartRefs.value).forEach(chart => {
      if (chart && typeof chart.dispose === 'function') {
        chart.dispose()
      }
    })
    chartRefs.value = {
      myChart: null,
      myChart2: null
    }
  }

  // ============ 响应式数据更新 ============
  const refreshChartData = (newData) => {
    if (newData) {
      // 根据当前图表类型更新对应数据
      switch (chartTypeState.value.taskType) {
        case 1:
          updateChartData(newData.timeData, newData.submitJobData)
          break
        case 2:
          updateChartData(newData.timeData, newData.pendingTimeData)
          break
        case 3:
          updateChartData(newData.timeData, newData.resourceUsageData)
          break
      }
    }
  }

  // ============ 监听器 ============
  watch(
    () => chartTypeState.value.taskType,
    (newType) => {
      console.log('图表类型切换至:', newType)
      // 这里可以添加图表切换时的数据刷新逻辑
    }
  )

  return {
    // 状态
    chartData,
    overviewData,
    chartTypeState,
    chartRefs,

    // 配置
    chartConfigs,

    // 方法
    switchChartType,
    updateOverviewData,
    updateChartData,
    generateLineChartConfig,
    generateBarChartConfig,
    generatePieChartConfig,
    setChartInstance,
    getChartInstance,
    disposeAllCharts,
    refreshChartData
  }
}
