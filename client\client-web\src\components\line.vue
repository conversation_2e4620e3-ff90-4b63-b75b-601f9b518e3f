<template>
  <div>
    <el-row type="flex" class="row-bg" justify="center">
      <el-col :span="12"
        ><div>
          <el-row type="flex" class="row-bg" justify="space-around">
            <el-col :span="5" :class="{ bg: true, bg2: show1 }"
              ><div
                @click="changeTime(1)"
                :class="{ button: true, check: show1 }"
              >
                所有
              </div></el-col
            >
            <el-col :span="5" class="bg"
              ><div
                @click="changeTime(2)"
                :class="{ button: true, check: show2 }"
              >
                今年
              </div></el-col
            >
            <el-col :span="5" class="bg"
              ><div
                @click="changeTime(3)"
                :class="{ button: true, check: show3 }"
              >
                本月
              </div></el-col
            >
          </el-row>
        </div></el-col
      >
    </el-row>
    <div :id="id" style="width: 100%; height: 197px" ref="echart"></div>
  </div>
</template>
<script>
import { getTend } from '@/api/screenService.js'
import { filterData } from '@/utils/index'
export default {
  name: 'lineChart',
  data () {
    return {
      tendData: { used: [], xData: [] },
      time: 1,
      show1: true,
      show2: false,
      show3: false,
      show4: false,
      lastData: { date: '', computerPower: 0 }
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    config: {
      type: Object,
      default: () => ({})
    },
    type: { type: Number }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        const TempData = JSON.parse(JSON.stringify(newValue))
        this.Data.used = TempData.used
        this.Data.xData = TempData.xData
        this.drawLine()
      },
      deep: true
    }
  },
  mounted () {
    this.getTend(1)
    this.drawLine()
    window.addEventListener('resize', () => {
      this.myChart.resize()
      this.getTend(1)
    })
  },
  destroyed () {
    clearInterval(this.timer)
  },
  methods: {
    filterData (val) {
      return filterData(val)
    },
    getTend (val) {
      const type = val
      let data = { createdAtGte: undefined, createdAtLt: undefined }
      switch (val) {
        case 1:
          data = null
          break
        case 2:
          const bb = new Date()
          const dd = new Date('2023-01-01')
          data.createdAtGte = (dd.getTime() / 1000).toFixed(0)
          data.createdAtLt = (bb.getTime() / 1000).toFixed(0)
          break
        case 3:
          const cc = new Date()
          data.createdAtLt = (cc.getTime() / 1000).toFixed(0)
          var month = new Date()
          month.setDate(1)
          month.setHours(0)
          month.setSeconds(0)
          month.setMinutes(0)
          data.createdAtGte = (month.getTime() / 1000).toFixed(0)
          break
        default:
          var now = new Date().getTime()
          data.createdAtGte = (now / 1000).toFixed(0)
          data.createdAtLt = (now / 1000).toFixed(0)
      }
      getTend(data).then((res) => {
        res.dailyComputerPowers.forEach((item) => {
          if (item.date == '2023-07-29') {
            item.date = '2023-11-18'
          }
          if (item.date == '2023-07-26') {
            item.date = '2023-11-15'
          }
          if (item.date == '2023-07-25') {
            item.date = '2023-11-14'
          }
          if (item.date == '2023-07-24') {
            item.date = '2023-11-12'
          }
          if (item.date == '2023-07-21') {
            item.date = '2023-11-10'
          }
          if (item.date == '2023-07-20') {
            item.date = '2023-11-8'
          }
          if (item.date == '2023-07-19') {
            item.date = '2023-11-2'
          }
        })
        if (res.dailyComputerPowers) {
          this.lastData.date = res.dailyComputerPowers[0].date
          this.lastData.computerPower =
            res.dailyComputerPowers[0].computerPower.toFixed(1)
        } else {
          this.lastData.date = ''
          this.lastData.computerPower = 0
        }

        if (res.dailyComputerPowers == null) {
          this.tendData = { used: [], xData: [] }
          this.drawLine(val)
        } else {
          res.dailyComputerPowers.forEach((item) => {
            item.date = item.date.replace(/-/g, '.')
          })
          this.tendData = { used: [], xData: [] }
          if (res.dailyComputerPowers.length > 31) {
            res.dailyComputerPowers.forEach((item, index) => {
              if (index % 30 == 0) {
                this.tendData.used.push(item.computerPower.toFixed(1))
                this.tendData.xData.push(item.date)
              }
            })
          } else {
            res.dailyComputerPowers.forEach((item) => {
              this.tendData.used.push(item.computerPower.toFixed(1))
              this.tendData.xData.push(item.date)
            })
          }
          this.drawLine(val)
        }
      })
    },
    drawLine (val) {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        title: {
          text:
            this.tendData.xData.length === 0 && this.tendData.used.length === 0
              ? '暂无数据'
              : '',
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10],
          textStyle: {
            color: '#fff'
          }
        },
        color: '#32c5ff ',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            show: false
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        legend: {
          icon: 'rect',
          right: '0',
          itemHeight: 1,
          itemWidth: 40,
          textStyle: { color: '#fff' },
          show: false
        },
        grid: {
          right: '5%',
          bottom: '18%',
          top: '12%',
          left: '5%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.tendData.xData,
          axisLine: {
            show: false // 不显示坐标轴线
          },
          axisLabel: {
            show: true,
            showMaxLabel: true,
            interval: 1,
            textStyle: {
              fontSize: '10',
              lineHeight: 40,
              color: 'rgb(1, 145, 255)',
              fontFamily: 'Microsoft YaHei'
            }
          },
          axisTick: {
            // x轴刻度相关设置
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: this.config.unit,
          show: false,
          nameTextStyle: {
            // 关键代码
            padding: this.config.unit.length > 7 ? [0, 0, 0, 45] : [0, 0, 0, 0]
          },
          splitLine: {
            show: false,
            lineStyle: {
              width: 0.5,
              type: 'dotted',
              color: '#fff'
            }
          },
          nameGap: 10,
          data: [],
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: false,
            interval: 1,
            align: 'right',
            textStyle: {
              fontSize: '8',
              fontWeight: 'bolder',
              color: '#fff'
            }
          },
          scale: true,
          min: 0,
          splitNumber: 3
        },
        series: [
          {
            name: this.config.status[0] ? this.config.status[0] : '',
            type: 'line',
            symbol: 'none',
            smooth: true,
            label: {
              show: false
            },
            data: this.tendData.used,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgb(50,197,255)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(22,93,255,0)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 500
        },
        animationEasing: 'backln'
      }
      if (val == 3 || val == 2 || val == 1) {
        this.tendData.used = this.tendData.used.reverse()
        this.tendData.xData = this.tendData.xData.reverse()
        option.xAxis.axisLabel.formatter = (params) => {
          // params=params.replace('2023, '0');
          return params
            .replace('2023.', '')
            .replace(/^0/gi, '')
            .replace('.0', '.')
        }
      }
      if (this.tendData.xData.length <= 7) {
        option.xAxis.axisLabel.interval = 0
      } else if (
        this.tendData.xData.length > 7 &&
        this.tendData.xData.length <= 14
      ) {
        option.xAxis.axisLabel.interval = 1
      } else if (
        this.tendData.xData.length > 14 &&
        this.tendData.xData.length <= 21
      ) {
        option.xAxis.axisLabel.interval = 2
      } else {
        option.xAxis.axisLabel.interval = 10
      }
      option && this.myChart.setOption(option)
    },
    changeTime (val) {
      this.time = val
      switch (val) {
        case 1:
          this.show1 = true
          this.show2 = false
          this.show3 = false
          this.show4 = false
          this.getTend(1)
          break
        case 2:
          this.show1 = false
          this.show2 = true
          this.show3 = false
          this.show4 = false
          this.getTend(2)
          break
        case 3:
          this.show1 = false
          this.show2 = false
          this.show3 = true
          this.show4 = false
          this.getTend(3)
          break
        case 4:
          this.show1 = false
          this.show2 = false
          this.show3 = false
          this.show4 = true
          this.getTend(4)
          break
        default:
      }
    }
  }
}
</script>
<style scoped>
.button {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.row-bg {
  padding-top: 5px;
}
.check {
  color: rgb(50, 197, 255);
  font-family: 思源黑体;
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 0px;
}
.bg {
  box-sizing: border-box;
  background: radial-gradient(
    65% 100% at 50% 0%,
    rgba(1, 145, 255, 0.33),
    rgba(255, 1, 246, 0) 100%
  );
  border: 1px solid rgba(68, 103, 215, 0.5);
  padding: 5px;
}
.bg2 {
  border: 1px solid rgb(50, 197, 255);
}
</style>
