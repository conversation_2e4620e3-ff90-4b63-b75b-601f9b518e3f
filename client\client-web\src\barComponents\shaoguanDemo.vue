<!-- eslint-disable vue/require-v-for-key -->
<template>
  <div class="min-width">
    <el-row type="flex" class="row-bg" justify="center">
      <el-col :span="22">
        <el-row>
          <el-col :span="10">
            <div class="mapWrapper1">
              <div class="itemContent">
                <el-row :gutter="20">
                  <el-row>
                    <el-col :span="10" :offset="14">
                      <div class="computeWrapper">
                        <span class="ComputeName">总算力: </span>
                        <span class="ComputeTotal">3963</span
                        ><span class="ComputeUnit"> PFlops</span>
                        <div class="status5 unitPos"></div>
                      </div>
                    </el-col>
                  </el-row>
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="green">
                      <span class="cursor" @click="changeItem(1, item1)">{{
                        item1.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow1"></div>
                    <div class="unitWrapper">
                      <span class="totalNum1">{{ item1.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(2, item2)">{{
                        item2.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 288px">
                      <div class="smallArrow2" style="width: 20px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 364px"
                      >
                        <span class="cpNum">{{ item2.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item2.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(3, item3)">{{
                        item3.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 256px"></div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item3.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(4, item4)">
                        {{ item4.itemName }}</span
                      >
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 230px">
                      <div class="smallArrow2" style="width: 12px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 350px"
                      >
                        <span class="cpNum">{{ item4.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item4.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(5, item5)">{{
                        item5.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 160px"></div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item5.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(6, item6)">{{
                        item6.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 240px">
                      <div class="smallArrow2" style="width: 15px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 355px"
                      >
                        <span class="cpNum">{{ item6.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item6.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(7, item7)">{{
                        item7.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 176px">
                      <div class="smallArrow2" style="width: 44px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 385px"
                      >
                        <span class="cpNum">{{ item7.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item7.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(8, item8)">{{
                        item8.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 360px">
                      <div class="smallArrow2" style="width: 33px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 375px"
                      >
                        <span class="cpNum">{{ item8.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item8.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(9, item9)">{{
                        item9.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 128px">
                      <div class="smallArrow2" style="width: 20px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 360px"
                      >
                        <span class="cpNum">{{ item9.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item9.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(10, item10)">{{
                        item10.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 288px">
                      <div class="smallArrow2" style="width: 24px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 365px"
                      >
                        <span class="cpNum">{{ item10.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item10.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(11, item11)">{{
                        item11.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 208px">
                      <div class="smallArrow2" style="width: 24px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 365px"
                      >
                        <span class="cpNum">{{ item11.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item11.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(12, item12)">{{
                        item12.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 230px">
                      <div class="smallArrow2" style="width: 40px"></div>
                      <div
                        class="cpWrapper"
                        style="postion: relative; left: 380px"
                      >
                        <span class="cpNum">{{ item12.totalCp }}</span
                        ><span class="cpUnit">智算机架</span>
                      </div>
                    </div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item12.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
                <el-row>
                  <el-col :span="10"
                    ><div class="blue">
                      <span class="cursor" @click="changeItem(13, item13)">{{
                        item13.itemName
                      }}</span>
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div class="arrow2" style="width: 304px"></div>
                    <div class="unitWrapper">
                      <span class="totalNum2">{{ item13.totalFrame }}</span
                      ><span class="totalUnit">总机架</span>
                    </div></el-col
                  >
                </el-row>
              </div>
              <computeTotal class="computeTotal"></computeTotal>
              <div class="iconWrapper">
                <el-row :gutter="20">
                  <el-col :span="10"
                    ><div>
                      <el-col :span="8"
                        ><div class="status1"></div>
                        <div class="statusText">建设中</div></el-col
                      >
                      <el-col :span="12"
                        ><div class="status2"></div>
                        <div class="statusText">已投入运营</div></el-col
                      >
                    </div></el-col
                  >
                  <el-col :span="14"
                    ><div>
                      <el-col :span="8"
                        ><div class="status3"></div>
                        <div class="statusText">规划总机架</div></el-col
                      >
                      <el-col :span="8"
                        ><div class="status4"></div>
                        <div class="statusText">规划智算机架</div></el-col
                      >
                      <el-col :span="8"
                        ><div class="status5"></div>
                        <div class="statusText">规划算力加总</div></el-col
                      >
                    </div></el-col
                  >
                </el-row>
              </div>
            </div>
          </el-col>
          <el-col :span="14">
            <!-- <div class="mapStyle">
              <div class="mapWrapper2">
                <div class="shaoguanArea">
                  <div v-if="type == 1" style="position: relative">
                    <div class="box1"></div>
                    <div class="line1"></div>
                  </div>
                  <div v-if="type == 2">
                    <div class="box2"></div>
                    <div class="line2"></div>
                  </div>
                  <div v-if="type == 3">
                    <div class="box3"></div>
                    <div class="line3"></div>
                  </div>
                  <div class="city1"><div class="name posit1">莞韶城</div></div>
                  <div class="city2">
                    <div class="name posit2" style="min-width:80px">浈江产业园</div>
                  </div>
                  <div class="city3">
                    <div class="name posit3">曲江白土开发区</div>
                  </div>
                  <dv-flyline-chart-enhanced
                    :config="config1"
                    style="width: 100%; height: 100%"
                    class="fly"
                  />
                  <dv-flyline-chart-enhanced
                    :config="config2"
                    style="width: 100%; height: 100%"
                    class="fly"
                  />
                  <dv-flyline-chart-enhanced
                    :config="config3"
                    style="width: 100%; height: 100%"
                    class="fly"
                  />
                </div>
              </div>
              <div>
                <div class="messageWrapper">
                  <div class="titleIcon"></div>
                  <div class="border1"></div>
                  <div class="border2"></div>
                  <div class="border3"></div>
                  <div class="border4"></div>
                  <div>
                    <el-row type="flex" class="row-bg">
                      <el-col :span="24"
                        ><div class="itemName">
                          {{ item.itemName ? item.itemName : "" }}
                        </div></el-col
                      >
                    </el-row>
                  </div>
                  <div>
                    <el-row
                      type="flex"
                      class="row-bg mt-2"
                      justify="space-between"
                    >
                      <el-col :span="16"
                        ><div class="busName">{{ item.busName }}</div></el-col
                      >
                      <el-col :span="8"
                        ><div class="msgStatus1" v-if="status">已投入运营</div>
                        <div class="msgStatus2" v-if="!status">
                          建设中
                        </div></el-col
                      >
                    </el-row>
                  </div>
                  <div>
                    <el-row type="flex" class="row-bg" justify="space-between">
                      <el-col :span="24"
                        ><div>
                          <span class="msgClass1">总机架:</span
                          ><span class="msgClass2">{{
                            item.totalFrame ? item.totalFrame : 0
                          }}</span
                          ><span class="msgClass3">个</span>
                        </div></el-col
                      >
                    </el-row>
                    <el-row type="flex" class="row-bg" justify="space-between">
                      <el-col :span="24"
                        ><div>
                          <span class="msgClass1">智算机架:</span
                          ><span class="msgClass2">{{
                            item.totalCp ? item.totalCp : 0
                          }}</span
                          ><span class="msgClass3">个</span>
                        </div></el-col
                      >
                    </el-row>
                    <el-row type="flex" class="row-bg" justify="space-between">
                      <el-col :span="24"
                        ><div>
                          <span class="msgClass1">算力:</span
                          ><span class="msgClass2">{{
                            item.scale ? item.scale : 0
                          }}</span
                          ><span class="msgClass3">PFlops</span>
                        </div></el-col
                      >
                    </el-row>
                    <el-row type="flex" class="row-bg" justify="space-between">
                      <el-col :span="24"
                        ><div>
                          <span class="msgClass1">所在园区:</span
                          ><span class="msgClass4">{{
                            item.area ? item.area : ""
                          }}</span>
                        </div></el-col
                      >
                    </el-row>
                  </div>
                  <div class="note">
                    <el-row type="flex" class="row-bg" justify="center">
                      <el-col :span="24"
                        ><div class="msgClass5">
                          {{ item.note ? item.note : "" }}
                        </div></el-col
                      >
                    </el-row>
                  </div>
                  <div class="areaIcon"></div>
                </div>
              </div>
            </div> -->
          </el-col> </el-row
      ></el-col>
    </el-row>
  </div>
</template>
<script>
import computeTotal from '@/barComponents/computeTotal'
export default {
  name: 'shaoguanArea',
  props: {},
  components: {
    computeTotal: computeTotal
  },
  watch: {
    mapData: {
      handler (newValue, oldValue) {}
    },
    show: {}
  },
  computed: {},
  data () {
    return {
      arrowConfig: null,
      type: 1,

      config1: {
        points: [
          {
            name: '浈江产业园',
            coordinate: [0.41, 0.4],
            halo: {
              show: true,
              radius: 80,
              color: 'rgb(247, 181, 1)'
            }
          },
          {
            name: '莞韶城',
            coordinate: [0.32, 0.475],
            halo: {
              show: true,
              radius: 80,
              color: 'rgb(247, 181, 1)'
            }
          }
        ],
        lines: [
          {
            source: '浈江产业园',
            target: '莞韶城',
            width: 4,
            color: 'rgb(50, 197, 255)',
            duration: [20.3],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          }
        ],
        k: 8,
        curvature: -10
      },
      config2: {
        points: [
          {
            name: '莞韶城',
            coordinate: [0.32, 0.475],
            halo: {
              show: true,
              radius: 80,
              color: 'rgb(247, 181, 1)'
            }
          },
          {
            name: '曲江白土开发区',
            coordinate: [0.37, 0.545],
            halo: {
              show: true,
              radius: 80,
              color: 'rgb(247, 181, 1)'
            }
          }
        ],
        lines: [
          {
            source: '莞韶城',
            target: '曲江白土开发区',
            width: 4,
            color: 'rgb(50, 197, 255)',
            duration: [20.3],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          }
        ],
        k: 8,
        curvature: 10
      },
      config3: {
        points: [
          {
            name: '曲江白土开发区',
            coordinate: [0.37, 0.545],
            halo: {
              show: true,
              radius: 80,
              color: 'rgb(247, 181, 1)'
            }
          },
          {
            name: '浈江产业园',
            coordinate: [0.41, 0.4],
            halo: {
              show: true,
              radius: 80,
              color: 'rgb(247, 181, 1)'
            }
          }
        ],
        lines: [
          {
            source: '曲江白土开发区',
            target: '浈江产业园',
            width: 4,
            color: 'rgb(50, 197, 255)',
            duration: [20.3],
            orbitColor: 'rgba(255, 255, 255, 0.1)'
          }
        ],
        k: 0.5,
        curvature: 2
      },
      status: true,
      item: {
        itemName: '',
        busName: '',
        totalFrame: 0,
        totalCp: 0,
        scale: 0,
        area: ''
      },

      item1: {
        itemName: '华韶数谷项目',
        busName: '广东华韶数智科技有限公司',
        totalFrame: 10000,
        area: '莞韶城园区',
        note: '总规划875亩，已建设投产运营，目前已完成项目一期的产业用房及数据中心的建设，承载 5kw机柜3000架，二期规划新增高标准数据中心3栋，可承载8KW机柜9000架，目前机房一年用电量1.2亿度电、平均温度25℃左右。'
      },
      item2: {
        itemName: '中国电信粤港澳大湾区一体化数据中心',
        busName: '中国电信',
        totalFrame: 36000,
        totalCp: 2000,
        scale: 457,
        area: '浈江产业园区',
        note: '土地已摘牌、已交付，已取得建设用地规划许可证，已完成土地初勘和详勘，能评已经批复。已完成超前钻、工地内部排水沟、施工临时场地与道路、现场桩基础试桩、地块土方平衡、临时设施施工等工作，累计完成灌注桩施工186根，完成率99.47%（10号地块总数为187根），承台开挖完成56个，完成率29.95%（总数187个），小应变检测（已完成29个），完成率15.51%（总数共187个）。主体工程设计已完成，目前第三方正在开展图纸审查，预计7月初完成图审。'
      },
      item3: {
        itemName: '中国移动粤港澳大湾区一体化数据中心',
        busName: '中国移动',
        totalFrame: 32000,
        area: '浈江产业园区',
        note: '土地已摘牌、已交付,已完成土地详勘，已办理建设用地规划许可证。临时围蔽项目目前已完成砌墙800米（全长1073米），目前正进行围墙抹灰、砌顶。预计7月底发布主体工程招标计划。能评报告已提交省能源局，已通过专家评审会。'
      },
      item4: {
        itemName: '中国联通粤港澳大湾区枢纽数据中心集群',
        busName: '中国联通',
        totalFrame: 28800,
        totalCp: 90,
        scale: 100,
        area: '浈江产业园区',
        note: '土地已摘牌，已办理建设用地规划许可证，能评已经批复,地块详勘已经完成。临时围蔽工程正在开展施工，目前临时围墙已经完成50%工程量。主体建设总包EPC已发布招标公告，原计划7月7日开标，因对投标人提出的疑问进行答疑，6月26日发布顺延开标公告，延期至7月18日开标。'
      },
      item5: {
        itemName: '广东广电粤港澳大湾区枢纽数据中心',
        busName: '广东广电',
        totalFrame: 20000,
        area: '浈江产业园区',
        note: '广电重新选址地块出让规划方案已经高新区审核，广电正按高新区意见进行局部修改，预计6月30日前提交高新区审核。广电希望一次性获得能评，市发改局答复需分期获得，企业正在与省能源局就能耗指标进行沟通，待确定后再提交能评报告。'
      },
      item6: {
        itemName: '鹰硕华南数谷(一期)',
        busName: '鹰硕(韶关)信息产业集团有限公司',
        totalFrame: 30000,
        totalCp: 300,
        scale: 100,
        area: '莞韶城园区',
        note: '能评已经批复，已完成土建，预计7月底完成设备采购，11月底完成设备安装。'
      },
      item7: {
        itemName: '云下科技韶关大数据信息港(一期)',
        busName: '广东云下汇金科技有限公司',
        totalFrame: 22000,
        totalCp: 5500,
        scale: 600,
        area: '浈江产业园区',
        note: '二期红线范围内还涉及3户民房拆迁，其中2户已同意拆除，目前正拆迁中，预计6月底完成；剩余1户不同意拆迁，正在走强拆程序，预计7月20日前完成拆迁。'
      },
      item8: {
        itemName: '万国数据算力集群(一期)',
        busName: '万国数据服务有限公司',
        totalFrame: 51840,
        totalCp: 4160,
        scale: 576,
        area: '曲江白土开发区',
        note: '土地已摘牌、已交付,已完成地勘和地块详细设计。已取得建设用地规划许可证、能评已经批复。目前主体工程超前钻全部完成，已经确定施工总承包单位并签订了施工合同，已取得工程规划许可证，预计7月下旬主体工程动工建设。'
      },
      item9: {
        itemName: '联融粤港澳大湾区算力基础设施(一期)',
        busName: '韶关曲江联融智算数据有限公司',
        totalFrame: 16000,
        totalCp: 2500,
        scale: 800,
        area: '曲江白土开发区',
        note: '能评已批复，已进场开展地块详勘。联想已经退出，目前正在寻找第三方合作者。原规划的地块需要调整，目前正在与曲江区商谈调整地块规划方案。'
      },
      item10: {
        itemName: '中联数据华南总部暨华南算力中心',
        busName: '中联云港数据科技股份有限公司',
        totalFrame: 32000,
        totalCp: 3000,
        scale: 200,
        area: '浈江产业园区',
        note: '已完成拟选地块的详勘、设计单位选定，能评已经批复。预计近期过董事会后进行分期摘地。公司副总裁马超计划下周一前来韶对接项目建设情况。'
      },
      item11: {
        itemName: '首都在线韶关数据智能应用与云计算数据中心',
        busName: '北京首都在线科技股份有限公司',
        totalFrame: 26000,
        totalCp: 3000,
        scale: 630,
        area: '曲江白土开发区',
        note: '由企业收购第三方企业落实项目用地（鹏洲地块），目前已完成新公司注册，近期准备签订最终收购协议、办理过户、立项等相关手续。预计7月20日可完成能评报告编制。'
      },
      item12: {
        itemName: '上海德衡韶关新型金融科技算力中心',
        busName: '上海德衡数据科技有限公司',
        totalFrame: 28800,
        totalCp: 5000,
        scale: 500,
        area: '浈江产业园区',
        note: '曲江地块因无法满足项目建设要求，已重新选址浈江产业园，6月28日可完成地块出让规划方案编制并提交高新区审核。预计7月上旬完成能评报告编制。'
      },
      item13: {
        itemName: '中瀚云韶关数据中心',
        busName: '中瀚云科技股份有限公司',
        area: '浈江产业园区',
        totalFrame: 38000,
        note: '土地已摘牌、已交付，独立法人公司已注册，已取得备案和用地规划许可证、工程规划许可证、施工许可证和能评批复，已完成详勘。围蔽工作已完成，采购物料已进场，目前临电项目和临建办公区域已开始施工建设。'
      },
      arrow: null
    }
  },
  created () {
    this.item = this.item1
    this.arrowConfig = this.config4
    this.type = 1
  },
  mounted () {},
  methods: {
    changeItem (val, item) {
      if (item.area == '莞韶城园区') {
        this.arrowConfig = this.config4
        this.type = 1
      } else if (item.area == '曲江白土开发区') {
        this.arrowConfig = this.config5
        this.type = 2
      } else {
        this.arrowConfig = this.config6
        this.type = 3
      }
      if (val == 1) {
        this.status = true
      } else {
        this.status = false
      }
      switch (val) {
        case 1:
          this.status = true
          this.item = this.item1
          break
        case 2:
          this.item = this.item2
          break
        case 3:
          this.item = this.item3
          break
        case 4:
          this.item = this.item4
          break
        case 5:
          this.item = this.item5
          break
        case 6:
          this.item = this.item6
          break
        case 7:
          this.item = this.item7
          break
        case 8:
          this.item = this.item8
          break
        case 9:
          this.item = this.item9
          break
        case 10:
          this.item = this.item10
          break
        case 11:
          this.item = this.item11
          break
        case 12:
          this.item = this.item12
          break
        case 13:
          this.item = this.item13
          break
        default:
          this.item = this.item1
      }
    }
  }
}
</script>
<style scoped>
.shaoguanArea {
  background-image: url("../barStatic/screen1/shaoguan-3d.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  margin-top: 40px;
  height: 800px;
  position: relative;
}
.mapWrapper1 {
  display: inline-block;
  position: relative;
  left: 120px;
  top: 160px;
}
.mapWrapper2 {
  width: 800px;
  height: 950px;
  display: inline-block;
}
.name {
  color: rgb(255, 211, 0);
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: 900;
  letter-spacing: 0px;
  text-align: center;
  padding: 5px 15px 5px 15px;
  background: linear-gradient(
    180deg,
    rgba(5, 16, 67, 0.78),
    rgba(26, 22, 26, 0) 100%
  );
}
.city1 {
  width: 80px;
  height: 80px;
  background-image: url("../barStatic/screen1/icon-idc.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 215px;
  top: 300px;
  z-index: 10;
}
.city2 {
  width: 80px;
  height: 80px;
  background-image: url("../barStatic/screen1/icon-idc.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 286px;
  top: 247px;
  z-index: 10;
}
.city3 {
  width: 80px;
  height: 80px;
  background-image: url("../barStatic/screen1/icon-idc.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 256px;
  top: 358px;
  z-index: 10;
}
.posit1 {
  position: absolute;
  top: 30px;
  left: -60px;
  z-index: 100;
}
.posit2 {
  position: absolute;
  top: 40px;
  left: 60px;
  z-index: 100;
}
.posit3 {
  position: absolute;
  top: 85px;
  left: -44px;
  width: 115px;
  z-index: 100;
}
.fly {
  position: absolute;
}
.green {
  color: rgb(68, 215, 182);
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 0px;
  margin-bottom: 15px;
  text-align: right;
  margin-right: 12px;
}
.cursor {
  cursor: pointer;
}
.blue {
  color: rgb(50, 197, 255);
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 0px;
  margin-bottom: 15px;
  text-align: right;
  margin-right: 12px;
}
.arrow1 {
  width: 80px;
  height: 13px;
  background: linear-gradient(
    270deg,
    rgb(68, 215, 182),
    rgba(22, 93, 255, 0) 100%
  );
  display: inline-block;
}
.arrow2 {
  height: 10px;
  background: linear-gradient(
    270deg,
    rgb(50, 197, 255),
    rgba(22, 93, 255, 0) 100%
  );
  display: inline-block;
}
.unitWrapper {
  font-size: 16px;
  font-weight: 400;
  line-height: 12px;
  height: 12px;
  display: inline-block;
  margin-left: 12px;
}
.computeWrapper {
  font-size: 16px;
  font-weight: 400;
  line-height: 12px;
  height: 12px;
  display: inline-block;
  margin-bottom: 12px;
  position: relative;
}
.totalNum1 {
  color: rgb(68, 215, 182);
  font-weight: 600;
}
.totalUnit {
  color: rgb(222, 222, 222);
  margin-left: 5px;
  font-size: 12px;
}
.totalNum2 {
  color: rgb(50, 197, 255);
  font-weight: 600;
}
.statusText {
  color: rgb(255, 255, 255);
  font-size: 16px;
  font-weight: 400;
  display: inline-block;
  margin-left: 5px;
}
.status1 {
  width: 30px;
  height: 12px;
  background: rgb(50, 197, 255);
  display: inline-block;
}
.status2 {
  width: 30px;
  height: 12px;
  background: rgb(80, 205, 137);
  display: inline-block;
}
.status3 {
  width: 30px;
  height: 12px;
  display: inline-block;
  background: linear-gradient(
    270deg,
    rgb(50, 197, 255),
    rgba(22, 93, 255, 0) 100%
  );
}
.status4 {
  width: 30px;
  height: 12px;
  display: inline-block;
  background: linear-gradient(
    270deg,
    rgb(182, 32, 224),
    rgba(22, 93, 255, 0) 100%
  );
}
.status5 {
  width: 12px;
  height: 12px;
  display: inline-block;
  box-sizing: border-box;
  background: rgb(182, 32, 224);
  border: 2px solid rgb(255, 255, 255);
  border-radius: 50%;
}
.iconWrapper {
  position: relative;
  width: 800px;
  left: 90px;
  top: 50px;
}
.smallArrow2 {
  background: linear-gradient(
    270deg,
    rgb(182, 32, 224),
    rgba(22, 93, 255, 0) 100%
  );
  height: 10px;
}
.cpWrapper {
  position: absolute;
  top: 2px;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  font-weight: 600;
}
.cpNum {
  color: rgb(247, 181, 1);
}
.cpUnit {
  color: rgb(222, 222, 222);
  font-weight: 400;
  font-size: 12px;
  margin-left: 5px;
}
.messageWrapper {
  box-sizing: border-box;
  background: rgba(8, 3, 51, 0.85);
  border: 2px solid rgb(23, 71, 151);
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.6);
  min-height: 325px;
  width: 420px;
  position: absolute;
  top: 100px;
  right: 20px;
  padding: 5px 25px 25px 25px;
}
.border1 {
  /* 矩形 4 */
  width: 20px;
  height: 20px;
  border-left: 2px solid rgb(185, 225, 255);
  border-top: 2px solid rgb(185, 225, 255);
  position: absolute;
  top: 0px;
  left: 0px;
}
.border2 {
  width: 20px;
  height: 20px;
  border-top: 2px solid rgb(185, 225, 255);
  border-right: 2px solid rgb(185, 225, 255);
  position: absolute;
  right: 0px;
  top: 0px;
}
.border3 {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 0px;
  bottom: -1px;
  border-left: 2px solid rgb(185, 225, 255);
  border-bottom: 2px solid rgb(185, 225, 255);
}
.border4 {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 0px;
  bottom: -1px;
  border-right: 2px solid rgb(185, 225, 255);
  border-bottom: 2px solid rgb(185, 225, 255);
}
.itemName {
  color: rgb(255, 255, 255);
  font-size: 18px;
  font-weight: 900;
  letter-spacing: 0px;
}
.busName {
  color: rgb(50, 197, 255);
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: 500;
}
.mt-2 {
  margin-top: 16px;
}
.msgStatus1 {
  background: rgba(68, 215, 182, 0.3);
  border: 1px solid rgb(68, 215, 182);
  color: rgb(109, 212, 1);
  text-align: center;
  float: right;
  font-size: 14px;
  padding: 2px 12px 2px 12px;
  font-weight: 700;
  position: relative;
  top: -5px;
}
.msgStatus2 {
  background: rgba(50, 197, 255, 0.3);
  border: 1px solid rgb(50, 197, 255);
  color: rgb(50, 197, 255);
  text-align: center;
  float: right;
  font-size: 14px;
  padding: 2px 12px 2px 12px;
  font-weight: 700;
  position: relative;
  top: -5px;
}
.msgClass1 {
  color: rgb(222, 222, 222);
  font-size: 16px;
}
.msgClass2 {
  margin-left: 5px;
  color: rgb(247, 181, 1);
  font-size: 16px;
  font-weight: 800;
  margin-right: 5px;
}
.msgClass3 {
  color: rgb(222, 222, 222);
  font-size: 16px;
}
.msgClass4 {
  color: rgb(222, 222, 222);
  font-size: 16px;
  margin-left: 5px;
}
.msgClass5 {
  color: rgb(222, 222, 222);
  font-size: 16px;
  font-weight: 900;
  margin-top: 5px;
  line-height: 25px;
}
.areaIcon {
  width: 100px;
  height: 100px;
  background-image: url("../barStatic/screen1/idc-bg.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
  right: 40px;
  bottom: 5px;
  z-index: 0;
}
.arrowMap {
  width: 500px;
  height: 500px;
  position: absolute;
  top: 100px;
  right: 500px;
}
.min-width {
  min-width: 1800px;
}
.mapStyle {
  width: 1000px;
  position: relative;
}
.box1 {
  margin-top: 50px;
  width: 15px;
  height: 15px;
  border-top: 5px solid rgb(247, 181, 1);
  border-right: 5px solid rgb(247, 181, 1);
  transform: rotate(196deg);
  position: absolute;
  top: 308px;
  left: 248px;
  z-index: 100;
}
.line1 {
  width: 462px;
  height: 6px;
  background: linear-gradient(
    to right,
    rgb(247, 181, 1),
    rgba(50, 197, 255, 0)
  );
  position: absolute;
  transform: rotate(-40deg);
  right: 140px;
  top: 222px;
  z-index: 10;
}
.box2 {
  margin-top: 50px;
  width: 15px;
  height: 15px;
  border-top: 5px solid rgb(247, 181, 1);
  border-right: 5px solid rgb(247, 181, 1);
  transform: rotate(196deg);
  position: absolute;
  top: 369px;
  left: 294px;
  z-index: 100;
}
.line2 {
  width: 402px;
  height: 6px;
  background: linear-gradient(
    to right,
    rgb(247, 181, 1),
    rgba(50, 197, 255, 0)
  );
  position: absolute;
  transform: rotate(-40deg);
  right: 147px;
  top: 302px;
  z-index: 10;
}
.box3 {
  margin-top: 50px;
  width: 15px;
  height: 15px;
  border-top: 5px solid rgb(247, 181, 1);
  border-right: 5px solid rgb(247, 181, 1);
  transform: rotate(196deg);
  position: absolute;
  top: 259px;
  left: 323px;
}
.line3 {
  width: 345px;
  height: 6px;
  background: linear-gradient(
    to right,
    rgb(247, 181, 1),
    rgba(50, 197, 255, 0)
  );
  position: absolute;
  transform: rotate(-40deg);
  right: 170px;
  top: 210px;
  z-index: 10;
}
.ComputeName {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: left;
}
.ComputeTotal {
  color: rgb(182, 32, 224);
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: 800;
  letter-spacing: 0px;
  text-align: left;
}
.ComputeUnit {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: left;
}
.computeTotal {
  position: absolute;
  top: -30px;
  left: 312px;
  z-index: 0;
}
.unitPos {
  position: absolute;
  left: 60px;
  top: 22px;
  z-index: 100;
}
.note {
  z-index: 100;
  position: relative;
}
.titleIcon {
  background-image: url("../barStatic/screen1/titleIcon.svg");
  background-size: 100%;
  background-repeat: no-repeat;
  height: 15px;
  width: 80%;
  margin: 0 auto;
}
.itemContent {
  position: relative;
  z-index: 100;
}
</style>
