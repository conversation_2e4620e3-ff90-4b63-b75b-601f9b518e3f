/**
 * API请求助手函数
 */
import { Message, Loading } from 'element-ui'

/**
 * 带加载提示的通用API请求处理
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 * @param {String} options.loadingText - 加载提示文字
 * @param {String} options.successMessage - 成功提示文字
 * @param {String} options.errorMessage - 错误提示文字
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @param {Function} options.onComplete - 完成回调(无论成功失败)
 * @returns {Promise} 请求Promise
 */
export function handleApiRequest (apiCall, options = {}) {
  const {
    loadingText = '加载中...',
    successMessage,
    errorMessage = '操作失败，请稍后重试',
    onSuccess,
    onError,
    onComplete
  } = options

  // 显示加载中指示器
  const loadingInstance = Loading.service({
    lock: true,
    text: loadingText,
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  return apiCall()
    .then(response => {
      // 显示成功消息（如果提供）
      if (successMessage) {
        Message.success(successMessage)
      }

      // 调用成功回调（如果提供）
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess(response)
      }

      return response
    })
    .catch(error => {
      console.error('API请求失败:', error)

      // 显示错误消息
      Message.error(errorMessage)

      // 调用错误回调（如果提供）
      if (onError && typeof onError === 'function') {
        onError(error)
      }

      return Promise.reject(error)
    })
    .finally(() => {
      // 关闭加载指示器
      loadingInstance.close()

      // 调用完成回调（如果提供）
      if (onComplete && typeof onComplete === 'function') {
        onComplete()
      }
    })
}

/**
 * 显示确认对话框
 * @param {Object} vm - Vue实例
 * @param {Object} options - 配置选项
 * @returns {Promise} - 确认Promise
 */
export function showConfirmDialog (vm, options = {}) {
  const {
    title = '提示',
    message = '确认执行此操作?',
    type = 'warning',
    confirmButtonText = '确定',
    cancelButtonText = '取消',
    customClass = 'confirm-dialog'
  } = options

  return vm.$confirm(message, title, {
    confirmButtonText,
    cancelButtonText,
    type,
    customClass
  })
}
