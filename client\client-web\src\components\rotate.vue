<template>
    <div id="13714" class="__easyv-component"
        style="position: absolute; pointer-events: auto; left: 470px; top:280px;height:250px">
        <div class="rotateTimer">
            <img class="index-module_rotateClockwise_3Qdra index-module_stretch_1bcJi"
                src="../assets/rotate.png"
                style="border-radius: unset; cursor: pointer; pointer-events: auto">
        </div>
    </div>
</template>
<script>
export default {
  data () {
    return {
      timer: null
    }
  },
  created () {

  }

}

</script>
<style>
    .rotateTimer {
        animation: rotate 4s ease infinite;
    }

    @keyframes rotate {
        from {
            opacity: 0.5;
            transform: perspective(3000px) rotateX(100deg) rotateY(0deg) rotateZ(0deg) scaleX(1) scaleY(1) translate3d(0px, 0px, 0px);
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 1100px;
            height: 1100px;
        }

        to {
            opacity: 0.5;
            transform: perspective(3000px) rotateX(100deg) rotateY(0deg) rotateZ(360deg) scaleX(1) scaleY(1) translate3d(0px, 0px, 0px);
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 1100px;
            height: 1100px;
        }
    }
</style>
