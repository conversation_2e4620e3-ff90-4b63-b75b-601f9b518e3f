<template>
  <div class="task-statistics">
    <statistics-tab
      v-model="taskType"
      :disabled="disabled"
      @change="handleTabChange"
    ></statistics-tab>
    <bar-chart-wrapper
      :chart-data="taskData"
      :chart-type="taskType"
      :task-info="taskInfo"
      :height="height"
    ></bar-chart-wrapper>
  </div>
</template>

<script>
import StatisticsTab from './StatisticsTab.vue'
import BarChartWrapper from './BarChartWrapper.vue'

/**
 * 任务统计组件 - 整合统计选项卡和柱状图
 */
export default {
  name: 'TaskStatistics',
  components: {
    StatisticsTab,
    BarChartWrapper
  },
  props: {
    /**
     * 提交任务数据
     */
    submitJobData: {
      type: Object,
      default: () => ({
        xData: [],
        used: [],
        used2: []
      })
    },
    /**
     * 等待任务数据
     */
    pendingJobData: {
      type: Object,
      default: () => ({
        xData: [],
        used: [],
        used2: []
      })
    },
    /**
     * 资源使用数据
     */
    resourceUsageData: {
      type: Object,
      default: () => ({
        xData: [],
        used: [],
        used2: []
      })
    },
    /**
     * 当前任务索引
     */
    currentIndex: {
      type: Number,
      default: 0
    },
    /**
     * 任务信息
     */
    taskInfo: {
      type: Object,
      default: () => ({
        taskId: 0,
        compareId: 0,
        strategy1: '',
        strategy2: ''
      })
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false
    },
    /**
     * 图表高度
     */
    height: {
      type: Number,
      default: 700
    }
  },
  data () {
    return {
      taskType: 1,
      taskData: {
        xData: [],
        used: [],
        used2: []
      }
    }
  },
  watch: {
    // 监听数据变化，更新图表数据
    submitJobData: {
      handler () {
        this.updateChartData()
      },
      deep: true
    },
    pendingJobData: {
      handler () {
        this.updateChartData()
      },
      deep: true
    },
    resourceUsageData: {
      handler () {
        this.updateChartData()
      },
      deep: true
    },
    currentIndex () {
      this.updateChartData()
    }
  },
  created () {
    this.updateChartData()
  },
  methods: {
    /**
     * 处理选项卡变化
     */
    handleTabChange (value) {
      this.taskType = value
      this.updateChartData()
    },

    /**
     * 更新图表数据
     */
    updateChartData () {
      const index = this.currentIndex

      // 根据任务类型更新图表数据
      if (this.taskType === 1) {
        this.setTaskData(
          this.submitJobData.xData,
          index < this.submitJobData.used.length ? this.submitJobData.used[index] : [],
          this.submitJobData.used2 && index < this.submitJobData.used2.length ? this.submitJobData.used2[index] : []
        )
      } else if (this.taskType === 2) {
        this.setTaskData(
          this.pendingJobData.xData,
          index < this.pendingJobData.used.length ? this.pendingJobData.used[index] : [],
          this.pendingJobData.used2 && index < this.pendingJobData.used2.length ? this.pendingJobData.used2[index] : []
        )
      } else if (this.taskType === 3) {
        this.setTaskData(
          this.resourceUsageData.xData,
          index < this.resourceUsageData.used.length ? this.resourceUsageData.used[index] : [],
          this.resourceUsageData.used2 && index < this.resourceUsageData.used2.length ? this.resourceUsageData.used2[index] : []
        )
      }
    },

    /**
     * 设置任务数据
     */
    setTaskData (xData, used, used2) {
      this.taskData = {
        xData: xData || [],
        used: used || [],
        used2: used2 || []
      }
    }
  }
}
</script>

<style scoped>
.task-statistics {
  width: 100%;
  height: 100%;
}
</style>
