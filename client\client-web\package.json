{"name": "1.0", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@jiaminghi/c-render": "^0.4.3", "@jiaminghi/data-view": "^2.10.0", "@popperjs/core": "^2.11.8", "animejs": "^3.2.2", "ant-design-vue": "^1.7.8", "axios": "^0.26.1", "bootstrap": "^5.3.2", "core-js": "^3.6.5", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "element-ui": "^2.15.6", "highcharts": "^11.0.1", "highcharts-vue": "^1.4.2", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.29.4", "nprogress": "^0.2.0", "popper.js": "^1.16.1", "register-service-worker": "^1.7.1", "screenfull": "^6.0.2", "vue": "^2.6.11", "vue-count-to": "^1.0.13", "vue-router": "^2.8.1", "vue-worker": "^1.2.1", "vuex": "^3.6.2", "worker-loader": "^3.0.8"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.12", "@vue/cli-plugin-eslint": "~4.5.12", "@vue/cli-plugin-pwa": "~4.5.12", "@vue/cli-plugin-router": "~4.5.12", "@vue/cli-plugin-vuex": "~4.5.12", "@vue/cli-service": "~4.5.12", "@vue/eslint-config-standard": "^5.1.2", "axios-retry": "^3.6.0", "babel-eslint": "^10.1.0", "default-passive-events": "^2.0.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "sass": "^1.86.3", "sass-loader": "^10.2.0", "vue-loader": "^15.0.0", "vue-template-compiler": "^2.6.11", "webpack-dev-server": "^4.8.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}