/**
 * 图表服务类 - 封装图表配置生成逻辑
 */
export class ChartService {
  /**
   * 生成折线图配置
   * @param {Object} options - 配置选项
   * @returns {Object} ECharts配置对象
   */
  static createLineChartConfig (options = {}) {
    const {
      xAxisData = [],
      yAxisData = [],
      hasData = false,
      title = '暂无数据'
    } = options

    return {
      title: {
        text: hasData ? '' : title,
        textStyle: { color: '#fff', fontSize: 14 },
        right: '40%',
        top: '30%'
      },
      color: '#32c5ff',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            type: 'solid',
            color: 'rgba(0, 0, 0, 0)'
          }
        },
        formatter: function (params) {
          if (!params || params.length === 0) return ''
          const param = params[0]
          return `
            <div>
              <div>${param.axisValue}h</div>
              <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:#1890ff;"></span>
              ${param.seriesName}: ${param.value}
            </div>
          `
        },
        backgroundColor: '#000033',
        textStyle: { color: '#fff' },
        borderWidth: 0
      },
      grid: {
        right: '5%',
        bottom: '25%',
        top: '12%',
        left: '10%'
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLine: {
          show: true,
          lineStyle: { color: 'rgba(1, 145, 255, 0.3)' }
        },
        axisLabel: {
          show: true,
          color: 'rgba(255, 255, 255,1)',
          formatter: value => value + 'h'
        },
        axisTick: { show: false }
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            type: 'dotted',
            color: 'rgba(1, 145, 255, 0.3)'
          }
        },
        axisLine: {
          lineStyle: { color: 'rgba(255, 255, 255,0.9)' }
        },
        axisLabel: {
          show: true,
          color: 'rgba(255, 255, 255,0.9)'
        },
        scale: true,
        min: 0,
        splitNumber: 4
      },
      series: [{
        name: '提交任务量',
        type: 'line',
        symbol: 'circle',
        symbolSize: 6,
        smooth: true,
        data: yAxisData,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(50,197,255,0)' },
              { offset: 1, color: 'rgb(22,93,255)' }
            ]
          }
        }
      }],
      animation: true,
      animationDuration: idx => idx * 500
    }
  }

  /**
   * 生成柱状图配置
   * @param {Object} options - 配置选项
   * @returns {Object} ECharts配置对象
   */
  static createBarChartConfig (options = {}) {
    const {
      xData = [],
      yData = [],
      compareData = [],
      hasData = false,
      title = '暂无数据',
      chartType = 1, // 1: 提交任务量, 2: 平均等待时长, 3: 资源利用率
      mainTaskName = '',
      compareTaskName = ''
    } = options

    // 数据格式化函数
    const formatter = (param) => {
      switch (chartType) {
        case 1:
          return param.data || 0
        case 2:
          return Math.floor((param.data || 0) * 3600 / 3600) + 'h'
        case 3:
          return (param.data || 0).toFixed(2) + '%'
        default:
          return param.data || 0
      }
    }

    const series = []

    // 主任务数据系列
    if (hasData) {
      series.push({
        name: mainTaskName,
        type: 'bar',
        barWidth: 12,
        data: yData,
        label: {
          show: true,
          position: 'right',
          textStyle: { color: 'rgba(255, 255, 255, 0.95)', fontSize: 11 },
          formatter
        },
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: 'rgba(0, 150, 255, 0.7)' },
              { offset: 1, color: 'rgba(0, 220, 255, 1)' }
            ]
          },
          borderRadius: [0, 5, 5, 0]
        }
      })

      // 对比任务数据系列
      if (compareData && compareData.length > 0) {
        series.push({
          name: compareTaskName,
          type: 'bar',
          barWidth: 12,
          data: compareData,
          label: {
            show: true,
            position: 'right',
            textStyle: { color: 'rgba(255, 255, 255, 0.95)', fontSize: 11 },
            formatter
          },
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: 'rgba(80, 100, 200, 0.7)' },
                { offset: 1, color: 'rgba(130, 150, 250, 1)' }
              ]
            },
            borderRadius: [0, 5, 5, 0]
          }
        })
      }
    }

    return {
      darkMode: true,
      title: {
        text: hasData ? '' : title,
        textStyle: { color: 'rgba(255, 255, 255, 0.6)', fontSize: 14 },
        left: 'center',
        top: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 25, 50, 0.9)',
        borderColor: 'rgba(0, 190, 255, 0.7)',
        textStyle: { color: 'rgba(255, 255, 255, 0.95)' }
      },
      legend: {
        show: hasData && series.length > 1,
        top: '4%',
        right: '5%',
        textStyle: { color: 'rgba(255, 255, 255, 0.9)' }
      },
      grid: {
        left: '3%',
        right: '5%',
        top: '10%',
        bottom: '8%',
        containLabel: true
      },
      yAxis: {
        type: 'category',
        data: xData,
        show: hasData,
        axisLine: {
          show: hasData,
          lineStyle: { color: 'rgba(0, 190, 255, 0.4)' }
        },
        axisLabel: {
          show: hasData,
          textStyle: { color: 'rgba(220, 220, 220, 0.95)', fontSize: 12 }
        }
      },
      xAxis: {
        type: 'value',
        show: hasData,
        axisLine: {
          show: hasData,
          lineStyle: { color: 'rgba(0, 190, 255, 0.4)' }
        },
        axisLabel: {
          show: hasData,
          textStyle: { color: 'rgba(220, 220, 220, 0.85)', fontSize: 11 }
        },
        scale: true,
        min: 0
      },
      series,
      animation: true,
      animationDurationUpdate: 300
    }
  }

  /**
   * 生成环形图配置
   * @param {Object} options - 配置选项
   * @returns {Object} ECharts配置对象
   */
  static createPieChartConfig (options = {}) {
    const {
      totalNum = 0,
      execNum = 0,
      hasData = false
    } = options

    const percentage = totalNum > 0 ? Math.round((execNum / totalNum) * 100) : 0

    return {
      title: {
        text: hasData ? '' : '暂无数据',
        textStyle: { color: '#fff', fontSize: 14 },
        left: 'center',
        top: 'center'
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: hasData ? [
          {
            value: execNum,
            name: '已执行',
            itemStyle: { color: '#00d4ff' }
          },
          {
            value: totalNum - execNum,
            name: '未执行',
            itemStyle: { color: 'rgba(255, 255, 255, 0.1)' }
          }
        ] : [],
        label: {
          show: hasData,
          position: 'center',
          formatter: `{a|${execNum}}\n{b|已执行 ${percentage}%}`,
          rich: {
            a: {
              fontSize: 24,
              color: '#fff',
              fontWeight: 'bold'
            },
            b: {
              fontSize: 12,
              color: '#ccc',
              padding: [5, 0, 0, 0]
            }
          }
        }
      }]
    }
  }

  /**
   * 图表类型配置
   */
  static CHART_TYPES = {
    SUBMIT_JOB: 1,
    PENDING_TIME: 2,
    RESOURCE_USAGE: 3
  }

  /**
   * 获取图表类型名称
   * @param {number} type - 图表类型
   * @returns {string} 类型名称
   */
  static getChartTypeName (type) {
    const typeNames = {
      [this.CHART_TYPES.SUBMIT_JOB]: '提交任务量',
      [this.CHART_TYPES.PENDING_TIME]: '任务平均等待时长',
      [this.CHART_TYPES.RESOURCE_USAGE]: '资源利用率对比'
    }
    return typeNames[type] || '未知类型'
  }
}
