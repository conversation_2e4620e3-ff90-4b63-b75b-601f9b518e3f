<template>
  <div id="earth" ref="echart"></div>
</template>
<script>
import * as echarts from 'echarts'
import China from './js/china.json'
import { getAicenter, getTrainJob } from '@/api/screenService.js'
const pOp = {
  geo: {
    map: 'china',
    label: {
      fontSize: 20
    },
    itemStyle: {
      borderColor: '#000d2d',
      areaColor: 'transparent',
      borderColor: 'rgba(0,253,255,0.3)',
      borderWidth: 1
      // emphasis: {
      //   areaColor: "#000d2d",
      // },
    },
    emphasis: {
      areaColor: 'transparent'
    },
    regions: [
      {
        name: '南海诸岛',
        value: 0,
        itemStyle: {
          opacity: 0.5,
          label: {
            show: true
          }
        }
      }
    ],
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    silent: true, // 图形是否不响应和触发鼠标事件
    boundingCoords: [
      [-180, 90],
      [180, -90]
    ]
  },
  series: [
    // 涟漪
    {
      type: 'effectScatter',
      coordinateSystem: 'geo',
      // zlevel: 1,
      rippleEffect: {
        brushType: 'stroke',
        scale: 3.5
      },
      // effectType: 'ripple',
      label: {
        show: false,
        position: 'right',
        fontSize: 18,
        formatter: (params) => {
          return ''
        }
      },
      itemStyle: {
        // color: '#FFDC00'
        color: function (params) {
          var colorList = [
            new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              {
                offset: 0,
                color: '#64fbc5'
              },
              {
                offset: 1,
                color: '#018ace'
              }
            ]),
            new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              {
                offset: 0,
                color: '#64fbc5'
              },
              {
                offset: 1,
                color: '#018ace'
              }
            ]),
            new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              {
                offset: 0,
                color: '#168e6d'
              },
              {
                offset: 1,
                color: '#c78d7b'
              }
            ]),
            new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              {
                offset: 0,
                color: '#61c0f1'
              },
              {
                offset: 1,
                color: '#6f2eb6'
              }
            ]),
            new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              {
                offset: 0,
                color: '#168e6d'
              },
              {
                offset: 1,
                color: '#c78d7b'
              }
            ]),
            new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              {
                offset: 0,
                color: '#61c0f1'
              },
              {
                offset: 1,
                color: '#6f2eb6'
              }
            ])
          ]
          return colorList[params.dataIndex]
        }
      },
      symbol: 'circle',
      data: [],
      symbolSize: [15, 15]
    }
    // 接入中心飞线图
    // {
    //   type: "lines",
    //   coordinateSystem: "geo",
    //   // zlevel: 1,
    //   effect: {
    //     show: true,
    //     period: 4, //箭头指向速度，值越小速度越快
    //     trailLength: 0.4, //特效尾迹长度[0,1]值越大，尾迹越长重
    //     symbol: "none", //箭头图标
    //     symbolSize: 7, //图标大小
    //   },
    //   lineStyle: {
    //     normal: {
    //       color: "#fff",
    //       width: 3, //线条宽度
    //       opacity: 0.1, //尾迹线条透明度
    //       curveness: 0.3, //尾迹线条曲直度
    //     },
    //   },
    //   data: [],
    // },
  ]
}
let mapChart
let canvas
// let canvas2;
// let mapChart2;
const pOp2 = {
  geo: {
    map: 'china',
    label: {
      fontSize: 20
    },
    itemStyle: {
      // borderColor: "#000d2d",
      areaColor: 'transparent',
      borderColor: 'rgba(0,253,255,0.3)',
      borderWidth: 1
      // emphasis: {
      //   areaColor: "#000d2d",
      // },
    },
    emphasis: {
      areaColor: 'transparent'
    },
    regions: [
      {
        name: '南海诸岛',
        value: 0,
        itemStyle: {
          opacity: 0.5,
          label: {
            show: true
          }
        }
      }
    ],
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    silent: true, // 图形是否不响应和触发鼠标事件
    boundingCoords: [
      [-180, 90],
      [180, -90]
    ]
  },
  series: [
    {
      type: 'lines',
      coordinateSystem: 'geo',
      // zlevel: 1,
      effect: {
        show: true,
        period: 4, // 箭头指向速度，值越小速度越快
        trailLength: 0.4, // 特效尾迹长度[0,1]值越大，尾迹越长重
        symbol: 'none', // 箭头图标
        symbolSize: 7 // 图标大小
      },
      lineStyle: {
        normal: {
          color: '#fff',
          width: 3, // 线条宽度
          opacity: 0.05, // 尾迹线条透明度
          curveness: -0.15 // 尾迹线条曲直度
        }
      },
      data: [
        // 成都
        {
          coords: [
            [113.88, 22.54],
            [104.07, 30.57]
          ],
          lineStyle: { color: '#00FFB0', width: 3, type: 'dotted' }
        },
        // 银川
        {
          coords: [
            [113.88, 22.54],
            [106.23, 38.49]
          ],
          lineStyle: { color: '#00FFB0', width: 3, type: 'dotted' }
        },
        // 北京
        {
          coords: [
            [113.88, 22.54],
            [116.41, 39.91]
          ],
          lineStyle: { color: '#00FFB0', width: 3, type: 'dotted' }
        },
        // 宁波
        {
          coords: [
            [113.88, 22.54],
            [121.63, 29.86]
          ],
          lineStyle: { color: '#99ff99', width: 3, type: 'dotted' }
        },
        // 光纤
        {
          coords: [
            [113.6, 24.81],
            [106.63, 26.65]
          ],
          lineStyle: { color: '#eb95fa', width: 3 }
        }
      ]
    },
    {
      type: 'lines',
      coordinateSystem: 'geo',
      // zlevel: 1,
      effect: {
        show: true,
        period: 10, // 箭头指向速度，值越小速度越快
        trailLength: 0.4, // 特效尾迹长度[0,1]值越大，尾迹越长重
        // symbol: "arrow", //箭头图标
        symbolSize: 14 // 图标大小
      },
      lineStyle: {
        color: '#FFDC00',
        width: 8, // 线条宽度
        opacity: 0.1, // 尾迹线条透明度
        curveness: -0.15 // 尾迹线条曲直度
      },
      data: [
        // //10TB全光网络互联
        // {
        //   coords: [
        //     [114.06, 22.54],
        //     [113.27, 23.13],
        //   ],
        //   lineStyle: { color: "#fff" },
        // },
        // SD-WAN互联
        {
          coords: [
            [114.06, 22.54],
            [117.12, 36.65]
          ],
          lineStyle: { color: '#00c7ff' }
        },

        // MPLS互联
        {
          coords: [
            [114.06, 22.54],
            [118.05, 31.59]
          ],
          lineStyle: { color: '#ff7272' }
        }
      ]
    },
    {
      type: 'effectScatter',
      coordinateSystem: 'geo',
      // zlevel: 1,
      rippleEffect: {
        brushType: 'stroke'
      },
      // effectType: 'ripple',
      label: {
        show: false,
        position: 'right',
        fontSize: 18,
        formatter: (params) => {
          return params.name
        },
        fontWeight: 'bolder'
      },
      symbol: 'circle',
      symbolSize: 16,
      data: [
        {
          name: '鹏城云脑',
          value: [113.88, 22.54],
          itemStyle: {
            color: 'yellow'
          },
          label: {
            color: 'yellow'
          }
        },
        {
          name: '大湾区枢纽集群(韶关)',
          value: [113.6, 24.68],
          itemStyle: {
            color: '#eb95fa'
          },
          label: {
            color: '#eb95fa'
          }
        },
        {
          name: '中科类脑',
          value: [118.05, 31.59],
          itemStyle: {
            color: '#ff7272'
          },
          label: {
            color: '#ff7272'
          }
        },
        {
          name: '济南超算',
          value: [117.12, 36.65],
          itemStyle: {
            color: '#00c7ff'
          },
          label: {
            color: '#00c7ff'
          }
        }
      ]
    },
    {
      type: 'scatter',
      coordinateSystem: 'geo',
      // zlevel: 1,
      rippleEffect: {
        brushType: 'stroke'
      },
      // effectType: 'ripple',
      label: {
        show: false,
        position: 'right',
        fontSize: 18,
        formatter: (params) => {
          return ''
        },
        fontWeight: 'bolder'
      },
      symbol: 'circle',
      symbolSize: 14,
      // itemStyle: {
      //   color: "rgba(255,255,255,1)", //点颜色
      //   borderColor: "rgba(255,255,255,1)", //点边框颜色
      //   opacity: 1, //点的透明度 1不透明
      //   borderWidth: 1,
      // },
      data: [
        {
          value: [106.63, 26.65, 0],
          itemStyle: { color: '#fff', opacity: 1 }
        },
        {
          value: [104.07, 30.57, 0],
          itemStyle: { color: '#fff', opacity: 1 }
        },
        {
          value: [106.23, 38.49, 0],
          itemStyle: { color: '#fff', opacity: 1 }
        },
        {
          value: [121.63, 29.86, 0],
          itemStyle: { color: '#fff', opacity: 1 }
        },
        {
          value: [116.41, 39.01, 0],
          itemStyle: { color: '#fff', opacity: 1 }
        }
      ]
    }
  ]
}
const symbolImg = 'image://' + require('@/assets/iconidc.svg')
let myChart
const barBaseData = [
  // 设置空的一个最小值,最大值
  {
    name: '算力网调度任务',
    taskName: 'min',
    value: [0, 0, 0],
    itemStyle: {
      opacity: 0
    }
  },
  {
    name: '算力网调度任务',
    taskName: 'max',
    value: [0, 0, 1000],
    itemStyle: {
      opacity: 0
    }
  }
]
const option = {
  backgroundColor: '',
  tooltip: {
    show: true,
    // extraCssText: "min-height:180px",
    trigger: 'item', // 触发类型
    triggerOn: 'click',
    backgroundColor: 'rgba(4,4,12,0.7)', // 提示框浮层的背景颜色
    borderColor: '#333', // 提示框浮层的边框颜色
    borderWidth: 1,
    padding: [10, 20, 10, 20], // 提示框浮层内边距
    formatter: function (param) {
      // if (param.componentSubType == "bar3D") {
      //   return "";
      // }
      if (param.name == '') {
        return ''
      }
      function timestampToYYYYMMDD (timestamp) {
        if (timestamp !== 0) {
          const date = new Date(timestamp) // 将时间戳转换为Date对象
          const year = date.getFullYear()
          const month = ('0' + (date.getMonth() + 1)).slice(-2) // 月份需要加1，且保证两位数显示
          const day = ('0' + date.getDate()).slice(-2) // 保证日期显示两位数
          return year + '-' + month + '-' + day
        } else {
          return '未接入'
        }
      }
      let date
      date = timestampToYYYYMMDD(param.data.accessTime)
      let html
      html = `<div><span style="display:inline-block;width:10px;height:10px;border:1px solid transparent;border-radius:50%;background-color:${param.color};margin-right:5px"></span ><span style="color:${param.color};font-size:14px;font-weight:800">${param.data.name}</span></div>`
      let html2
      let html3
      if (date == '未接入') {
        html2 = ''
      } else {
        html2 = `<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">接入时间 :</span ><span style="color:#fff;font-size:12px">${date}</span></div>`
      }
      if (param.data.computeScale == 0) {
        html3 = ''
      } else {
        html3 = `<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">算力规模 :</span ><span style="color:#fff;font-size:12px">${param.data.computeScale}POps@FP16</span></div><div>`
      }
      html =
        html +
        `<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">所在城市 :</span ><span style="color:#fff;font-size:12px">${param.data.city}</span></div><div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">中心类型 :</span ><span style="color:#fff;font-size:12px">${param.data.centerType}</span></div>` +
        html2 +
        html3
      let html6
      if (param.data.connectionState === 1) {
        html6 = '<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">接入状态 :</span ><span style="color:#fff;font-size:12px">接入中</span></div>'
      } else if (param.data.connectionState === 2) {
        html6 = '<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">接入状态 :</span ><span style="color:#fff;font-size:12px">待接入</span></div>'
      } else if (param.data.connectionState === 3) {
        html6 = '<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">接入状态 :</span ><span style="color:#fff;font-size:12px">已接入</span></div>'
      } else {
        html6 = ''
      }
      let html4
      if (param.data.cardRunTime == 0) {
        html4 = ''
      } else {
        html4 = `<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">正在运行任务 :</span ><span style="color:#fff;font-size:12px">${param.data.jobCount}个任务</span></div>`
      }
      let html5
      if (param.data.jobCount == 0) {
        html5 = ''
      } else {
        html5 = `<div><span style="color:rgba(255,255,255,0.7);font-size:12px;display:inline-block;margin-right:5px">累计服务 :</span ><span style="color:#fff;font-size:12px">${
          param.data.cardRunTime ? param.data.cardRunTime.toFixed(1) : 0
        }卡时</span></div>`
      }
      html = html + html6 + html4 + html5
      return html
    },
    // 提示框浮层的文本样式
    color: '#fff',
    fontStyle: 'normal',
    fontWeight: 'normal',
    fontFamily: 'sans-serif',
    fontSize: 14
  },
  globe: {
    // baseTexture: baseTexture,
    baseTexture: require('./background.jpg'),
    // heightTexture: '/img/bathymetry_bw_composite_4k.jpg',
    // displacementScale: 0.03,//贴图深度
    displacementScale: 0.04,
    shading: 'color',
    displacementQuality: 'low',
    // environment: 'img/bg.jpg',
    environment: new echarts.graphic.LinearGradient(
      0,
      0,
      0,
      1,
      [
        {
          offset: 0,
          color: '#280067' // 天空颜色
        },
        {
          offset: 0.7,
          color: '#0d0d29' // 地面颜色
        },
        {
          offset: 1,
          color: '#0d0d29' // 地面颜色
        }
      ],
      false
    ),

    atmosphere: {
      show: true, // 是否开启大气层
      offset: 12,
      color: '#1d0669',
      glowPower: 6,
      innerGlowPower: 2
    },
    postEffect: {
      // 为画面添加高光，景深，环境光遮蔽（SSAO），调色等效果
      SSAO: {
        // 环境光遮蔽
        radius: 20, // 环境光遮蔽的采样半径。半径越大效果越自然
        intensity: 1, // 环境光遮蔽的强度
        enable: true
      }
    },
    // layers: [
    //   {
    //     type: "blend",
    //     texture: mapChart,
    //   },
    // ],
    light: {
      ambient: {
        intensity: 0.6
      },
      main: {
        intensity: 1.0,
        shadow: false
      }
    },
    viewControl: {
      autoRotate: false, // 默认不启用自动旋转
      targetCoord: [-0.126608, 51.208425], // 初始坐标设置为伦敦
      distance: 400, // 设置初始的地球大小，值越小地球越大
      center: [0, 55, 0]
      // maxDistance: 400,
      // minDistance: 400,
      // rotateSensitivity: 0, // 无法旋转
      // zoomSensitivity: 0, // 无法缩放
    }
  },
  series: [
    // 调度任务
    {
      name: '运行任务',
      type: 'bar3D',
      coordinateSystem: 'globe',
      barSize: 0.1, // 柱子粗细
      opacity: 1,
      // zlevel: 4,
      bevelSize: 0.3,
      label: {
        show: true,
        // 提示框浮层的文本样式
        color: '#ffff00',
        fontWeight: 'normal',
        fontSize: 12,
        lineHeight: 24,
        formatter: (params) => {
          const taskName = params.data.name
          return `{b|${taskName}}`
        },
        rich: {
          a: {
            color: '#00c7ff',
            lineHeight: 24,
            fontSize: 16,
            height: 20,
            fontWeight: 'bolder',
            align: 'center'
          },
          b: {
            color: 'yellow',
            lineHeight: 14,
            fontSize: 14,
            height: 14,
            align: 'center',
            fontWeight: 'normal'
          }
        },
        backgroundColor: 'rgba(9,24,48,0.3)',
        borderColor: 'rgba(9,24,48,0)',
        borderWidth: 0.5,
        borderRadius: 8,
        padding: [8, 15, 5, 15]
      },
      minHeight: 1,
      itemStyle: {
        color: '#ffff00',
        opacity: 1
      },
      data: barBaseData,
      tooltip: {
        show: false
      }
    },
    {
      name: 'scatter3D',
      type: 'scatter3D',
      blendMode: 'lighter',
      coordinateSystem: 'globe',
      showEffectOn: 'render',
      effectType: 'ripple',
      // symbol: "pin",
      // symbolSize: 20,
      symbol: symbolImg,
      // symbol:"path://M14.5234 0C13.6951 0 13.0234 0.671631 13.0234 1.5C13.0234 2.32153 13.6841 2.98901 14.5032 2.99976C13.8413 42.866 0 44.5459 0 51.344C0 56.073 6.50415 59.0005 14.5276 59.0005C22.551 59.0005 29.0554 56.7485 29.0554 51.344C29.0554 44.5459 15.2141 42.866 14.5522 2.99976C15.3674 2.98438 16.0234 2.31885 16.0234 1.5C16.0234 0.671631 15.3521 0 14.5234 0ZM12.937 35.3557C12.937 34.4851 13.6597 33.7793 14.551 33.7793C15.4426 33.7793 16.1653 34.4851 16.1653 35.3557C16.1653 36.2263 15.4426 36.9321 14.551 36.9321C13.6597 36.9321 12.937 36.2263 12.937 35.3557ZM13.937 35.3557C13.937 35.0374 14.2119 34.7793 14.551 34.7793C14.8904 34.7793 15.1653 35.0374 15.1653 35.3557C15.1653 35.6741 14.8904 35.9321 14.551 35.9321C14.2119 35.9321 13.937 35.6741 13.937 35.3557ZM14.7578 39.1833C13.739 39.1833 12.9131 39.99 12.9131 40.9849C12.9131 41.9797 13.739 42.7864 14.7578 42.7864C15.7769 42.7864 16.6028 41.9797 16.6028 40.9849C16.6028 39.99 15.7769 39.1833 14.7578 39.1833ZM14.7578 40.7864C14.6721 40.7864 14.6028 40.8752 14.6028 40.9849C14.6028 41.0945 14.6721 41.1833 14.7578 41.1833C14.8438 41.1833 14.9131 41.0945 14.9131 40.9849C14.9131 40.8752 14.8438 40.7864 14.7578 40.7864ZM15.0127 49.0918C12.8477 49.0918 11.0925 49.8984 11.0925 50.8933C11.0925 51.8882 12.8477 52.6948 15.0127 52.6948C17.1777 52.6948 18.9329 51.8882 18.9329 50.8933C18.9329 49.8984 17.1777 49.0918 15.0127 49.0918Z",
      // symbol:
      //   "path://M523.7248 13.9264l363.776 158.976L523.776 308.736 138.24 172.8512 523.7248 13.9264zM127.5904 195.6352l379.904 126.0544V958.976L127.5392 195.584zM895.3856 196.5056l-371.712 134.8608 8.6016 626.944 363.1104-761.856z M523.8784 0L921.6 173.824 523.8784 322.4064 102.4 173.824 523.8784 0z m-0.3072 27.8016L174.0288 171.9296l349.6448 123.2384 329.728-123.2384L523.5712 27.8016z M102.4 173.824l417.8432 138.6496v700.928L102.4 173.7728z m50.3296 43.6224l341.9136 687.0528V330.9568L152.7296 217.4464z M920.3712 173.824l-409.6 148.5824 9.472 690.944 400.128-839.5264zM870.4 219.136L544.3584 903.3216l-7.68-563.0976L870.4 219.136z",
      symbolSize: [30, 60],
      // rippleEffect: {
      //   period: 4,
      //   scale: 4,
      //   brushType: "fill",
      // },
      label: {
        show: false,
        lineHeight: 24,
        fontWeight: 'bolder',
        formatter: (params) => {
          return ' '
        },
        fontSize: 24,
        color: '#FFF',
        fontWeight: 'bold',
        backgroundColor: 'transparent'
      }
      // itemStyle: {
      //   opacity: 1, // 透明度
      //   borderWidth: 1, // 边框宽度
      //   borderColor: "rgba(255,255,255, 0.8)", //rgba(180, 31, 107, 0.8)
      //   distance: 10,
      // },
    },
    {
      name: 'scatter3D',
      type: 'scatter3D',
      blendMode: 'lighter',
      coordinateSystem: 'globe',
      showEffectOn: 'render',
      effectType: 'ripple',
      // symbol: 'image://' + require('../../assets/sgIng.svg'),
      symbol: 'roundRect',
      symbolSize: 14,
      rippleEffect: {
        period: 4,
        scale: 4,
        brushType: 'fill'
      },
      label: {
        show: true,
        lineHeight: 30,
        fontSize: 18,
        fontWeight: 'bolder',
        color: '#fff',
        formatter: (params) => {
          return params.name
        },
        fontSize: 24,
        color: '#FFF',
        fontWeight: 'bold',
        backgroundColor: 'transparent'
      },
      tooltip: {
        show: false
      },
      itemStyle: {
        // color: 'rgb(29,183,255)',
        opacity: 1, // 透明度
        borderWidth: 1, // 边框宽度
        borderColor: 'rgba(255,255,255,0.4)', // rgba(180, 31, 107, 0.8)
        distance: 10
      }
    },
    {
      name: 'scatter3D',
      type: 'scatter3D',
      blendMode: 'lighter',
      coordinateSystem: 'globe',
      showEffectOn: 'render',
      effectType: 'ripple',
      symbol: 'circle',
      symbolSize: 14,
      rippleEffect: {
        period: 4,
        scale: 4,
        brushType: 'fill'
      },
      tooltip: {
        show: false
      },
      label: {
        show: true,
        lineHeight: 30,
        fontSize: 18,
        fontWeight: 'bolder',
        color: '#fff',
        formatter: (params) => {
          return params.name
        },
        fontSize: 24,
        color: '#FFF',
        fontWeight: 'bold',
        backgroundColor: 'transparent'
      },
      itemStyle: {
        // color: 'rgb(29,183,255)',
        opacity: 1, // 透明度
        borderWidth: 1, // 边框宽度
        borderColor: 'rgba(255,255,255,0.4)', // rgba(180, 31, 107, 0.8)
        distance: 10
      }
    },
    // 地理位置
    {
      name: 'scatter3D',
      type: 'scatter3D',
      blendMode: 'lighter',
      coordinateSystem: 'globe',
      showEffectOn: 'render',
      effectType: 'ripple',
      symbol: 'none',
      symbolSize: 10,
      rippleEffect: {
        period: 4,
        scale: 4,
        brushType: 'fill'
      },
      label: {
        show: true,
        lineHeight: 30,
        fontSize: 18,
        fontWeight: 'bold',
        color: 'rgba(255,255,255,0.5)',
        formatter: function (params) {
          if (params.dataIndex == 0) {
            return '西北地区'
          } else if (params.dataIndex == 1) {
            return '东北地区'
          } else if (params.dataIndex == 2) {
            return '华北地区'
          } else if (params.dataIndex == 3) {
            return '华东地区'
          } else if (params.dataIndex == 4) {
            return '华中地区'
          } else if (params.dataIndex == 5) {
            return '西南地区'
          } else if (params.dataIndex == 6) {
            return '华南地区'
          }
        }
      },
      data: null
    },
    // 主要城市
    {
      name: 'scatter3D',
      type: 'scatter3D',
      blendMode: 'lighter',
      coordinateSystem: 'globe',
      showEffectOn: 'render',
      effectType: 'ripple',
      symbol: 'none',
      symbolSize: 10,
      rippleEffect: {
        period: 4,
        scale: 4,
        brushType: 'fill'
      },
      label: {
        show: true,
        lineHeight: 30,
        fontSize: 16,
        fontWeight: 'bold',
        color: '#fff',
        backgroundColor: 'transparent',
        opacity: 1,
        formatter: function (params) {
          if (params.dataIndex == 0) {
            return '北京'
          } else if (params.dataIndex == 1) {
            return '西安'
          } else if (params.dataIndex == 2) {
            return '中原'
          } else if (params.dataIndex == 3) {
            return '南京'
          } else if (params.dataIndex == 4) {
            return '武汉'
          } else if (params.dataIndex == 5) {
            return '重庆'
          } else if (params.dataIndex == 6) {
            return '韶关'
          } else {
            return '深圳'
          }
        }
      },
      data: null
    },
    // 虚拟中心
    {
      name: 'scatter3D',
      type: 'scatter3D',
      blendMode: 'lighter',
      coordinateSystem: 'globe',
      showEffectOn: 'render',
      effectType: 'ripple',
      symbol: 'none',
      symbolSize: 10,
      rippleEffect: {
        period: 4,
        scale: 4,
        brushType: 'fill'
      },
      label: {
        show: false,
        lineHeight: 30,
        fontSize: 14,
        fontWeight: 'bold',
        color: '#fff',
        formatter: function (params) {
          return ''
        }
      },
      data: null
    },
    // 虚拟飞线
    {
      name: 'lines3D',
      type: 'lines3D',
      coordinateSystem: 'globe',
      blendMode: 'lighter',
      effect: {
        show: true,
        period: 2,
        trailWidth: 3,
        trailLength: 0.5,
        trailOpacity: 0.4,
        trailColor: '#000DC0'
      },
      tooltip: {
        show: false
      },
      data: [],
      silent: true
    },
    // 任务调度
    {
      name: 'lines3D',
      type: 'lines3D',
      coordinateSystem: 'globe',
      effect: {
        show: true,
        period: 2,
        trailWidth: 4,
        trailLength: 0.2,
        trailOpacity: 1,
        trailColor: '#FFDC00'
      },
      tooltip: {
        show: false
      },
      blendMode: 'lighter',
      lineStyle: {
        width: 4,
        color: '#FFDC00',
        opacity: 0
      },
      data: [],
      silent: false
    },
    // 已接入飞线图
    {
      name: 'lines3D',
      type: 'lines3D',
      coordinateSystem: 'globe',
      tooltip: {
        show: false
      },
      effect: {
        show: true,
        period: 2,
        trailWidth: 2,
        trailLength: 0.5,
        trailOpacity: 0,
        trailColor: '#FFDC00'
      },
      blendMode: 'lighter',
      lineStyle: {
        width: 2,
        color: '#FFDC00',
        opacity: 0
      },
      data: [],
      silent: false
    }
  ]
};
(function () {
  if (typeof EventTarget !== 'undefined') {
    const originalFunc = EventTarget.prototype.addEventListener // 先存储原来的事件
    EventTarget.prototype.addEventListener = function (type, fn, capture) {
      if (typeof capture !== 'boolean') {
        capture = capture || {}
        capture.passive = false // 这句是关键的
      }
      originalFunc.call(this, type, fn, capture)
    }
  }
})()
export default {
  name: 'world',
  data () {
    return {
      // baseTexture: require("./background.jpg"),
      populationData: null,
      geoJson: null,
      china: null,
      timer: null,
      timer2: null,
      timer3: null,
      timer4: null,
      timer5: null,
      timer6: null,
      city: [
        {
          value: [116.41, 39.91],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [108.94, 34.34],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [113.85, 34.04],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [118.8, 32.06],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [114.31, 30.59],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [106.55, 29.56],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [113.6, 24.81],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [114.06, 22.54],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        }
      ],
      geographical: [
        {
          value: [98.78, 36.62],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [115.13, 43.1],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [112.33, 37.52],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [115.86, 28.68],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [110.12, 32.01],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [100.64, 26.74],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        },
        {
          value: [108.22, 23.75],
          itemStyle: { color: 'rgba(255,255,255,0.8)' }
        }
      ],
      virtual: [
        // 国外
        {
          value: [-0.12, 51.5]
        },
        {
          value: [-73.86, 40.84]
        },
        {
          value: [-122.41, 37.77]
        },
        {
          value: [139.76, 35.68]
        },
        {
          value: [151.2, -33.86]
        }
      ],
      statusData: { connected: 0, accessing: 0, pending: 0 }
    }
  },
  props: {
    centerType: {
      type: Number,
      default: 1
    },
    Restart: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    centerType: {
      handler (newValue, oldValue) {
        if (newValue == 0) {
          this.clearTimer()
          this.drawEarth2()
        } else {
          this.clearTimer()
          this.getAicenter('noRatate')
        }
      }
    }
  },
  created () {
    // this.clearTimer();
    // clearInterval(this.timer4);
    // this.timer4 = null;
  },
  mounted () {
    this.getBaseWorld()
    window.addEventListener('resize', () => {
      if (myChart) {
        myChart.resize()
      }
    })
    // this.timer4 = setInterval(this.refresh, 3600000);
    window.addEventListener('visibilitychange', this.Refresh)
    this.timer6 = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    window.removeEventListener('resize', myChart.resize)
    mapChart.dispose()
    mapChart = null
    myChart.dispose()
    myChart = null
    this.clearTimer()
    window.removeEventListener('visibilitychange', this.Refresh)
  },
  destroyed () {},
  methods: {
    getBaseWorld () {
      // this.geoJson = GeoJson;
      this.china = China
      if (!myChart) {
        myChart = this.$echarts.init(this.$refs.echart)
      }
      // this.$echarts.registerMap("world", this.geoJson);
      this.$echarts.registerMap('china', { geoJSON: this.china })
      canvas = null
      if (!canvas) {
        canvas = document.createElement('canvas')
      }
      canvas.getContext('2d', {
        willReadFrequently: true
      })
      if (!mapChart) {
        mapChart = echarts.init(canvas, null, {
          // renderer: 'svg',
          width: 8096,
          height: 4096
        })
      }
      // this.drawEarth2()
      this.getAicenter('rotate')
    },
    drawEarth (val) {
      if (val !== 'rotate') {
        option.globe.viewControl = {
          center: [0, 55, 0],
          autoRotate: false, // 默认不启用自动旋转
          targetCoord: [112, 0], // 初始坐标设置为伦敦
          distance: 80 // 设置初始的地球大小，值越小地球越大
        }
      }
      canvas = null
      mapChart.clear()
      myChart.clear()
      mapChart.setOption(pOp)
      option.globe.layers = [
        {
          type: 'overlay',
          texture: mapChart,
          distance: 0
        }
      ]
      myChart.setOption(option, true)
      // this.getTrainJob()
      if (val == 'rotate') {
        this.rotateToTarget(112, 0, 80)
        //   setTimeout(() => {
        //   //需要定时执行的代码，这个3000代表的是毫秒。
        //     this.getTrainJob()
        // }, 10000);
      }
      // this.loadingReady();
    },
    drawEarth2 () {
      var option = {
        tooltip: {
          show: false,
          trigger: 'item', // 触发类型
          backgroundColor: 'rgba(4,4,12,0.7)', // 提示框浮层的背景颜色
          borderColor: '#333', // 提示框浮层的边框颜色
          color: '#fff',
          fontStyle: 'normal',
          fontWeight: 'normal',
          fontFamily: 'sans-serif',
          fontSize: 14
        },
        globe: {
          // baseTexture: baseTexture,
          baseTexture: require('./background.jpg'),
          // heightTexture: '/img/bathymetry_bw_composite_4k.jpg',
          // displacementScale: 0.03,//贴图深度
          displacementScale: 0.04,
          shading: 'color',
          displacementQuality: 'low',
          // environment: 'img/bg.jpg',
          environment: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: '#280067' // 天空颜色
              },
              {
                offset: 0.7,
                color: '#0d0d29' // 地面颜色
              },
              {
                offset: 1,
                color: '#0d0d29' // 地面颜色
              }
            ],
            false
          ),

          atmosphere: {
            show: true, // 是否开启大气层
            offset: 12,
            color: '#1d0669',
            glowPower: 6,
            innerGlowPower: 2
          },
          postEffect: {
            // 为画面添加高光，景深，环境光遮蔽（SSAO），调色等效果
            SSAO: {
              // 环境光遮蔽
              radius: 20, // 环境光遮蔽的采样半径。半径越大效果越自然
              intensity: 1, // 环境光遮蔽的强度
              enable: true
            }
          },
          // layers: [
          //   {
          //     type: "blend",
          //     texture: mapChart,
          //   },
          // ],
          light: {
            ambient: {
              intensity: 0.6
            },
            main: {
              intensity: 1.0,
              shadow: false
            }
          },
          viewControl: {
            autoRotate: false, // 默认不启用自动旋转
            targetCoord: [112, 0], // 初始坐标设置为伦敦
            distance: 80, // 设置初始的地球大小，值越小地球越大
            center: [0, 55, 0]
            // maxDistance: 400,
            // minDistance: 400,
            // rotateSensitivity: 0, // 无法旋转
            // zoomSensitivity: 0, // 无法缩放
          }
        },
        series: [
          {
            name: 'scatter3D',
            type: 'scatter3D',
            blendMode: 'source-over',
            coordinateSystem: 'globe',
            showEffectOn: 'render',
            effectType: 'ripple',
            symbol: 'none',
            symbolSize: 30,
            rippleEffect: {
              period: 4,
              scale: 4,
              brushType: 'fill'
            },
            label: {
              show: true,
              position: 'right',
              formatter: function (params) {
                return params.data.city
              },
              lineHeight: 24,
              fontSize: 20,
              color: '#fff',
              fontWeight: 'bolder',
              backgroundColor: 'transparent',
              distance: 0
            },
            data: [
              {
                value: [114.06, 22.54],
                itemStyle: { color: '#ff0' },
                city: '鹏城云脑',
                label: {
                  position: 'right',
                  color: '#ff0',
                  fontSize: 20,
                  lineHeight: 24,
                  fontWeight: 'bolder'
                }
              },
              {
                value: [113.6, 24.81],
                itemStyle: { color: '#eb95fa' },
                city: '大湾区枢纽集群(韶关)',
                label: {
                  position: 'right',
                  color: '#fff',
                  fontSize: 20,
                  lineHeight: 24,
                  fontWeight: 'bolder'
                }
              },
              {
                value: [118.05, 31.59],
                itemStyle: { color: '#ff7272' },
                city: '中科类脑',
                label: {
                  position: 'right',
                  color: '#fff',
                  fontSize: 20,
                  lineHeight: 24,
                  fontWeight: 'bolder'
                }
              },
              {
                value: [117.12, 36.65],
                itemStyle: { color: '#00c7ff' },
                city: '济南超算',
                label: {
                  position: 'right',
                  color: '#fff',
                  fontSize: 20,
                  lineHeight: 24,
                  fontWeight: 'bolder'
                }
              }
            ]
          },
          {
            name: 'scatter3D',
            type: 'scatter3D',
            blendMode: 'source-over',
            coordinateSystem: 'globe',
            showEffectOn: 'render',
            effectType: 'ripple',
            symbol: 'none',
            symbolSize: 0,
            // rippleEffect: {
            //   period: 4,
            //   scale: 4,
            //   brushType: "fill",
            // },
            label: {
              show: true,
              position: 'right',
              formatter: function (params) {
                return params.data.city
              },
              lineHeight: 20,
              fontSize: 16,
              color: '#fff',
              fontWeight: 'bolder',
              backgroundColor: 'transparent'
            },
            itemStyle: {
              color: 'rgba(255,255,255,1)', // 点颜色
              borderColor: 'rgba(255,255,255,1)', // 点边框颜色
              opacity: 1, // 点的透明度 1不透明
              borderWidth: 2
            },
            data: [
              {
                value: [107.63, 26.65],
                itemStyle: {
                  color: '000'
                },
                city: '多芯光纤/空芯光纤',
                label: {
                  position: 'right',
                  color: '#eb95fa',
                  fontSize: 17,
                  lineHeight: 24,
                  fontWeight: 'bolder'
                }
              },
              {
                value: [117.5, 27.82],
                itemStyle: {
                  color: '000'
                },
                city: 'MPLS互联',
                label: {
                  position: 'right',
                  color: '#ff7272',
                  fontSize: 17,
                  lineHeight: 24,
                  fontWeight: 'bolder'
                }
              },
              {
                value: [112.0, 30.0],
                itemStyle: {
                  color: '000'
                },
                city: 'SD-WAN互联',
                label: {
                  position: 'right',
                  color: '#00c7ff',
                  fontSize: 17,
                  lineHeight: 24,
                  fontWeight: 'bolder'
                }
              }
            ]
          }
        ]
      }
      mapChart.clear()
      myChart.clear()
      mapChart.setOption(pOp2, true);
      (option.globe.layers = [
        {
          type: 'overlay',
          texture: mapChart,
          distance: 0
        }
      ]),
      myChart.setOption(option, true)
    },
    loadingReady () {
      // this.timer2 = setTimeout(() => {
      //   //需要定时执行的代码，这个3000代表的是毫秒。
      //   this.$emit("isReady", false);
      //   clearTimeout(this.timer2);
      //   this.timer2 = null;
      // }, 1000);
    },
    // 定义自动旋转的动作
    rotateToTarget (longitude, latitude, zoom) {
      if (myChart) {
        myChart.setOption({
          globe: {
            viewControl: {
              autoRotate: false, // 关闭自动旋转
              targetCoord: [longitude, latitude], // 目标经纬度
              animation: true, // 开启动画
              animationDurationUpdate: 10000, // 动画时长
              animationEasingUpdate: 'cubicInOut', // 动画效果
              distance: zoom // 这里设置“放大地球”的参数，distance越小则放大程度越大
            }
          }
        })
      }
    },
    getDemo (val) {
      if (val == 'rotate') {
        this.drawEarth('rotate')
        const self = this
        this.timer3 = setTimeout(() => {
          // 需要定时执行的代码，这个3000代表的是毫秒。
          this.$emit('isReady', false)
          self.rotateAnimation()
          clearTimeout(self.timer3)
          self.timer3 = null
        }, 1000)
      } else {
        this.drawEarth()
      }
    },
    getAicenter (val) {
      pOp.series[0].data = []
      // pOp.series[1].data = [];
      option.series[0].data = []
      option.series[1].data = []
      option.series[2].data = []
      option.series[3].data = []
      option.series[4].data = []
      option.series[5].data = []
      option.series[6].data = []
      option.series[7].data = []
      option.series[8].data = []
      option.series[9].data = []
      getAicenter().then((res) => {
        if (res) {
          this.statusData = { connected: 0, accessing: 0, pending: 0 }
          if (this.centerType === 1) {
            option.series[4].data = this.geographical
            option.series[5].data = this.city
            option.series[6].data = this.virtual

            res.aiCenterInfos.forEach((item) => {
              if (item.centerType == '智算中心') {
                let color
                if (item.connectionState == 3) {
                  color = '#00c7ff'
                } else if (item.connectionState == 1) {
                  color = ' #15c252'
                } else {
                  color = '#fa6401'
                }
                if (item.city !== '测试') {
                  if (item.posX != null && item.posY != null) {
                    option.series[1].data.push({
                      value: [item.posX.tude, item.posY.tude],
                      itemStyle: { color: color },
                      name: item.name,
                      city: item.city,
                      centerType: item.centerType,
                      computeScale: item.computeScale,
                      connectionState: item.connectionState,
                      accessTime: item.accessTime,
                      cardRunTime: item.cardRunTime,
                      jobCount: item.jobCount,
                      connectionState: item.connectionState
                    })
                  }
                }
                if (item.connectionState == 3 && item.city !== '测试') {
                }
                if (item.connectionState == 3) {
                  this.statusData.connected++
                } else if (item.connectionState == 1) {
                  this.statusData.accessing++
                } else if (item.connectionState == 2) {
                  this.statusData.pending++
                }
              }
            })
            option.series[6].data.forEach((item) => {
              option.series[7].data.push({
                coords: [
                  [item.value[0], item.value[1]],
                  [114.05, 22.55]
                ],
                lineStyle: {
                  color: 'rgba(255,255,255,0)',
                  width: 2,
                  opacity: 0
                }
              })
            })
          } else if (this.centerType === 2) {
            option.series[2].label = {
              show: true,
              fontWeight: 'bolder',
              distance: 5,
              position: 'right',
              formatter: (params) => {
                return params.name
              },
              fontSize: 18,
              lineHeight: 30,
              color: '#FFF',
              fontWeight: 'bold',
              backgroundColor: 'transparent'
            }
            res.aiCenterInfos.forEach((item) => {
              if (item.centerType == '超算中心') {
                let color
                if (item.connectionState == 3) {
                  color = '#00c7ff'
                } else if (item.connectionState == 1) {
                  color = ' #15c252'
                } else {
                  color = '#fa6401'
                }
                if (item.posX != null && item.posY != null) {
                  option.series[2].data.push({
                    value: [item.posX.tude, item.posY.tude],
                    itemStyle: { color: color },
                    name: item.name,
                    city: item.city,
                    centerType: item.centerType,
                    computeScale: item.computeScale,
                    connectionState: item.connectionState,
                    accessTime: item.accessTime,
                    cardRunTime: item.cardRunTime,
                    jobCount: item.jobCount,
                    connectionState: item.connectionState
                  })
                }
                if (item.connectionState == 3) {
                  this.statusData.connected++
                } else if (item.connectionState == 1) {
                  this.statusData.accessing++
                } else if (item.connectionState == 2) {
                  this.statusData.pending++
                }
              }
            })
          } else {
            option.series[3].label = {
              show: true,
              fontWeight: 'bolder',
              distance: 5,
              position: 'right',
              formatter: (params) => {
                return params.name
              },
              fontSize: 18,
              lineHeight: 30,
              color: '#FFF',
              fontWeight: 'bold',
              backgroundColor: 'transparent'
            }
            res.aiCenterInfos.forEach((item) => {
              if (item.centerType == '东数西算') {
                let color
                if (item.connectionState == 3) {
                  color = '#00c7ff'
                } else if (item.connectionState == 1) {
                  color = ' #15c252'
                } else {
                  color = '#fa6401'
                }
                if (item.posX != null && item.posY.tude != null) {
                  option.series[3].data.push({
                    value: [item.posX.tude, item.posY.tude],
                    itemStyle: { color: color },
                    name: item.name,
                    city: item.city,
                    centerType: item.centerType,
                    computeScale: item.computeScale,
                    connectionState: item.connectionState,
                    accessTime: item.accessTime,
                    cardRunTime: item.cardRunTime,
                    jobCount: item.jobCount,
                    connectionState: item.connectionState
                  })
                }
                if (item.connectionState == 3) {
                  this.statusData.connected++
                } else if (item.connectionState == 1) {
                  this.statusData.accessing++
                } else if (item.connectionState == 2) {
                  this.statusData.pending++
                }
              }
            })
          }
        }
        this.$emit('childEvent', this.statusData)
        if (this.centerType == 1) {
          this.getTrainJob(val)
          // this.getTrainJob(val);
          // this.getTrainJob(val);
        } else {
          // this.drawEarth()
          this.getDemo('NoRotate')
        }
      })
    },
    getTrainJob (val) {
      getTrainJob({ pageIndex: 1, pageSize: 100, status: 'running' }).then(
        (res) => {
          res.otJobs ? res.otJobs : []
          // this.option.series[0].data = [];
          res.otJobs.forEach((item) => {
            option.series[1].data.forEach((Item) => {
              if (item.tasks[0].centerName == Item.name) {
                pOp.series[0].data.push({
                  name: Item.name,
                  taskName: item.name,
                  value: [Item.value[0], Item.value[1]]
                })
                option.series[8].data.push({
                  coords: [
                    [114.05, 22.94],
                    [Item.value[0], Item.value[1]]
                  ],
                  lineStyle: {
                    color: 'rgb(0,104,210)',
                    width: 4,
                    opacity: 0.5
                  }
                })
              }
            })
          })
          pOp.series[0].data = pOp.series[0].data.filter(
            (item, index, self) => {
              return (
                index ===
                self.findIndex(
                  (obj) =>
                    obj.value[0] === item.value[0] &&
                    obj.value[1] === item.value[1]
                )
              )
            }
          )
          if (pOp.series[0].data.length > 5) {
            pOp.series[0].data = pOp.series[0].data.slice(0, 5)
          }
          option.series[8].data = option.series[8].data.filter(
            (item, index, self) => {
              return (
                index ===
                self.findIndex(
                  (obj) =>
                    obj.coords[1][0] === item.coords[1][0] &&
                    obj.coords[1][1] === item.coords[1][1]
                )
              )
            }
          )
          if (option.series[8].data.length > 5) {
            option.series[8].data = option.series[8].data.slice(0, 5)
          }
          pOp.series[0].data.forEach((item) => {
            option.series[0].data.push({
              name: item.taskName,
              // name: extractLetters(item.taskName),
              taskName: item.taskName,
              value: [item.value[0], item.value[1], 120]
            })
          })
          option.series[0].data = option.series[0].data.filter(
            (item, index, self) => {
              return (
                index ===
                self.findIndex(
                  (obj) =>
                    obj.value[0] === item.value[0] &&
                    obj.value[1] === item.value[1] &&
                    obj.value[2] === item.value[2]
                )
              )
            }
          )
          if (option.series[0].data.length > 5) {
            option.series[0].data = option.series[0].data.slice(0, 5)
          }
          option.series[0].data = option.series[0].data.concat(barBaseData)
          pOp.series[0].data = []
          if (val == 'rotate') {
            this.getDemo('rotate')
            this.timer5 = setTimeout(() => {
              // 需要定时执行的代码
              this.getDemo('rotate')
            }, 1000)
          } else {
            this.getDemo('NoRotate')
          }
        }
      )
    },
    rotateAnimation () {
      const self = this
      let self1 = setTimeout(function () {
        // 延迟执行，确保地球完成初始化
        self.rotateToTarget(112, -6, 20) // 2、动画旋转到北京的经纬度并放大，这里设置distance为60，可以根据需要进行调整
        clearTimeout(self1)
        self1 = null
        let self2 = setTimeout(function () {
          self.rotateToTarget(110, 0, 80) // 1、将地球旋转到纽约的经纬度并缩小，这里设置distance为30
          clearTimeout(self2)
          self2 = null
          let self3 = setTimeout(function () {
            self.rotateToTarget(118, 4, 60) // 3、将地球旋转到伦敦的经纬度并缩小，这里设置distance为30
            clearTimeout(self3)
            self3 = null
            let self4 = setTimeout(function () {
              self.rotateAnimation() // 递归调用自身实现循环执行动画
              clearTimeout(self4)
              self4 = null
            }, 10000) // 设置延迟10秒钟后执行下一次动画
          }, 20000) // 设置延迟10秒钟后执行第三段动画
        }, 20000) // 设置延迟10秒钟后执行第二段动画
      }, 10000) // 延迟时间可以自行调整
    },
    clearTimer () {
      // clearTimeout(this.timer);
      // clearTimeout(this.timer2);
      clearTimeout(this.timer3)
      clearTimeout(this.timer4)
      clearTimeout(this.timer5)
      clearTimeout(this.timer6)
      // this.timer = null;
      // this.timer2 = null;
      this.timer3 = null
      this.timer4 = null
      this.timer5 = null
      this.timer6 = null
    },
    resize () {
      if (myChart) {
        myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.clearTimer()
        // this.resize();
        if (this.centerType == 0) {
          this.drawEarth2()
        } else {
          this.drawEarth()
        }
      }
    }
  }
}
</script>

<style scoped>
#earth {
  position: relative;
  width: 100%;
  height: 100%;
  pointer-events: auto;
}
</style>
