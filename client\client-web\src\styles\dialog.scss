/* 对话框共用样式 */

/* ---------- 基础对话框样式 ---------- */
:deep(.el-dialog) {
  background-color: rgba(0, 20, 60, 0.95) !important;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 6px;
  box-shadow: 0 0 20px rgba(18, 137, 221, 0.3);
  backdrop-filter: blur(8px);
}

:deep(.el-dialog__title) {
  color: #fff !important;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px rgba(50, 197, 255, 0.3);
}

:deep(.el-dialog__header) {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
}

:deep(.el-dialog__body) {
  padding: 12px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid rgba(18, 137, 221, 0.5);
  padding: 10px 20px;
}

.task-dialog {
  background-color: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.5);
}

/* 添加计算中心弹窗缩放样式 */
.center-dialog {
  background-color: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.5);
  transform: scale(0.8);
  transform-origin: center;
}

/* ---------- 通用布局组件 ---------- */
/* 区域标题和内容 */
.section-header {
  margin: 3px 0;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
  padding-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  color: #32c5ff;
  font-size: 16px;
  font-weight: bold;
  padding-left: 8px;
  border-left: 3px solid #32c5ff;
}

.section-content {
  padding: 2px 0 4px 0;
}

/* 表单布局 */
.form-container {
  padding: 0 10px;
}

.form-section {
  margin-bottom: 5px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
  margin-bottom: 3px;
}

.form-item-container {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.form-item-full {
  grid-column: span 2;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

/* ---------- 表单元素样式 ---------- */
:deep(.el-form-item) {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 4px !important;
}

:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.9) !important;
  text-align: right !important;
  float: left !important;
  display: inline-block !important;
  height: 32px !important;
  line-height: 32px !important;
  padding-right: 12px !important;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-form-item__content) {
  display: inline-block !important;
  margin-left: 0 !important;
  min-height: 32px;
  line-height: 32px;
  font-size: 14px;
}

/* 输入控件基础样式 */
:deep(.el-input__inner),
:deep(.el-select .el-input__inner),
:deep(.el-radio-group),
:deep(.el-switch) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

:deep(.el-input__inner) {
  background: rgba(18, 137, 221, 0.1) !important;
  border: 1px solid rgba(18, 137, 221, 0.7);
  color: #fff;
  width: 100%;
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus),
:deep(.el-select:hover .el-input__inner) {
  border-color: #32c5ff !important;
  box-shadow: 0 0 5px rgba(50, 197, 255, 0.3);
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
}

/* 移除数字输入框的箭头 */
:deep(input[type=number]::-webkit-inner-spin-button),
:deep(input[type=number]::-webkit-outer-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}

:deep(input[type=number]) {
  -moz-appearance: textfield;
}

/* 下拉菜单 */
:deep(.el-select-dropdown) {
  background-color: rgba(0, 20, 60, 0.95);
  border: 1px solid rgba(18, 137, 221, 0.7);
}

:deep(.el-select-dropdown__item) {
  color: rgba(255, 255, 255, 0.9);
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(18, 137, 221, 0.2);
}

:deep(.el-select-dropdown__item.selected) {
  color: #32c5ff;
  font-weight: bold;
}

/* 单选按钮 */
:deep(.el-radio-group) {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 450px;
}

:deep(.el-radio) {
  margin-right: 25px;
  padding: 8px 15px;
  border-radius: 4px;
  transition: all 0.3s;
  border: 1px solid transparent;
}

:deep(.el-radio:hover) {
  background-color: rgba(18, 137, 221, 0.1);
  border-color: rgba(18, 137, 221, 0.3);
}

:deep(.el-radio__label) {
  color: #fff;
  font-size: 15px;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #32c5ff;
  border-color: #32c5ff;
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #32c5ff;
}

:deep(.el-radio.is-checked) {
  background-color: rgba(18, 137, 221, 0.15);
  border-color: rgba(18, 137, 221, 0.5);
}

/* 开关 */
:deep(.el-switch.is-disabled) {
  opacity: 1;
}

:deep(.el-switch.is-disabled .el-switch__core) {
  background-color: rgba(18, 137, 221, 0.8) !important;
  border-color: rgba(18, 137, 221, 1) !important;
}

:deep(.el-switch.is-disabled .el-switch__core:after) {
  background-color: #fff !important;
}

/* ---------- 统一滚动条样式 ---------- */
/* 所有需要滚动条样式的元素统一样式 */
.log-content-wrapper::-webkit-scrollbar,
.log-content::-webkit-scrollbar,
.yaml-content::-webkit-scrollbar,
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

.log-content-wrapper::-webkit-scrollbar-track,
.log-content::-webkit-scrollbar-track,
.yaml-content::-webkit-scrollbar-track,
:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: rgba(0, 20, 60, 0.3);
  border-radius: 4px;
}

.log-content-wrapper::-webkit-scrollbar-thumb,
.log-content::-webkit-scrollbar-thumb,
.yaml-content::-webkit-scrollbar-thumb,
:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: rgba(18, 137, 221, 0.6);
  border-radius: 4px;
  transition: background 0.3s;
}

.log-content-wrapper::-webkit-scrollbar-thumb:hover,
.log-content::-webkit-scrollbar-thumb:hover,
.yaml-content::-webkit-scrollbar-thumb:hover,
:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: rgba(50, 197, 255, 0.8);
}

.log-content-wrapper::-webkit-scrollbar-corner,
.log-content::-webkit-scrollbar-corner,
.yaml-content::-webkit-scrollbar-corner,
:deep(.el-table__body-wrapper::-webkit-scrollbar-corner) {
  background: transparent;
}

/* ---------- 表格样式 ---------- */
.center-table {
  margin-bottom: 15px;
  background-color: transparent;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 15px rgba(0, 20, 60, 0.3);
}

:deep(.center-table .el-table) {
  background-color: transparent !important;
  border: none !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

:deep(.center-table .el-table__header-wrapper) {
  overflow-x: hidden !important;
}

:deep(.center-table .el-table__header-wrapper th) {
  background: linear-gradient(to bottom, rgba(18, 137, 221, 0.3), rgba(0, 20, 60, 0.5)) !important;
  color: #32c5ff !important;
  font-weight: 600;
  font-size: 14px;
  border-bottom: 1px solid rgba(50, 197, 255, 0.4);
  padding: 8px 0;
  height: 38px;
  letter-spacing: 0.5px;
  text-shadow: 0 0 5px rgba(50, 197, 255, 0.3);
}

:deep(.center-table th > .cell) {
  white-space: nowrap !important;
  text-overflow: unset !important;
  overflow: visible !important;
  line-height: 23px !important;
}

:deep(.center-table .el-table__header),
:deep(.center-table .el-table__body) {
  width: 100% !important;
  table-layout: fixed !important;
}

:deep(.center-table .el-table__body-wrapper) {
  background-color: rgba(0, 20, 60, 0.4);
  overflow-x: auto !important;
}

:deep(.center-table .el-table__row) {
  background-color: transparent !important;
  transition: background-color 0.3s;
}

:deep(.center-table .el-table__row:nth-child(odd)) {
  background-color: rgba(18, 137, 221, 0.05) !important;
}

:deep(.center-table .el-table__row:hover),
:deep(.center-table .el-table__body tr:hover > td) {
  background-color: rgba(18, 137, 221, 0.15) !important;
}

:deep(.center-table .el-table__row td) {
  border-bottom: 1px solid rgba(18, 137, 221, 0.3);
  color: rgba(255, 255, 255, 0.9);
  padding: 6px 0;
  height: 38px;
  font-size: 14px;
  font-weight: 400;
}

:deep(.center-table .el-table__row:last-child td) {
  border-bottom: none;
}

/* 隐藏表格默认边框 */
:deep(.center-table .el-table::before),
:deep(.center-table .el-table::after),
:deep(.center-table .el-table__fixed::before),
:deep(.center-table .el-table__fixed-right::before) {
  display: none !important;
}

:deep(.center-table .el-table__empty-block) {
  background-color: rgba(0, 20, 60, 0.4);
  min-height: 60px;
}

/* 单元格边框和对齐 */
:deep(.center-table .el-table td),
:deep(.center-table .el-table th) {
  border-right: 1px solid rgba(18, 137, 221, 0.5) !important; /* 修改竖边框颜色 */
  padding: 8px 10px !important;
}

:deep(.center-table .el-table td:last-child),
:deep(.center-table .el-table th:last-child) {
  border-right: none !important;
}

:deep(.center-table .el-table th.is-leaf) {
  border-bottom: 1px solid rgba(18, 137, 221, 0.4) !important;
}

/* 固定列样式 */
:deep(.center-table .el-table__fixed),
:deep(.center-table .el-table__fixed-right) {
  background-color: rgba(0, 20, 60, 0.6) !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3) !important;
}

:deep(.center-table .el-table__fixed-right-patch) {
  background: linear-gradient(to bottom, rgba(18, 137, 221, 0.3), rgba(0, 20, 60, 0.5)) !important;
}

/* 表格操作按钮 */
:deep(.center-table .cell:last-child) {
  display: flex;
  justify-content: center;
}

:deep(.center-table .el-button--text) {
  padding: 0;
  margin: 0;
  font-size: 0;
  background: none;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

:deep(.center-table .el-button--text:first-child) {
  background-color: rgba(18, 137, 221, 0.15);
  margin-right: 6px;
}

:deep(.center-table .el-button--text:last-child) {
  background-color: rgba(255, 107, 107, 0.15);
}

:deep(.center-table .el-button--text i) {
  font-size: 14px;
}

:deep(.center-table .el-button--text:first-child i) {
  color: #32c5ff;
}

:deep(.center-table .el-button--text:last-child i) {
  color: #ff6b6b;
}

:deep(.center-table .el-button--text:hover) {
  transform: translateY(-2px);
}

:deep(.center-table .el-button--text:first-child:hover) {
  background-color: rgba(18, 137, 221, 0.3);
  box-shadow: 0 2px 6px rgba(18, 137, 221, 0.3);
}

:deep(.center-table .el-button--text:last-child:hover) {
  background-color: rgba(255, 107, 107, 0.3);
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
}

/* 通用表格样式 */
:deep(.el-table) {
  background-color: transparent !important;
  border: 1px solid rgba(18, 137, 221, 0.5) !important;
}

:deep(.el-table::before) {
  display: none;
}

:deep(.el-table th),
:deep(.el-table td) {
  background-color: transparent !important;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5) !important;
}

:deep(.el-table--border th),
:deep(.el-table--border td) {
  border-right: 1px solid rgba(18, 137, 221, 0.5) !important;
}

:deep(.el-table thead th) {
  background-color: rgba(18, 137, 221, 0.15) !important;
  color: #32c5ff !important;
  font-weight: bold;
}

:deep(.el-table tbody td) {
  color: rgba(255, 255, 255, 0.9) !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(18, 137, 221, 0.08) !important;
}

:deep(.el-table__body tr:hover > td) {
  background-color: rgba(18, 137, 221, 0.2) !important;
}

/* 空表格状态 */
.empty-table, .log-empty, .yaml-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  border: 1px dashed rgba(18, 137, 221, 0.5);
  border-radius: 4px;
  background-color: rgba(18, 137, 221, 0.05);
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.log-empty i, .yaml-empty i {
  font-size: 40px;
  margin-bottom: 10px;
}

/* ---------- 按钮样式 ---------- */
/* 通用按钮样式 */
:deep(.el-button) {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
  font-size: 15px;
}

:deep(.el-button:hover) {
  border-color: #32c5ff;
  color: #32c5ff;
}

:deep(.el-button--primary) {
  background-color: rgba(18, 137, 221, 0.8);
  border-color: rgba(18, 137, 221, 1);
}

:deep(.el-button--primary:hover) {
  background-color: rgba(18, 137, 221, 1);
}

.submit-btn, .cancel-btn, .preview-btn {
  min-width: 90px;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn:hover, .cancel-btn:hover, .preview-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(18, 137, 221, 0.3);
}

.submit-btn:active, .cancel-btn:active, .preview-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(18, 137, 221, 0.2);
}

.cancel-btn {
  margin-right: 15px;
}

/* 对话框底部按钮布局 */
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

.preview-btn {
  background-color: rgba(50, 60, 80, 0.7);
  border-color: rgba(50, 60, 80, 0.9);
  color: #fff;
  margin-right: auto; /* 让按钮保持在左侧 */
}

.preview-btn:hover {
  background-color: rgba(50, 60, 80, 0.9);
  border-color: rgba(50, 60, 80, 1);
  color: #fff;
}

/* 添加计算中心按钮 */
.add-center-btn {
  background: rgba(18, 137, 221, 0.2);
  border: 1px solid rgba(50, 197, 255, 0.6);
  color: #32c5ff;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.add-center-btn i {
  margin-right: 4px;
  font-size: 16px;
}

.add-center-btn:hover {
  background: rgba(50, 197, 255, 0.2);
  border-color: #32c5ff;
  color: #fff;
  box-shadow: 0 0 10px rgba(50, 197, 255, 0.3);
}

/* 刷新按钮 */
.refresh-btn {
  min-width: 90px;
  background: rgba(18, 137, 221, 0.2) !important;
  border: 1px solid rgba(50, 197, 255, 0.6) !important;
  color: #32c5ff !important;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.refresh-btn i {
  margin-right: 5px;
  font-size: 15px;
}

.refresh-btn:hover {
  background: rgba(50, 197, 255, 0.3) !important;
  border-color: #32c5ff !important;
  color: #fff !important;
  box-shadow: 0 4px 10px rgba(50, 197, 255, 0.3);
  transform: translateY(-1px);
}

/* ---------- YAML和日志相关样式 ---------- */
/* 容器样式 */
.yaml-content-container, .log-content-wrapper {
  position: relative;
  height: 450px;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 6px;
  background-color: rgba(0, 12, 36, 0.8);
  overflow: hidden;
  box-shadow: 0 0 15px rgba(0, 20, 60, 0.3);
  transition: box-shadow 0.3s ease;
}

.yaml-content-container:hover, .log-content-wrapper:hover {
  box-shadow: 0 0 20px rgba(18, 137, 221, 0.4);
}

/* 内容样式 */
.yaml-content, .log-content {
  height: 100%;
  padding: 12px 15px;
  margin: 0;
  overflow-y: auto;
  word-break: break-all;
  white-space: pre-wrap;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  background-color: transparent !important;
}

/* 日志容器样式 */
.log-container {
  margin-top: 10px;
}

.log-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
}

.log-controls {
  display: flex;
  align-items: center;
}

.log-pagination {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

/* YAML预览对话框 */
.yaml-preview-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }

  .yaml-info {
    margin-bottom: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
  }
}

/* ---------- MessageBox 确认对话框样式 ---------- */
.el-message-box {
  background-color: rgba(0, 20, 60, 0.95) !important;
  border: 1px solid rgba(18, 137, 221, 0.5) !important;
  border-radius: 8px !important;
  box-shadow: 0 0 30px rgba(18, 137, 221, 0.4), 0 0 60px rgba(0, 20, 60, 0.3) !important;
  min-width: 420px !important;
  max-width: 500px !important;
}

.el-message-box__header {
  padding: 12px 24px 12px 24px !important;
  border-bottom: 1px solid rgba(18, 137, 221, 0.3) !important;
  background: linear-gradient(135deg, rgba(18, 137, 221, 0.1), rgba(0, 20, 60, 0.2)) !important;
  border-radius: 8px 8px 0 0 !important;
}

.el-message-box__title {
  color: #fff !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  letter-spacing: 0.8px !important;
  margin: 0 !important;
}

.el-message-box__content {
  padding: 20px 24px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 15px !important;
  line-height: 1.7 !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: 16px !important;
}

.el-message-box__message {
  color: rgba(255, 255, 255, 0.9) !important;
  flex: 1 !important;
  margin: 0 !important;
  padding-top: 2px !important;
}

.el-message-box__btns {
  padding: 8px 24px 8px 24px !important;
  border-top: 1px solid rgba(18, 137, 221, 0.3) !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
  background: linear-gradient(135deg, rgba(0, 20, 60, 0.2), rgba(18, 137, 221, 0.05)) !important;
  border-radius: 0 0 8px 8px !important;
}

.el-message-box__btns .el-button {
  min-width: 75px !important;
  height: 32px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  background: transparent !important;
  border: 1px solid rgba(18, 137, 221, 0.8) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 5px !important;
}

.el-message-box__btns .el-button:hover {
  border-color: #32c5ff !important;
  color: #32c5ff !important;
}

.el-message-box__btns .el-button--primary {
  background-color: rgba(18, 137, 221, 0.8) !important;
  border-color: rgba(18, 137, 221, 1) !important;
  color: #fff !important;
}

.el-message-box__btns .el-button--primary:hover {
  background-color: rgba(18, 137, 221, 1) !important;
  border-color: #32c5ff !important;
  color: #fff !important;
}

.el-message-box__close {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 18px !important;
  width: 24px !important;
  height: 24px !important;
  line-height: 24px !important;
  text-align: center !important;
  border-radius: 50% !important;
}

.el-message-box__close:hover {
  color: #32c5ff !important;
}

/* MessageBox图标样式 */
.el-message-box__status {
  color: #32c5ff !important;
  font-size: 28px !important;
  margin-right: 0 !important;
  flex-shrink: 0 !important;
  text-shadow: 0 0 10px rgba(50, 197, 255, 0.5) !important;
  animation: iconGlow 2s ease-in-out infinite alternate !important;
}

@keyframes iconGlow {
  from {
    text-shadow: 0 0 10px rgba(50, 197, 255, 0.5) !important;
  }
  to {
    text-shadow: 0 0 20px rgba(50, 197, 255, 0.8), 0 0 30px rgba(50, 197, 255, 0.3) !important;
  }
}

/* MessageBox遮罩层样式 */
.v-modal {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* ---------- 任务状态相关样式 ---------- */
.status-container {
  padding: 10px;
}

.task-info-grid {
  margin-top: 10px;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(0, 20, 60, 0.4);
  box-shadow: 0 0 15px rgba(0, 20, 60, 0.3);
}

.info-row {
  display: grid;
  grid-template-columns: 120px 1fr 120px 1fr;
  border-bottom: 1px solid rgba(18, 137, 221, 0.3);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  padding: 12px 15px;
  background-color: rgba(18, 137, 221, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  border-right: 1px solid rgba(18, 137, 221, 0.3);
}

.info-value {
  padding: 12px 15px;
  color: #fff;
}

.config-file {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  grid-column: span 3;
}

.status-cell {
  display: flex;
  align-items: center;
}

.status-completed,
.status-running {
  padding: 5px 12px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-completed::before,
.status-running::before {
  content: '';
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-completed {
  background-color: rgba(82, 196, 26, 0.15);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.5);
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.2);
}

.status-completed::before {
  background-color: #52c41a;
  box-shadow: 0 0 5px #52c41a;
}

.status-running {
  background-color: rgba(24, 144, 255, 0.15);
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.5);
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.2);
}

.status-running::before {
  background-color: #1890ff;
  box-shadow: 0 0 5px #1890ff;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.8);
  }
}

/* ---------- 任务详情选项卡样式 ---------- */
:deep(.task-detail-tabs) {
  border: none;
}

:deep(.task-detail-tabs .el-tabs__header) {
  background-color: rgba(0, 20, 60, 0.4);
  border-radius: 4px;
  padding: 5px;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
  margin-bottom: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.task-detail-tabs .el-tabs__nav) {
  border: none;
}

:deep(.task-detail-tabs .el-tabs__item) {
  color: rgba(255, 255, 255, 0.9);
  background-color: transparent;
  border: none;
  padding: 8px 20px;
  transition: all 0.25s ease;
  height: 36px;
  line-height: 20px;
  position: relative;
  overflow: hidden;
  font-size: 15px;
  letter-spacing: 0.5px;
}

:deep(.task-detail-tabs .el-tabs__item::after) {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: #32c5ff;
  transition: all 0.25s ease;
  opacity: 0;
  transform: translateX(-50%);
}

:deep(.task-detail-tabs .el-tabs__item:hover::after) {
  width: 80%;
  opacity: 0.8;
}

:deep(.task-detail-tabs .el-tabs__item.is-active) {
  color: #32c5ff;
  background-color: rgba(18, 137, 221, 0.2);
  border-radius: 4px;
  font-weight: 500;
  text-shadow: 0 0 3px rgba(50, 197, 255, 0.5);
}

:deep(.task-detail-tabs .el-tabs__item.is-active::after) {
  width: 90%;
  opacity: 1;
}

:deep(.task-detail-tabs .el-tabs__item:hover) {
  color: #32c5ff;
  text-shadow: 0 0 2px rgba(50, 197, 255, 0.3);
}

:deep(.task-detail-tabs .el-tabs__nav-wrap::after) {
  display: none;
}

/* ---------- 分页器样式 ---------- */
:deep(.el-pagination) {
  background-color: transparent;
  padding: 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

:deep(.el-pagination button) {
  background-color: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.5);
  color: rgba(255, 255, 255, 0.9);
}

:deep(.el-pagination button:hover) {
  color: #32c5ff;
  background-color: rgba(18, 137, 221, 0.2);
}

:deep(.el-pagination .btn-prev, .el-pagination .btn-next) {
  background-color: rgba(18, 137, 221, 0.1);
  border-radius: 4px;
  padding: 0 8px;
}

:deep(.el-pager li) {
  background-color: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.5);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  margin: 0 2px;
}

:deep(.el-pager li.active) {
  background-color: rgba(18, 137, 221, 0.3);
  color: #32c5ff;
  border-color: #32c5ff;
}

:deep(.el-pager li:hover) {
  color: #32c5ff;
}

:deep(.el-pagination .el-input__inner) {
  background-color: rgba(18, 137, 221, 0.1) !important;
  border: 1px solid rgba(18, 137, 221, 0.5);
  color: rgba(255, 255, 255, 0.9);
}

:deep(.el-pagination__editor.el-input) {
  width: 50px;
}

/* ---------- 加载状态控制 ---------- */
:deep(.el-loading-mask) {
  background-color: rgba(0, 12, 36, 0.7) !important;
  backdrop-filter: blur(2px);
}

:deep(.el-loading-spinner .path) {
  stroke: #32c5ff;
}

:deep(.el-loading-spinner .el-loading-text) {
  color: #32c5ff;
  font-size: 14px;
  margin-top: 10px;
}

:deep(.el-loading-mask .el-loading-spinner) {
  margin-top: -15px;
}

/* ---------- 通用工具类 ---------- */
.fixed-width {
  width: 200px !important;
}

.unit-label {
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.7);
  display: inline-flex;
  align-items: center;
  height: 36px;
  font-size: 15px;
}

.box-with-border {
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 4px;
  box-shadow: 0 0 15px rgba(0, 20, 60, 0.3);
  background-color: rgba(0, 12, 36, 0.8);
}
