import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    id: 0,
    compareId: 0,
    strategy1: '',
    strategy2: '',
    lastTime: 0,
    interval: 24,
    // 新增：支持多个任务选择
    selectedTasks: [], // 存储所有选中的任务
    selectedTaskIds: [] // 存储所有选中的任务ID
  },
  mutations: {
    changeId (state, id) {
      state.id = id
    },
    changeCompareId (state, id) {
      state.compareId = id
    },
    changeState1 (state, value) {
      state.strategy1 = value
    },
    changeState2 (state, value) {
      state.strategy2 = value
    },
    changelastTime (state, value) {
      state.lastTime = value
    },
    changeInterval (state, value) {
      state.interval = value
    },
    // 新增：设置多个选中的任务
    setSelectedTasks (state, tasks) {
      state.selectedTasks = tasks
      state.selectedTaskIds = tasks.map(task => task.ID)

      // 保持向后兼容，设置主任务和对比任务
      if (tasks.length > 0) {
        state.id = tasks[0].ID
        state.strategy1 = tasks[0].Strategy
      } else {
        state.id = 0
        state.strategy1 = ''
      }

      if (tasks.length > 1) {
        state.compareId = tasks[1].ID
        state.strategy2 = tasks[1].Strategy
      } else {
        state.compareId = 0
        state.strategy2 = ''
      }
    },
    // 新增：清空选中的任务
    clearSelectedTasks (state) {
      state.selectedTasks = []
      state.selectedTaskIds = []
      state.id = 0
      state.compareId = 0
      state.strategy1 = ''
      state.strategy2 = ''
    }
  },
  actions: {
  },
  modules: {
  }
})
