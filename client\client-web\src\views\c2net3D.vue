<template>
  <div>
    <div
      class="background"
      @click="hiddenNavbar"
      ref="background"
      v-if="showContent"
    >
      <div class="contentWrapper">
        <el-main>
          <div class="titleBg">
            <div>
              <el-row type="flex" justify="center">
                <el-col :span="12">
                  <div class="title">
                    中国算力网<span class="smalltitle">(C²NET)</span
                    >国家总调度中心
                  </div>
                </el-col>
              </el-row>
            </div>
            <dropdown class="dropdown" :type="'c2net3D'"></dropdown>
          </div>
          <div class="leftChart">
            <div class="showMt">
              <el-row>
                <el-col :span="24">
                  <el-row>
                    <el-col :span="24">
                      <div class="title1 titleBg1">累计任务情况</div>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <div>
                    <CumulativeTask :config="accrueData"></CumulativeTask>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="showMt">
              <el-row>
                <el-col :span="24">
                  <div class="title1 titleBg1">运行任务情况</div>
                </el-col>
              </el-row>
              <TASK id="task" :config="taskConfig"></TASK>
            </div>
            <div class="showMt">
              <el-row>
                <el-col :span="24">
                  <div class="title1 titleBg1">数据调度任务</div>
                </el-col>
              </el-row>
              <Monitor></Monitor>
            </div>
          </div>
          <div class="rightChart">
            <div class="showMt">
              <el-row style="margin-bottom: 3px">
                <el-col :span="24">
                  <div class="title1 titleBg1">算力使用趋势</div>
                </el-col>
              </el-row>
              <TEND id="TEND" :config="tendConfig"></TEND>
            </div>
            <div class="showMt">
              <el-row>
                <el-col :span="24">
                  <div class="title1 titleBg1">中心算力情况</div>
                </el-col>
              </el-row>
              <STATUS
                id="Status"
                :data="centerData"
                :config="statusConfig"
              ></STATUS>
            </div>
            <div class="showMt">
              <el-row style="margin-bottom: 20px">
                <el-col :span="24">
                  <div class="title1 titleBg1">算力接入情况</div>
                </el-col>
              </el-row>
              <INSERT
                id="Imsert"
                :data="connectedData"
                :config="connectedConfig"
              ></INSERT>
            </div>
          </div>
          <div class="totalWrapper">
            <el-row type="flex" justify="center">
              <el-col :span="24">
                <Total :data="totalNum" :statusData="statusData"></Total>
              </el-col>
              <div></div>
            </el-row>
            <el-row type="flex" justify="center">
              <el-col :span="24">
                <div class="messageWrapper">
                  <el-row type="flex" justify="space-between">
                    <el-col :span="6">
                      <provinceMap
                        v-if="show1"
                        style="position: absolute; top: 0px"
                      ></provinceMap>
                      <provinceMap2
                        v-if="show2"
                        style="position: absolute; top: 0px"
                      ></provinceMap2>
                    </el-col>
                    <el-col :span="12">
                      <el-row
                        type="flex"
                        justify="space-around"
                        class="buttonPos"
                      >
                        <el-col :span="8">
                          <div
                            :class="{ buttonStyle: true, check: show1 }"
                            @click="changeNetwork('computing')"
                          >
                            算力资源调度
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div
                            :class="{ buttonStyle: true, check: show2 }"
                            @click="changeNetwork('new')"
                          >
                            超宽带光纤直连
                          </div>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="6" v-if="show2">
                      <div class="statusWrapper2">
                        <div class="wrapper">
                          <div class="arrow1">
                            <span class="ml-2">10TB全光网络互联</span>
                          </div>
                          <div class="arrow2">
                            <span class="ml-2">SD-WAN互联</span>
                          </div>
                          <div class="arrow3">
                            <span class="ml-2">MPLS互联</span>
                          </div>
                        </div>
                      </div></el-col
                    >
                    <el-col :span="6" v-if="show1">
                      <div class="statusWrapper1">
                        <div>
                          <div class="staticWrapper" @click="changeShow(1)">
                            <span class="dotBlue"></span
                            ><span class="staticName" v-if="showType !== 1"
                              >智算中心</span
                            >
                            <span class="boldStaticName" v-if="showType == 1"
                              >智算中心</span
                            >
                            <span class="staticNum"
                              >({{ intelligenceTotal }})</span
                            >
                          </div>
                          <div class="staticWrapper" @click="changeShow(2)">
                            <span class="dotGreen"></span
                            ><span class="staticName" v-if="showType !== 2"
                              >超算中心</span
                            >
                            <span class="boldStaticName" v-if="showType == 2"
                              >超算中心</span
                            >
                            <span class="staticNum">({{ superTotal }})</span>
                          </div>
                          <div class="staticWrapper" @click="changeShow(3)">
                            <span class="dotOrange"></span
                            ><span class="staticName" v-if="showType !== 3"
                              >东数西算</span
                            >
                            <span class="boldStaticName" v-if="showType == 3"
                              >东数西算</span
                            >
                            <span class="staticNum">({{ eAdnwTotal }})</span>
                          </div>
                          <div style="margin-top: 5px">
                            <span class="dotBlue" v-if="blue"></span>
                            <span class="dotGreen" v-if="green"></span>
                            <span class="dotOrange" v-if="orange"></span>
                            <span class="staticName">已接入</span
                            ><span class="staticNum"
                              >({{ statusData.connected }})</span
                            >
                          </div>
                          <div>
                            <span class="dotBlue2" v-if="blue"></span>
                            <span class="dotGreen2" v-if="green"></span>
                            <span class="dotOrange2" v-if="orange"></span>
                            <span class="staticName">接入中</span
                            ><span class="staticNum"
                              >({{ statusData.accessing }})</span
                            >
                          </div>
                          <div>
                            <span class="dotBlue3" v-if="blue"></span>
                            <span class="dotGreen3" v-if="green"></span>
                            <span class="dotOrange3" v-if="orange"></span>
                            <span class="staticName">待接入</span
                            ><span class="staticNum"
                              >({{ statusData.pending }})</span
                            >
                          </div>
                        </div>
                      </div></el-col
                    >
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="worldPos">
            <world
              v-on:isReady="handleEvent"
              :centerType="showType"
              :Restart="restart"
              @childEvent="handleChildEvent"
            ></world>
            <div class="shade"></div>
          </div>
          <div class="notReady" v-if="isShow">
            <dv-loading><span class="loading">Loading</span></dv-loading>
          </div>
          <div class="footerWrapper">
            <div class="footerBg"></div>
            <div class="footerLogo"></div>
          </div>
        </el-main>
      </div>
    </div>
    <div v-if="!showContent" class="passwordPos">
      <el-row type="flex" class="row-bg" justify="center">
        <el-col :span="6">
          <el-form :model="formInline" class="demo-form-inline">
            <el-form-item label="">
              <el-input
                v-model="formInline.password"
                placeholder="请输入密码"
                show-password
              ></el-input>
            </el-form-item>
            <el-form-item class="passConfirm">
              <el-button type="primary" @click="onSubmit" class="passConfirm"
                >确认</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
// eslint-disable-next-line import/no-duplicates
import TEND from '@/components/lineChart'
// eslint-disable-next-line import/no-duplicates
import STATUS from '@/components/Histogram'
// eslint-disable-next-line import/no-duplicates
import INSERT from '@/components/HisAndLine'
// eslint-disable-next-line import/no-duplicates
import TASK from '@/components/Histogram2'
import Monitor from '@/components/schedulingMon'
import Total from '@/components/Total3D'
import chinaMap from '@/components/chinaMapDemo'
// import statusMap from "@/components/statusMap";
import provinceMap from '@/components/provinceMap4'
import provinceMap2 from '@/components/provinceMap5'
import provinceMap3 from '@/components/provinceMap3'
import World from '../components/world/world.vue'
// import World2 from "../components/world/world2.vue";
import dropdown from '@/components/Dropdown'
import circle from '@/components/circle'
import { order } from '@/utils/index'
import { getConfig } from '@/utils/config'
import moment from 'moment'
import {
  getAicenter,
  getTrainJob,
  getAccrueCenter,
  getConnected,
  getDynamic
} from '@/api/screenService.js'

export default {
  components: {
    TEND: TEND,
    STATUS: STATUS,
    INSERT: INSERT,
    TASK: TASK,
    Monitor: Monitor,
    Total: Total,
    chinaMap: chinaMap,
    // statusMap: statusMap,
    provinceMap: provinceMap,
    provinceMap2: provinceMap2,
    provinceMap3: provinceMap3,
    world: World,
    // world1: World1,
    // world2: World2,
    CumulativeTask: circle,
    dropdown: dropdown
  },
  data () {
    return {
      npuData: { used: [], unused: [], xData: [] },
      gpuData: { used: [], unused: [], xData: [] },
      cpuData: { used: [], unused: [], xData: [] },
      taskConfig: { unit: '单位:个', status: ['运行任务数', '等待任务数'] },
      statusConfig: { unit: '单位:  卡时', status: ['使用量'] },
      tendConfig: { unit: '单位:  卡时', status: ['使用量'] },
      connectedConfig: { unit: '单位:  POps@FP16', status: ['接入数'] },
      mapData: [],
      statusData: { connected: 0, accessing: 0, pending: 0 },
      crossDomain: [
        { value: 64, name: '已完成' },
        { value: 30, name: '等待中' },
        { value: 58, name: '处理中' }
      ],
      crossTitle: '',
      collaborate: [
        { value: 64, name: '已完成' },
        { value: 30, name: '等待中' },
        { value: 58, name: '处理中' }
      ],
      coollTitle: '',
      totalNum: {
        centerTotal: { accessed: 0, unaccess: 0 },
        computingPower1: 0,
        storageScale1: 0,
        computingPower2: 0,
        storageScale2: 0
      },
      tableData: [],
      pageIndex: 1,
      pageSize: 100,
      timer: null,
      timer2: null,
      timer3: null,
      itemNow: [],
      item1: [], // item1智算
      item2: [], // item2超算
      item3: [], // item3集群东数西算
      item4: [], // 全部
      show: false,
      show1: true,
      show2: false,
      show3: false,
      type1: true,
      type2: false,
      type3: false,
      type: 4,
      networkType: 'computing',
      accrueData: {
        config1: undefined,
        config2: undefined,
        config3: undefined
      },
      tendData: { used: [], xData: [] },
      centerData: { used: [], xData: [] },
      connectedData: { used: [], xData: [] },
      total: 0,
      intelligenceTotal: 0,
      superTotal: 0,
      eAdnwTotal: 0,
      tipData: {},
      desMap: null,
      destination: [],
      dataMap: 1,
      showType: 1,
      restart: true,
      blue: true,
      green: false,
      orange: false,
      isShow: true,
      showContent: false,
      formInline: {
        password: ''
      },
      num: 1,
      canClick: true
    }
  },
  created () {
    var passworld = localStorage.getItem('c2net')
    if (passworld == 'c2netPcl') {
      this.showContent = true
      localStorage.setItem('c2net', 'c2netPcl')
      this.getTaskTotal()
      this.getAicenter()
      // this.getTrainJob();
      this.getAccrueCenter()
      this.getConnected()
    } else {
      this.showContent = false
    }
  },
  mounted () {
    this.getNewData()
  },
  watch: {
    type: {
      handler (newValue, oldValue) {
        // this.getAicenter();
      }
    }
  },
  methods: {
    changeNetwork (value) {
      if (this.canClick) {
        return
      }
      this.networkType = value
      if (this.networkType == 'computing') {
        this.type = 4
        this.show1 = true
        this.show2 = false
        this.show3 = false
        this.dataMap = 1
        this.showType = 1
        this.color = '#26adff'
        this.restart = false
      } else {
        this.show1 = false
        this.show2 = true
        this.show3 = false
        this.dataMap = 2
        this.restart = false
        this.showType = 0
      }
    },
    getAicenter () {
      getAicenter({ sortBy: 'weight', orderBy: 'asc' }).then((res) => {
        // this.statusData = { connected: 0, accessing: 0, pending: 0 };
        // this.mapData = [];
        // this.totalNum.centerTotal = res.aiCenterInfos.length;
        let computingPower1 = 0
        let storageScale1 = 0
        let computingPower2 = 0
        let storageScale2 = 0
        this.totalNum.centerTotal.accessed = 0
        this.totalNum.centerTotal.unaccess = 0
        this.item1 = []
        this.item2 = []
        this.item3 = []
        this.item4 = []
        res.aiCenterInfos.forEach((item) => {
          if (item.connectionState == 3) {
            this.totalNum.centerTotal.accessed++
          } else if (item.connectionState == 2) {
            this.totalNum.centerTotal.unaccess++
          }
        })
        res.aiCenterInfos.forEach((item) => {
          if (item.centerType == '东数西算') {
            this.item3.push(item)
          } else if (item.centerType == '超算中心') {
            this.item2.push(item)
          } else {
            this.item1.push(item)
          }
        })
        this.intelligenceTotal = this.item1.length
        this.superTotal = this.item2.length
        this.eAdnwTotal = this.item3.length
        switch (this.type) {
          case 1:
            this.type1 = true
            this.type2 = false
            this.type3 = false
            this.type4 = false
            this.itemNow = this.item1
            break
          case 2:
            this.type2 = true
            this.type1 = false
            this.type3 = false
            this.type4 = false
            this.itemNow = this.item2
            break
          case 3:
            this.type3 = true
            this.type2 = false
            this.type1 = false
            this.type4 = false
            this.itemNow = this.item3
            break
          default:
            this.type1 = false
            this.type2 = false
            this.type3 = false
            this.type4 = true
            this.item4 = this.item1.concat(this.item2).concat(this.item3)
            this.itemNow = this.item4
        }
        this.mapData = []
        res.aiCenterInfos.forEach((item) => {
          if (item.connectionState == 3) {
            (computingPower1 = computingPower1 + item.computeScale),
            (storageScale1 = storageScale1 + item.storageScale)
          }
          if (item.connectionState != 3) {
            (computingPower2 = computingPower2 + item.computeScale),
            (storageScale2 = storageScale2 + item.storageScale)
          }
        })
        this.total =
          this.statusData.connected +
          this.statusData.accessing +
          this.statusData.pending
        this.totalNum.computingPower1 = computingPower1
        this.totalNum.storageScale1 = storageScale1
        this.totalNum.computingPower2 = computingPower2
        this.totalNum.storageScale2 = storageScale2
      })
    },
    getTrainJob () {
      getTrainJob({ pageIndex: this.pageIndex, pageSize: this.pageSize }).then(
        (res) => {
          this.tableData = []
          res.otJobs ? res.otJobs : []
          res.otJobs.forEach((item) => {
            if (this.showUnderTaker(item) !== '') {
              this.tableData.push({
                name: item.name,
                status: item.status,
                undertaker: this.showUnderTaker(item)
              })
            }
          })
          this.desMap = getConfig()
          this.destination = []
          res.otJobs.forEach((item) => {
            if (
              item.tasks[0] !== null &&
              item.tasks[0].centerName != null &&
              item.tasks[0].centerName[0]
            ) {
              this.destination.push(item.tasks[0].centerName[0])
            }
          })
          this.destination = Array.from(new Set(this.destination))
          this.desMap.lines = this.desMap.lines.filter((item) => {
            if (this.destination.indexOf(item.target) !== -1) {
              return item
            }
          })
          this.show = true
        }
      )
    },
    showUnderTaker (item) {
      if (item.tasks == null) {
        return ''
      } else if (item.tasks.length > 2) {
        return item.tasks[0].centerName[0] + '等'
      } else {
        if (item.tasks[0].centerName == null) {
          return ''
        } else {
          return item.tasks[0].centerName[0]
        }
      }
    },
    getTaskTotal () {
      getDynamic().then((res) => {
        if (res) {
          if (res.allCardRunTime && res.allJobCount && res.allJobRunTime) {
            const data = res
            this.accrueData = {
              config1: data.allCardRunTime,
              config2: data.allJobCount,
              config3: data.allJobRunTime
            }
          }
        }
      })
    },
    getAccrueCenter () {
      getAccrueCenter().then((res) => {
        this.centerData = { used: [], xData: [] }
        if (res.perCenterComputerPowers) {
          const data = res.accOtJobInfo
          const data1 = res.perCenterComputerPowers.filter((item) => {
            if (item.computerPower !== 0) {
              return item
            }
          })
          data1.sort(order)
          data1.forEach((item) => {
            if (item.computerPower !== 0) {
              this.centerData.used.push(item.computerPower.toFixed(1))
              this.centerData.xData.push(item.centerName)
            }
          })
        }
      })
    },
    getConnected () {
      getConnected().then((res) => {
        this.connectedData = { used: [], xData: [] }
        if (res.computerPowerInfos !== null) {
          res.computerPowerInfos.forEach((item) => {
            item.monthTime = item.monthTime
              .replace(/-/g, '.')
              .replace('.0', '.')
          })
        }
        if (res.computerPowerInfos !== null) {
          res.computerPowerInfos.forEach((item, index, array) => {
            if (
              item.monthAccPower == 0 &&
              array[index + 1].monthAccPower != 0
            ) {
              this.connectedData.used.push(item.monthAccPower)
              this.connectedData.xData.push(item.monthTime)
            }
            if (item.monthAccPower !== 0) {
              this.connectedData.used.push(item.monthAccPower)
              this.connectedData.xData.push(item.monthTime)
            }
          })
        }
      })
    },
    showTip (e) {
      if (this.show2 == false) {
        if (e.showTip == true) {
          this.show1 = false
          this.show3 = true
          this.tipData = JSON.parse(JSON.stringify(e))
        }
      }
    },
    hiddenTip (e) {
      if (this.show2 == false) {
        if (e == false) {
          this.show1 = true
          this.show3 = false
          this.show2 = false
        }
      }
    },
    hiddenNavbar () {
      var target = document.getElementById('navbarToggleExternalContent')
      target.className = 'collapse'
    },
    changeShow (val) {
      if (this.canClick) {
        return
      }
      this.showType = val
      if (val == 1) {
        this.blue = true
        this.green = false
        this.orange = false
      } else if (val == 2) {
        this.blue = false
        this.green = true
        this.orange = false
      } else {
        this.blue = false
        this.green = false
        this.orange = true
      }
    },
    handleEvent (val) {
      this.isShow = val
      this.canClick = false
    },
    clearTimer () {
      this.timer && clearInterval(this.timer)
      this.timer2 && clearInterval(this.timer2)
      this.timer3 && clearInterval(this.timer3)
    },
    onSubmit () {
      if (this.formInline.password == '') {
        this.$message({
          message: '密码不能为空',
          type: 'warning'
        })
      } else {
        if (this.formInline.password == 'c2netPcl') {
          this.showContent = true
          localStorage.setItem('c2net', 'c2netPcl')
          this.getTaskTotal()
          this.getAicenter()
          this.getTrainJob()
          this.getAccrueCenter()
          this.getConnected()
        } else {
          if (this.num <= 2) {
            this.num = this.num + 1
            this.$message({
              message: '密码错误',
              type: 'warning'
            })
          } else {
            window.location.href = 'https://zslm.openi.org.cn/'
          }
        }
      }
    },
    getNewData () {
      var nowTemp = new Date().getTime() // 获取当前时间戳
      var tomorrowTemp = new Date(
        moment().add(1, 'days').format().substr(0, 10) + ' 00:00:00'
      ).getTime() // 获取今天24：00的时间戳
      var residueTemp = tomorrowTemp - nowTemp // 距离当天24：00的时间戳
      setTimeout(() => {
        // location.reload();
        // 次天0点 执行每天24;00 刷新
        setTimeout(() => {
          location.reload()
        }, 60000)
      }, residueTemp)
    },
    handleChildEvent (data) {
      this.statusData.connected = data.connected
      this.statusData.accessing = data.accessing
      this.statusData.pending = data.pending
    }
  },
  beforeDestroy () {}
}
</script>
<style scoped>
.picWrapper {
  position: absolute;
  top: 1120px;
  height: 80px;
  width: 100%;
  z-index: 10;
}
.footerLogo {
  /* margin: 0px 220px 0px 220px; */
  background-image: url("../assets/footer-logo.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  min-height: 60px;
  width: 80%;
  margin: 0 auto;
}
.footerBg {
  /* margin: 0px 220px 0px 220px; */
  background-image: url("../assets/footer-bg.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  min-height: 50px;
  width: 88%;
  margin: 0 auto;
}
.el-header {
  height: 4rem !important;
}

.background {
  min-width: 1850px;
  width: 100%;
  position: relative;
  background-color: #0d0d29;
  height: 100%;
  height: 100%;
}
.title {
  margin-top: 15px;
  font-size: 45px;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  /* transform: translateY(-50%); */
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
}

/* .subTitle {
    margin-top: 1px;
    font-family: "思源黑体 CN-Regular";
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0px;
    line-height: 20px;
    text-align: center;
    width: 100%;
    font-style: italic;
    font-weight: normal;
    filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  } */

.subTitle span {
  letter-spacing: 1px;
}

.title1 {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 20px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0px;
  text-align: center;
  height: 45px;
  width: 100%;
}
.titleBg1 {
  background-image: url("../assets/titleBg.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 100%;
  background-position: 0px 3px;
}
.titleBg2 {
  background-image: url("../assets/titleBg.svg");
  background-size: 110% 100%;
  background-repeat: no-repeat;
  width: 90%;
  margin: 0 auto;
  background-position: -18px 3px;
}
.titleBg3 {
  background-image: url("../assets/titleBg2.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 90%;
  margin: 0 auto;
  background-position: 0px 3px;
}
.province {
  width: 100px;
  height: 100px;
}
.el-main {
  position: relative;
  width: 100%;
  z-index: 1;
  padding: 0px;
  height: 100vh;
  min-height: 1200px;
  overflow: hidden;
}
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}
.el-container.is-vertical {
  height: 100%;
}

.showMt {
  margin-top: 10px;
  border: 2px solid rgb(26, 25, 111);
  border-radius: 20px;
  background: rgba(6, 6, 18, 0.6);
  padding: 10px;
  z-index: 10;
}

.buttonStyle {
  height: 50px;
  line-height: 50px;
  color: white;
  text-align: center;
  background-color: rgba(255, 255, 255, 10%);
  cursor: pointer;
  min-width: 200px;
  border: 2px solid rgba(0, 0, 0, 0.25);
  box-sizing: border-box;
  font-weight: 800;
}

.check {
  height: 50px;
  font-weight: 800;
  line-height: 50px;
  color: #ffd300;
  border: 2px solid rgb(50, 197, 255);
  box-sizing: border-box;
}

.footer {
  background-image: url("../assets/footer.svg");
  background-repeat: no-repeat;
  /* min-height: 20px !important; */
  background-size: 100% 100%;
}

.wrapper {
  position: absolute;
  left: 25px;
}

.icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.icon2 {
  display: inline-block;
  width: 25px;
  height: 25px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.text {
  display: inline-block;
  line-height: 18px;
  height: 18px;
  position: relative;
  top: -2px;
  margin-right: 2px;
}

.img1 {
  background-image: url("../static/screen1/intellectual.svg");
}

.img2 {
  background-image: url("../static/screen1/super.svg");
}

.img3 {
  background-image: url("../static/screen1/eandw.svg");
}

.img4 {
  background-image: url("../assets/statusEd.svg");
}

.img5 {
  background-image: url("../assets/statusIng.svg");
}

.img6 {
  background-image: url("../assets/statusUn.svg");
}

.buttonStyle2 {
  /* height: 50px; */
  color: white;
  /* line-height: 50px; */
  /* text-align: center; */
  /* background-color: rgba(255, 255, 255, 10%); */
  cursor: pointer;
  min-width: 150px;
  margin-bottom: 10px;
}

.check2 {
  color: #fff !important;
  background-image: linear-gradient(
    to right,
    rgba(24, 144, 255, 0%),
    rgba(24, 144, 255, 10%),
    rgba(24, 144, 255, 40%)
  );
}

.statusWrapper {
  position: absolute;
  right: 220px;
  top: 560px;
}

.statusWrapper2 {
  position: absolute;
  right: 190px;
  top: 630px;
}
.statusWrapper1 {
  position: absolute;
  right: 0px;
  top: 580px;
}
.title2 {
  color: rgba(255, 255, 255, 0.7);
  line-height: 40px;
}

.title3 {
  line-height: 40px;
  color: rgba(255, 255, 255, 0.7);
  width: 140px;
}

.arrow1 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrow10TB.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow2 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowSD.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow3 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowMPLS.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.mt-2 {
  margin-top: 20px;
}

.ml-2 {
  margin-left: 65px;
}

.smalltitle {
  letter-spacing: 0px;
  font-size: 32px;
}
.dropdown {
  width: 225px;
  position: fixed;
  right: 30px;
  top: 5px;
  z-index: 200;
  pointer-events: auto;
}
.worldPos {
  position: absolute;
  top: 0px;
  width: 100%;
  /* z-index: 0; */
  height: 100%;
  z-index: -10;
  min-height: 1100px;
  overflow: hidden;
}
.notReady {
  position: absolute;
  top: 0px;
  width: 100%;
  /* z-index: 0; */
  height: 100%;
  z-index: -9;
  min-height: 1100px;
  overflow: hidden;
  background: #0d0d29;
}
.contentWrapper {
  z-index: 1;
  position: relative;
}
.titleBg {
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100px;
  background-image: url("../static/screen1/top-bg.svg");
  background-size: 100%;
  background-repeat: no-repeat;
  z-index: 2;
  background-position-y: -30px;
  background-position-x: 10px;
  min-width: 1200px;
}
.leftChart {
  width: 22%;
  position: absolute;
  top: 50px;
  left: 20px;
}
.rightChart {
  width: 22%;
  position: absolute;
  top: 50px;
  right: 20px;
}
.totalWrapper {
  margin-top: 7vh;
  z-index: -10;
  width: 50%;
  margin: 0 auto;
}
.messageWrapper {
  position: relative;
  top: 110px;
  z-index: 10;
  /* min-width: 1200px; */
}
.staticName {
  display: inline-block;
  color: #fff;
  font-size: 14px;
  height: 35px;
  line-height: 35px;
  position: relative;
  top: -1px;
  margin-right: 10px;
  font-weight: 400;
}
.boldStaticName {
  display: inline-block;
  color: #fff;
  font-size: 14px;
  height: 35px;
  line-height: 35px;
  position: relative;
  top: -1px;
  margin-right: 10px;
  font-weight: 800;
}
.dotBlue {
  display: inline-block;
  background-image: url("../assets/icon/iconFlat.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 5px;
}
.dotBlue2 {
  display: inline-block;
  background-image: url("../assets/icon/iconFlat2.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 5px;
}
.dotBlue3 {
  display: inline-block;
  background-image: url("../assets/icon/iconFlat3.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 5px;
}
.dotGreen {
  display: inline-block;
  background-image: url("../assets/icon/iconRect.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 6px;
}
.dotGreen2 {
  display: inline-block;
  background-image: url("../assets/icon/iconRect2.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 6px;
}
.dotGreen3 {
  display: inline-block;
  background-image: url("../assets/icon/iconRect3.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 6px;
}
.dotOrange {
  display: inline-block;
  background-image: url("../assets/icon/iconDot.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 6px;
}
.dotOrange2 {
  display: inline-block;
  background-image: url("../assets/icon/iconDot2.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 6px;
}
.dotOrange3 {
  display: inline-block;
  background-image: url("../assets/icon/iconDot3.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 15px;
  height: 20px;
  margin-right: 10px;
  position: relative;
  top: 6px;
}
.connectedDot {
  display: inline-block;
  border: 2px solid #fff;
  border-radius: 50%;
  /* background: #26adff; */
  width: 14px;
  height: 14px;
  margin-right: 10px;
}
.connectingDot {
  display: inline-block;
  border: 2px solid yellow;
  border-radius: 50%;
  /* background: #f7b501; */
  width: 14px;
  height: 14px;
  margin-right: 10px;
}
.unconnectDot {
  display: inline-block;
  border: 2px solid transparent;
  border-radius: 50%;
  /* background: #26adff; */
  width: 14px;
  height: 14px;
  margin-right: 10px;
}
.staticNum {
  display: inline-block;
  color: #fff;
  font-size: 14px;
  height: 35px;
  line-height: 35px;
  position: relative;
  top: -1px;
}
.staticWrapper {
  cursor: pointer;
}
.footerWrapper {
  position: absolute;
  top: 1115px;
  width: 100%;
  z-index: 10;
}
.buttonPos {
  position: relative;
  top: 750px;
}
.loading {
  color: #fff;
  font-size: 20px;
  font-weight: 800;
}
.shade {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 80%;
  background: linear-gradient(rgba(13, 13, 41, 0), #0d0d29);
  z-index: 99;
}
.passwordPos {
  margin-top: 40vh;
}
.passConfirm {
  text-align: center;
}
</style>
