<template>
  <div>
    <div style="width: 100%; margin: 0 auto">
      <div class="chart-container">
        <div id="taskPieChart" ref="pieChart" class="pie-chart"></div>
        <div class="data-summary">
          <div class="data-card">
            <div class="card-title">任务总提交量</div>
            <div class="card-content">{{ msg.totalNum }}</div>
          </div>
          <div class="data-card">
            <div class="card-title">任务总执行量</div>
            <div class="card-content">{{ msg.execNum }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import TEND from '@/components/lineChart'
import { jobDetail } from '@/api/screenService.js'

export default {
  name: 'storage',
  components: { TEND },
  data () {
    return {
      tableData: [],
      timer: null,
      chart: { xAxis: [], yAxis: [] },
      pieChart: null,
      flag: true
    }
  },
  watch: {
    Data: {
      deep: true,
      handler (newVal, oldVal) {
        // console.log(newVal, oldVal);
      }
    },
    msg: {
      deep: true,
      handler (newVal) {
        this.updatePieChart()
      }
    }
  },
  props: {
    msg: {
      type: Object,
      default: () => ({
        totalNum: 0,
        execNum: 0
      })
    },
    Data: {
      type: Object,
      default: () => ({})
    }
  },
  mounted () {
    this.initPieChart()
    window.addEventListener('resize', this.resizeChart)
  },
  beforeDestroy () {
    if (this.pieChart) {
      this.pieChart.dispose()
      this.pieChart = null
    }
    window.removeEventListener('resize', this.resizeChart)
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    initPieChart () {
      this.pieChart = this.$echarts.init(this.$refs.pieChart)
      this.updatePieChart()
    },
    updatePieChart () {
      if (!this.pieChart) return

      const totalNum = this.msg.totalNum || 0
      const execNum = this.msg.execNum || 0
      const pendingNum = totalNum - execNum > 0 ? totalNum - execNum : 0

      // 饼图配置
      const option = {
        backgroundColor: 'transparent',
        title: {
          text: '任务执行情况',
          left: 'center',
          top: '5%',
          textStyle: {
            color: 'rgba(0, 230, 255, 1)',
            fontSize: 18,
            fontWeight: 'normal',
            fontFamily: 'SourceHanSansSC-regular',
            textShadow: '0 0 12px rgba(0, 217, 255, 0.6)'
          },
          padding: [0, 0, 15, 0]
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
          backgroundColor: 'rgba(0, 15, 35, 0.95)',
          borderColor: 'rgba(0, 217, 255, 0.3)',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          extraCssText: 'box-shadow: 0 0 15px rgba(0, 217, 255, 0.2);'
        },
        legend: {
          orient: 'horizontal',
          bottom: '6%',
          itemGap: 30,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.95)',
            fontSize: 13,
            fontFamily: 'SourceHanSansSC-regular',
            textShadow: '0 0 3px rgba(0, 217, 255, 0.2)'
          },
          itemWidth: 15,
          itemHeight: 10,
          icon: 'roundRect',
          data: ['已执行任务', '待执行任务'],
          padding: [0, 0, 5, 0]
        },
        series: [
          {
            name: '任务情况',
            type: 'pie',
            radius: ['40%', '65%'],
            center: ['50%', '52%'],
            roseType: false,
            avoidLabelOverlap: true,
            itemStyle: {
              borderColor: 'rgba(0, 15, 35, 0.9)',
              borderWidth: 2,
              shadowBlur: 25,
              shadowColor: 'rgba(0, 217, 255, 0.5)'
            },
            label: {
              show: true,
              formatter: '{d}%',
              color: 'rgba(255, 255, 255, 1)',
              fontSize: 16,
              fontWeight: 'bold',
              fontFamily: 'PMZDBiaoTi-regular',
              position: 'inside',
              distanceToLabelLine: 15,
              textShadow: '0 0 5px rgba(0, 217, 255, 0.6)'
            },
            emphasis: {
              scale: true,
              scaleSize: 12,
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold',
                textShadow: '0 0 8px rgba(0, 217, 255, 0.8)'
              },
              itemStyle: {
                shadowBlur: 30,
                shadowColor: 'rgba(0, 217, 255, 0.8)'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {
                value: pendingNum,
                name: '待执行任务',
                itemStyle: {
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(0, 217, 255, 0.25)' },
                    { offset: 1, color: 'rgba(0, 123, 255, 0.25)' }
                  ])
                }
              },
              {
                value: execNum,
                name: '已执行任务',
                itemStyle: {
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(0, 255, 255, 0.95)' },
                    { offset: 1, color: 'rgba(0, 123, 255, 0.95)' }
                  ])
                }
              }
            ],
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
              return Math.random() * 300
            },
            animationDuration: 1500
          }
        ]
      }

      this.pieChart.setOption(option)
    },
    resizeChart () {
      if (this.pieChart) {
        this.pieChart.resize()
      }
    }
  }
}
</script>
<style scoped>
.chart-container {
  display: flex;
  align-items: center;
  height: 240px;
  background-color: rgba(10, 30, 55, 0.15);
  position: relative;
  border-radius: 12px;
  border: 1px solid rgba(0, 217, 255, 0.25);
  box-shadow:
    0 0 25px rgba(0, 0, 0, 0.3),
    inset 0 0 20px rgba(0, 217, 255, 0.05);
  overflow: hidden;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow:
    0 0 30px rgba(0, 0, 0, 0.35),
    inset 0 0 25px rgba(0, 217, 255, 0.08);
  border-color: rgba(0, 217, 255, 0.35);
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right,
    transparent,
    rgba(0, 217, 255, 0.6),
    transparent
  );
  z-index: 1;
}

.chart-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right,
    transparent,
    rgba(0, 217, 255, 0.4),
    transparent
  );
}

.pie-chart {
  flex: 0 0 65%;
  height: 100%;
  position: relative;
  padding: 10px 0;
  z-index: 2;
}

.pie-chart::after {
  content: '';
  position: absolute;
  top: 15%;
  bottom: 15%;
  right: 0;
  width: 1px;
  background: linear-gradient(to bottom,
    transparent,
    rgba(5, 188, 253, 0.7),
    transparent);
  filter: blur(0.5px);
}

.data-summary {
  flex: 0 0 35%;
  height: 85%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 0 15px;
  z-index: 2;
}

.data-card {
  background-color: rgba(10, 30, 55, 0.15);
  border-radius: 10px;
  padding: 12px 10px;
  text-align: center;
  position: relative;
  box-shadow:
    0 5px 15px rgba(0, 0, 0, 0.25),
    inset 0 0 15px rgba(0, 217, 255, 0.05);
  border: 1px solid rgba(0, 217, 255, 0.2);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  margin-bottom: 10px;
  transform: translateZ(0);
}

.data-card:hover {
  transform: translateY(-3px) translateZ(0);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 0 20px rgba(0, 217, 255, 0.15);
  border-color: rgba(0, 217, 255, 0.45);
}

.data-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right,
    transparent,
    rgba(0, 217, 255, 0.6),
    transparent
  );
}

.data-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10%;
  right: 10%;
  height: 1px;
  background: linear-gradient(to right,
    transparent,
    rgba(0, 217, 255, 0.3),
    transparent
  );
}

.card-title {
  color: rgba(255, 255, 255, 0.95);
  font-size: 13px;
  text-align: center;
  font-family: 'SourceHanSansSC-regular', sans-serif;
  margin-bottom: 8px;
  position: relative;
  letter-spacing: 1px;
  font-weight: 500;
  text-shadow: 0 0 3px rgba(0, 217, 255, 0.3);
}

.card-content {
  color: rgba(0, 217, 255, 0.95);
  font-size: 26px;
  text-align: center;
  font-family: 'PMZDBiaoTi-regular', 'Arial', sans-serif;
  text-shadow: 0 0 15px rgba(0, 217, 255, 0.6);
  font-weight: bold;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  line-height: 1.1;
  letter-spacing: 0.5px;
}

.data-card:hover .card-content {
  transform: scale(1.05);
  color: rgba(50, 230, 255, 1);
  text-shadow: 0 0 20px rgba(0, 217, 255, 0.8);
}

/* 添加卡片高亮效果 */
.data-card:nth-child(1)::before,
.data-card:nth-child(2)::before {
  animation: glow 4s infinite alternate;
}

@keyframes glow {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.3;
  }
}

/* 添加内容加载动画 */
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-content {
  animation: countUp 0.8s ease-out forwards;
}

@media screen and (max-width: 768px) {
  .chart-container {
    flex-direction: column;
    height: auto;
    padding: 15px 0;
    min-height: 480px;
  }

  .pie-chart {
    flex: 0 0 320px;
    width: 100%;
    margin-bottom: 10px;
  }

  .pie-chart::after {
    display: none;
  }

  .data-summary {
    flex: 0 0 auto;
    width: 90%;
    margin: 0 auto;
    padding: 15px 0;
  }

  .data-card {
    margin-bottom: 15px;
    padding: 15px 10px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-content {
    font-size: 28px;
  }
}

@media screen and (max-width: 480px) {
  .chart-container {
    min-height: 450px;
  }

  .pie-chart {
    flex: 0 0 280px;
  }

  .data-summary {
    width: 95%;
  }
}
</style>
