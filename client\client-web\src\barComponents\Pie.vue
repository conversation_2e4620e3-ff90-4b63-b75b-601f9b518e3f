<template>
  <div>
    <div id="id" style="width: 100%; height: 235px" ref="echart"></div>
  </div>
</template>
<script>
import { taskDist } from '@/barApi/screenService.js'
export default {
  name: 'pie',
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => [
        { value: 0, name: 'CPU' },
        { value: 0, name: 'GPU' },
        { value: 0, name: 'NPU' },
        { value: 0, name: 'DCU' },
        { value: 0, name: 'ECS' },
        { value: 0, name: 'GCU' },
        { value: 0, name: 'MLU' }
      ]
    },
    title: {
      type: String,
      default: ''
    }
  },
  watch: {},
  created () {
    taskDist().then((res) => {
      if (res) {
        delete res.data
        this.total = res.total
        this.data.forEach((item) => {
          if (item.name === 'CPU') {
            item.value = res.cpu
          }
          if (item.name === 'GCU') {
            item.value = res.gcu
          }
          if (item.name === 'NPU') {
            item.value = res.npu
          }
          if (item.name === 'GPU') {
            item.value = res.gpu
          }
          if (item.name === 'DCU') {
            item.value = res.dcu
          }
          if (item.name === 'ECS') {
            item.value = res.ecs
          }
          if (item.name === 'MLU') {
            item.value = res.mlu
          }
        })
        this.drawLine()
      }
    })
  },
  mounted () {
    window.addEventListener('resize', this.resize)
    window.addEventListener('visibilitychange', this.Refresh)
    this.timer = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('visibilitychange', this.Refresh)
    clearInterval(this.timer)
    this.timer = null
  },
  data () {
    return {
      total: 100,
      option: {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          top: '12%',
          left: '60%',
          itemWidth: 3,
          itemHeight: 16,
          formatter: (name) => {
            let value = 0
            const data = this.data
            const total = this.total
            for (let i = 0, l = data.length; i < l; i++) {
              if (data[i].name === name) {
                if (total && total !== 0) {
                  value = (data[i].value / total) * 100
                  value = value.toFixed(0)
                }
              }
            }
            return `{c|${name}}{a|${value}}{b| %}`
          },
          textStyle: {
            fontSize: 12,
            color: '#fff',
            rich: {
              a: {
                fontSize: 14,
                color: '#ffd408',
                width: 20,
                align: 'right',
                fontWeight: 'bold'
              },
              b: {
                fontSize: 12,
                color: '#CBCDD2',
                fontWeight: 'bold'
              },
              c: {
                fontSize: 14,
                color: '#fff',
                width: 70,
                fontWeight: 'bold'
              }
            }
          }
        },
        color: [
          'rgb(22, 93, 255)',
          'rgb(250, 100, 1)',
          'rgb(50, 197, 255)',
          'rgb(98, 54, 255)',
          'rgb(80, 205, 137)',
          'rgb(255, 199, 0)',
          'rgb(188, 196, 208)'
        ],
        series: [
          {
            type: 'pie',
            radius: ['65%', '80%'],
            avoidLabelOverlap: false,
            right: '45%',
            label: {
              show: true,
              position: 'center',
              formatter: (param) => {
                const total = this.total
                return `{a|任务总数}\n{b|${total}}`
              },
              rich: {
                a: {
                  color: '#fff',
                  fontSize: 12,
                  padding: [0, 0, 10, 0],
                  fontWeight: 'bold'
                },
                b: {
                  color: '#ffd408',
                  fontSize: 22,
                  fontWeight: 'bold'
                }
              }
            },
            emphasis: {
              label: {
                show: true
              }
            },
            labelLine: {
              show: false
            },
            data: this.data
          }
        ]
      },
      timer: null
    }
  },
  methods: {
    drawLine () {
      if (this.myChart) {
        this.myChart && this.myChart.dispose()
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      const option = this.option
      option && this.myChart.setOption(option)
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    }
  }
}
</script>
<style scoped></style>
