import axios from 'axios'
import axiosRetry from 'axios-retry'
// import { Message } from 'element-ui'
// import store from '@/store'
// import { removeToken } from '@/utils/auth'
// import router from '../router'
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API2, // url = base url + request url  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 120000 // request timeout
})
axiosRetry(service, {
  retries: 5, // 设置自动发送请求次数
  retryDelay: () => 1000, // 重新请求的间隔
  shouldResetTimeout: true, //  重置超时时间
  retryCondition: (error) => { // true为打开自动发送请求，false为关闭自动发送请求
    if (error.message.includes('timeout')) return true
    // 如果你要在请求出错的时候重新发送请求（返回400以上的状态码时） 你应该像下面这样写
    if (error.message.includes('timeout') || error.message.includes('status code')) return true
  }
})
// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    // if (store.getters.token) {
    //   config.headers['Authorization'] = 'Bearer ' + getToken()
    // }
    // eslint-disable-next-line no-undef
    return config
  },
  error => {
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */

  response => {
    // if (!response.data.success && (response.data.error.subcode === 16004 || response.data.error.subcode === 16010 || response.data.error.subcode === 16007)) {
    //   // setTimeout(function() {
    //   //   removeToken()
    //   //   router.replace({ path: '/' })
    //   // }, 1000)
    //   return res
    // } else {
    //   return res
    // }
    if (response && response.data) { return response.data }
  },
  error => {
    console.log('err' + error) // for debug
    // Message({
    //   message: error.message,
    //   type: 'error',
    //   duration: 5 * 1000
    // })
    return Promise.reject(error)
  }
)

export default service
