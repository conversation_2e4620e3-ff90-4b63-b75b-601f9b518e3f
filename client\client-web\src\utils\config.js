export function centerMsg () {
  return {
    points: [
      {
        name: '算力调度中心',
        coordinate: [0.59, 0.6],
        halo: {
          show: true
        },
        text: {
          show: false
        }
      },
      {
        name: '昇腾沈阳智算中心',
        coordinate: [0.70, 0.27]

      },
      {
        name: '昇腾大连智算中心',
        coordinate: [0.69, 0.31]
      },
      {
        name: '昇腾青岛智算中心',
        coordinate: [0.69, 0.34]
      },
      {
        name: '济南智算',
        coordinate: [0.63, 0.37]
      },
      {
        name: '济南超算',
        coordinate: [0.64, 0.35]
      },
      {
        name: '昇腾海南智算中心',
        coordinate: [0.53, 0.66]
      },
      {
        name: '昇腾南宁智算中心',
        coordinate: [0.52, 0.61]
      },
      {
        name: '昇腾昆明智算中心',
        coordinate: [0.42, 0.58]
      },
      {
        name: '昇腾福州智算中心',
        coordinate: [0.65, 0.55]
      },
      {
        name: '贵安集群',
        coordinate: [0.498, 0.54]
      },
      {
        name: '昇腾长沙智算中心',
        coordinate: [0.55, 0.52]
      },
      {
        name: '长沙超算',
        coordinate: [0.57, 0.52]
      },
      {
        name: '成渝枢纽集群',
        coordinate: [0.5, 0.49]
      },
      {
        name: '昇腾重庆智算中心',
        coordinate: [0.52, 0.47]
      },
      {
        name: '成都超算',
        coordinate: [0.44, 0.47]
      },
      {
        name: '昇腾成都智算中心',
        coordinate: [0.432, 0.46]
      },
      {
        name: '甘肃集群',
        coordinate: [0.46, 0.39]
      },
      {
        name: '宁夏枢纽集群',
        coordinate: [0.48, 0.36]
      },
      {
        name: '昇腾武汉智算中心',
        coordinate: [0.573, 0.475]
      },
      {
        name: '昇腾中原智算中心',
        coordinate: [0.575, 0.42]
      },
      {
        name: '郑州超算',
        coordinate: [0.58, 0.4]
      },
      {
        name: '内蒙古集群',
        coordinate: [0.56, 0.3]
      },
      {
        name: '寒武纪西安中心',
        coordinate: [0.525, 0.42]
      },
      {
        name: '西安超算',
        coordinate: [0.51, 0.428]
      },
      {
        name: '昇腾西安智算中心',
        coordinate: [0.51, 0.41]
      },
      {
        name: '沣东智算',
        coordinate: [0.52, 0.40]
      },
      {
        name: '京津冀集群',
        coordinate: [0.60, 0.29]
      },
      {
        name: '北大分中心',
        coordinate: [0.61, 0.29]
      },
      {
        name: '昇腾河北智算中心',
        coordinate: [0.605, 0.31]
      },
      {
        name: '北京智算',
        coordinate: [0.62, 0.28]
      },
      {
        name: '天津市AI计算中心',
        coordinate: [0.619, 0.325]
      },
      {
        name: '天津超算',
        coordinate: [0.63, 0.305]
      },
      {
        name: '长三角枢纽集群',
        coordinate: [0.64, 0.47]
      },
      {
        name: '合肥类脑',
        coordinate: [0.63, 0.44]
      },
      {
        name: '寒武纪合肥智算',
        coordinate: [0.63, 0.45]
      },
      {
        name: '昇腾杭州智算中心',
        coordinate: [0.66, 0.5]
      },
      {
        name: '昇腾宁波智算中心',
        coordinate: [0.68, 0.49]
      },
      {
        name: '昆山超算',
        coordinate: [0.68, 0.47]
      },
      {
        name: '寒武纪南京智算',
        coordinate: [0.67, 0.455]
      },
      {
        name: '昇腾南京智算中心',
        coordinate: [0.66, 0.457]
      },
      {
        name: '无锡超算',
        coordinate: [0.665, 0.45]
      },
      {
        name: '寒武纪昆山中心',
        coordinate: [0.68, 0.46]
      },
      {
        name: '新疆大学智算中心',
        coordinate: [0.245, 0.234]

      },
      {
        name: '曙光云平台',
        coordinate: [0.672, 0.48]
      },
      {
        name: '天数智算中心',
        coordinate: [0.695, 0.48]
      },
      {
        name: '昇腾上海智算中心',
        coordinate: [0.695, 0.465]
      },
      {
        name: '北京超算云计算中心',
        coordinate: [0.62, 0.29]
      },
      {
        name: '昇腾长春智算中心',
        coordinate: [0.74, 0.218]
      },
      {
        name: '燧原智算',
        coordinate: [0.55, 0.478]
      },
      {
        name: '川投绿色智算中心',
        coordinate: [0.44, 0.49]
      },
      {
        name: '启智混合智算集群',
        coordinate: [0.575, 0.61]

      },
      {
        name: '寒武纪横琴智算',
        coordinate: [0.56, 0.618]

      },
      {
        name: '昇腾广州智算中心',
        coordinate: [0.57, 0.615]

      },
      {
        name: '鹏城云脑二',
        coordinate: [0.578, 0.588]

      },
      {
        name: '鹏城云脑一',
        coordinate: [0.59, 0.615]

      },
      {
        name: '启智燧原集群',
        coordinate: [0.6, 0.59]

      },
      {
        name: '鹏城云计算所GPU集群',
        coordinate: [0.60, 0.60]

      },
      {
        name: '天数云',
        coordinate: [0.65, 0.368]

      },
      {
        name: '天翼云集群',
        coordinate: [0.61, 0.34]
      },
      {
        name: '天翼云',
        coordinate: [0.56, 0.29]

      },
      // 超算中心
      {
        name: '广州超算',
        coordinate: [0.567, 0.612]
      },
      {
        name: '深圳超算',
        coordinate: [0.586, 0.612]
      },
      {
        name: '长沙超算',
        coordinate: [0.567, 0.52]
      },
      {
        name: '成都超算',
        coordinate: [0.44, 0.475]
      },
      {
        name: '西安超算',
        coordinate: [0.506, 0.43]
      },
      {
        name: '郑州超算',
        coordinate: [0.58, 0.405]
      },
      {
        name: '济南超算',
        coordinate: [0.638, 0.35]
      },
      {
        name: '无锡超算',
        coordinate: [0.663, 0.46]
      },
      {
        name: '昆山超算',
        coordinate: [0.68, 0.465]
      },
      // 东数西算
      {
        name: '大湾区枢纽集群',
        coordinate: [0.592, 0.59]
      },
      {
        name: '贵安集群',
        coordinate: [0.498, 0.535]
      },
      {
        name: '成渝枢纽集群',
        coordinate: [0.5, 0.49]
      },
      {
        name: '甘肃集群',
        coordinate: [0.467, 0.39]
      },
      {
        name: '宁夏枢纽集群',
        coordinate: [0.475, 0.358]
      },
      {
        name: '内蒙古集群',
        coordinate: [0.54, 0.281]
      },
      {
        name: '京津冀集群',
        coordinate: [0.594, 0.285]
      },
      {
        name: '长三角枢纽集群',
        coordinate: [0.636, 0.47]
      }
    ],
    lines: [
      {
        source: '算力调度中心',
        target: '昇腾沈阳智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾大连智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾青岛智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '济南智算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '济南超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾海南智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾南宁智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾昆明智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾福州智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '贵安集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾长沙智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '长沙超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '成渝枢纽集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾重庆智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '成都超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾成都智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '甘肃集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '宁夏枢纽集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾武汉智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾中原智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '郑州超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '内蒙古集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪西安中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '西安超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾西安智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '沣东智算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '京津冀集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '北大分中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾河北智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '北京智算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '天津市AI计算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '天津超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '长三角枢纽集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪合肥智算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '合肥类脑',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾杭州智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾宁波智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昆山超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪南京智算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾南京智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '无锡超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪昆山中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '新疆大学智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '天数智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾上海智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '北京超算云计算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '曙光云平台',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾长春智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '燧原智算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '川投绿色智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '启智混合智算集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '寒武纪横琴智算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昇腾广州智算中心',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '鹏城云脑一',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '鹏城云脑二',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '鹏城云计算所GPU集群',
        width: 0,
        duration: [30, 30]
        // radius: 0.1
      },
      {
        source: '算力调度中心',
        target: '启智燧原集群',
        width: 0,
        duration: [30, 30]
        // radius: 0.1
      },
      {
        source: '算力调度中心',
        target: '天翼云集群',
        width: 0,
        duration: [30, 30]
        // radius: 0.1
      },
      // 超算中心
      {
        source: '算力调度中心',
        target: '广州超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '深圳超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '长沙超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '成都超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '郑州超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '济南超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '西安超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '无锡超算',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '昆山超算',
        width: 0,
        duration: [30, 30]
      },
      // 东数西算
      {
        source: '算力调度中心',
        target: '大湾区枢纽集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '贵安集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '成渝枢纽集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '甘肃集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '宁夏枢纽集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '内蒙古集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '京津冀集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '长三角枢纽集群',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '天数云',
        width: 0,
        duration: [30, 30]
      },
      {
        source: '算力调度中心',
        target: '天翼云',
        width: 0,
        duration: [30, 30]
      }
    ]
  }
}
export function cityMsg () {
  return [
    { name: '昇腾广州智算中心', x: 642, y: 570 },
    { name: '鹏城云脑一', x: 665, y: 570 },
    { name: '鹏城云脑二', x: 652, y: 544 },
    { name: '川投绿色智算中心', x: 500, y: 445 },
    { name: '燧原智算', x: 620, y: 435 },
    { name: '昇腾长春智算中心', x: 838, y: 193 },
    { name: '天数智算中心', x: 785, y: 440 },
    { name: '昇腾南宁智算中心', x: 585, y: 560 },
    { name: '昇腾杭州智算中心', x: 746, y: 456 },
    { name: '河北人工智能计算中心', x: 684, y: 280 },
    { name: '昆山超算', x: 770, y: 422 },
    { name: '内蒙古集群', x: 610, y: 250 },
    { name: '宁夏枢纽集群', x: 533, y: 320 },
    { name: '西安超算', x: 570, y: 388 },
    { name: '郑州超算', x: 655, y: 366 },
    { name: '昇腾天津智算中心', x: 711, y: 290 },
    { name: '昇腾南宁智算中心', x: 585, y: 560 },
    { name: '昆山超算', x: 770, y: 422 },
    { name: '昇腾昆明智算中心', x: 467, y: 530 },
    { name: '京津冀集群', x: 672, y: 255 },
    { name: '内蒙古集群', x: 610, y: 250 },
    { name: '宁夏枢纽集群', x: 533, y: 320 },
    { name: '甘肃集群', x: 525, y: 355 },
    { name: '昇腾上海智算中心', x: 785, y: 427 },
    { name: '大湾区枢纽集群', x: 655, y: 540 },
    { name: '深圳超算', x: 660, y: 565 },
    { name: '天津市AI计算中心', x: 700, y: 284 },
    { name: '昇腾福州智算中心', x: 730, y: 502 },
    // { name: "昇腾广州智算中心", x: 648, y: 570 },
    { name: '昇腾西安智算中心', x: 573, y: 375 },
    { name: '昇腾武汉智算中心', x: 645, y: 437 },
    { name: '济南智算', x: 722, y: 315 },
    { name: '昇腾青岛智算中心', x: 780, y: 315 },
    { name: '寒武纪横琴中心', x: 632, y: 567 },
    { name: '济南超算', x: 722, y: 315 },
    { name: '鹏城云计算所集群', x: 677, y: 556 },
    { name: '合肥类脑', x: 710, y: 404 },
    { name: '昇腾中原智算中心', x: 648, y: 386 },
    { name: '昇腾成都智算中心', x: 484, y: 423 },
    { name: '沣东智算', x: 585, y: 386 },
    { name: '寒武纪昆山中心', x: 770, y: 420 },
    { name: '寒武纪南京智算', x: 758, y: 409 },
    { name: '北大分中心', x: 690, y: 268 },
    { name: '寒武纪西安中心', x: 590, y: 380 },
    { name: '寒武纪合肥智算', x: 710, y: 410 },
    { name: '天翼云集群', x: 690, y: 305 },
    { name: '天数云', x: 735, y: 330 },
    { name: '北京超算云计算中心', x: 700, y: 260 },
    { name: '新疆大学智算中心', x: 269, y: 208 },
    { name: '曙光云平台', x: 760, y: 435 },
    { name: '启智混合智算集群', x: 649, y: 565 },
    { name: '启智燧原集群', x: 677, y: 547 },
    { name: '昇腾宁波智算中心', x: 769, y: 452 },
    { name: '广州超算', x: 792, y: 240 },
    { name: '昇腾沈阳智算中心', x: 792, y: 243 },
    { name: '昇腾南京智算中心', x: 745, y: 423 },
    { name: '昇腾重庆智算中心', x: 585, y: 432 },
    { name: '天翼云', x: 630, y: 255 },
    { name: '昇腾大连智算中心', x: 780, y: 282 }
  ]
}
