'use strict'
module.exports = {
  publicPath: './',
  outputDir: 'dist',
  assetsDir: './',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: process.env.NODE_ENV !== 'production',
  pwa: {
    iconPaths: {
      favicon32: '',
      favicon16: '',
      appleTouchIcon: '',
      maskIcon: '',
      msTileImage: ''
    }
  },
  devServer: { // 环境配置
    hot: true, // 开启模块热替换
    port: 8080, // 指定端口
    host: '0.0.0.0', // 指定地址  可以是0.0.0.0也可以是localhost
    compress: true,
    open: true,
    openPage: '#/c2net', // 设置默认打开的页面路径
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: 'http://***************:30009',
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: '/'
        }
      },
      [process.env.VUE_APP_BASE_API3]: {
        target: 'http://**************:8989',
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API3]: '/'
        }
      },
      [process.env.VUE_APP_BASE_API2]: {
        target: 'http://***************',
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API2]: '/urchin'
        }
      }
    }

  },
  chainWebpack: (config) => {
    config.plugin('html').tap((args) => {
      args[0].title = '算力网仿真大屏'
      return args
    })
  }

}
