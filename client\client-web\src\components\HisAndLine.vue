<template>
  <div :id="id" style="width: 100%; height: 285px" ref="echart"></div>
</template>
<script>
export default {
  name: 'histogram',
  data () {
    return {
      timer: null,
      // timer2: null,
      Data: { unused: [], used: [], xData: [] },
      barWidth: 20
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        const TempData = JSON.parse(JSON.stringify(newValue))
        this.Data.used = TempData.used
        this.Data.xData = TempData.xData
        // this.barWidth = 10;
        this.drawLine()
      },
      deep: true
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.drawLine()
    })
    window.addEventListener('resize', this.resize)
    window.addEventListener('visibilitychange', this.Refresh)
    this.timer = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('visibilitychange', this.Refresh)
    clearInterval(this.timer)
    this.timer = null
    // clearInterval(this.timer2);
    // this.timer2 = null;
  },
  destroyed () {
    // clearInterval(this.timer);
  },
  methods: {
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      if (!this.myChart) {
        this.myChart = this.$echarts.init(this.$refs.echart)
      }
      var option
      option = {
        title: {
          text:
            this.Data.used.length === 0 && this.Data.xData.length === 0
              ? '暂无数据'
              : '',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10]
        },
        color: '#32c5ff ',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid',
              color: 'rgba(0, 0, 0, 0)'
            }
          },
          formatter: function (params) {
            params = [params[0]]
            let htmlStr = ''
            htmlStr += '<div>'
            htmlStr += '<div>'
            htmlStr += params[0].axisValue
            htmlStr += '</div>'
            htmlStr +=
              '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
              '#1890ff' +
              ';"></span>'
            htmlStr += params[0].seriesName + '：' + params[0].value
            htmlStr += '</div>'
            return htmlStr
          },
          backgroundColor: '#000033',
          textStyle: { color: 'rgba(255, 255, 255,0.9)' },
          borderWidth: 0
        },
        legend: {
          right: '0',
          itemWidth: 15,
          textStyle: { color: 'rgba(255, 255, 255,0.9)' },
          icon: 'circle'
        },
        grid: {
          right: '7%',
          bottom: '15%',
          top: '22%',
          left: this.config.unit.length > 7 ? '15%' : '8%'
        },
        xAxis: [
          {
            type: 'category',
            data: this.Data.xData,
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(1, 145, 255, 0.3)' // 设置坐标轴颜色
              }
            },
            boundaryGap: false,
            axisLabel: {
              show: true,
              interval: 'auto',
              fontSize: '12',
              lineHeight: 40,
              color: 'rgba(255, 255, 255,0.9)',
              fontFamily: 'Microsoft YaHei',
              fontWeight: 'bold',
              showMaxLabel: true
            },
            axisTick: {
              // x轴刻度相关设置
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                type: 'dotted',
                color: 'rgba(1, 145, 255, 0.3)'
              }
            },
            name: this.config.unit,
            nameTextStyle: {
              // 关键代码
              padding:
                this.config.unit.length > 7 ? [0, 0, 25, 40] : [0, 0, 0, 0]
            },
            data: [],
            axisLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255,0.9)'
              }
            },
            axisLabel: {
              show: true,
              fontSize: '10',
              fontWeight: 'bolder',
              color: 'rgba(255, 255, 255,0.9)'
            },
            scale: true,
            min: 0,
            splitNumber: 3
          },
          {
            type: 'value',
            splitLine: { show: false },
            axisLabel: {
              formatter: function (params) {
                return ''
              }
            }
          }
        ],
        series: [
          {
            name: this.config.status[0] ? this.config.status[0] : '',
            type: 'line',
            stack: 'total',
            // barWidth: this.barWidth,
            areaStyle: {},
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            zlevel: 1,
            z: 1,
            data: this.Data.used ? this.Data.used : [],
            smooth: true,
            symbol: 'none',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(50,197,255,0)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgb(22,93,255)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 100
        },
        animationEasing: 'backln'
      }
      option && this.myChart.setOption(option)
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    }
  }
}
</script>
