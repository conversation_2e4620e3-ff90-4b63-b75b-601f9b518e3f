<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-row type="flex" justify="center">
          <el-col :span="8">
            <el-row type="flex" justify="center">
              <el-col :span="5">
                <div class="circle1"></div>
              </el-col>
              <el-col :span="18">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="12">
                        <div>算力中心总数</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="4">
                      <div class="num">{{ data.centerTotal }}</div>
                    </el-col>
                    <el-col :span="12">
                      <div class="unit">个</div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="8">
            <el-row type="flex" justify="center">
              <el-col :span="4">
                <div class="circle2"></div>
              </el-col>
              <el-col :span="20">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="20">
                        <div>已接入/待接入算力</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="9">
                      <div class="num">
                        <div class="num">
                          {{
                            data.computingPower1 + "/" + data.computingPower2
                          }}
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="unitBg">POps@FP16</div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="8">
            <el-row type="flex" justify="center">
              <el-col :span="4">
                <div class="circle3"></div>
              </el-col>
              <el-col :span="18">
                <div class="content">
                  <div class="title">
                    <el-row>
                      <el-col :span="20">
                        <div>已接入/待接入存储</div>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                    <el-col :span="8">
                      <div class="num">
                        {{ data.storageScale1 + "/" + data.storageScale2 }}
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="unit">PB</div>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'total',
  data () {
    return {}
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    statusData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    statusData: {
      handler (newValue, oldValue) {
        this.statusData = newValue
      }
    },
    data: {
      handler (newValue, oldValue) {
        this.data = newValue
      }
    }
  },
  mounted () {},
  methods: {}
}
</script>
<style scoped>
.icon {
  background-image: url(../barStatic/screen1/cloud.png);
  width: 80px;
  height: 80px;
  background-size: 100% 100%;
  background-position-y: 10px;
  background-repeat: no-repeat;
}

.title {
  font-family: "思源黑体 CN-Regular";
  font-size: 20px;
  color: rgb(211, 237, 255);
  letter-spacing: 1px;
  font-weight: normal;
  font-style: normal;
  transform: translate(0px, 0px);
  word-break: keep-all;
}

.content {
  padding-top: 5px;
}

.num {
  text-align: left;
  color: rgb(24, 144, 255);
  font-size: 20px;
  font-family: DINPro-Medium;
  font-weight: normal;
  text-shadow: rgb(19 128 255) 0px 0px 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.unit {
  display: inline-flex;
  align-items: center;
  color: rgb(211, 237, 255);
  font-size: 20px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  padding-left: 2px;
}

.unitBg {
  color: rgb(211, 237, 255);
  font-size: 20px;
  font-family: "思源黑体 CN-Regular";
  letter-spacing: 0px;
  font-weight: normal;
  font-style: normal;
  padding-left: 10px;
}

.arrow {
  background-size: 100% 150%;
  background-image: url("../barStatic/screen1/arrow.png");
  width: 50px;
  height: 30px;
  background-position: 0px -8px;
  background-repeat: no-repeat;
}

.mt {
  margin-top: 20px;
  /* position: relative;
  left: 80px; */
}
.loading-tip1 {
  background-image: url("../barAssets/icon/riLine-map-pin-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip2 {
  background-image: url("../barAssets/icon/riLine-camera-lens-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.loading-tip3 {
  background-image: url("../barAssets/icon/riLine-map-pin-line.svg");
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
}
.circle1 {
  background-image: url("../barStatic/screen1/icon-07.png");
  background-size: 100% 100%;
  width: 45px;
  height: 45px;
  position: absolute;
  top: 6px;
  background-repeat: no-repeat;
  border: 6px solid rgba(50, 197, 255, 0.2);
  box-shadow: 0px 0px 20px rgb(50, 197, 255);
  border-radius: 50%;
}
.circle2 {
 background-image: url("../barStatic/screen1/icon-06.png");
  background-size: 100% 100%;
  width: 45px;
  height: 45px;
  position: absolute;
  top: 6px;
  left: -15px;
  background-repeat: no-repeat;
  border: 6px solid rgba(50, 197, 255, 0.2);
  box-shadow: 0px 0px 20px rgb(50, 197, 255);
  border-radius: 50%;
}
.circle3 {
 background-image: url("../barStatic/screen1/icon-01.png");
  background-size: 100% 100%;
  width: 45px;
  height: 45px;
  position: absolute;
  top: 6px;
  left: 0px;
  background-repeat: no-repeat;
  border: 6px solid rgba(50, 197, 255, 0.2);
  box-shadow: 0px 0px 20px rgb(50, 197, 255);
  border-radius: 50%;
}
</style>
