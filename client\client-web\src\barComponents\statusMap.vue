<template>
    <div style="position: absolute; display: block; width: 190px; height:300px; right: 0px; top: 515px; pointer-events: none;">
        <!-- <div class="wrapper">
            <div :class="{ buttonStyle: true, check: type1,title:true }" ><span
                    class="img1 icon"></span><span class="text">智算中心</span></div>
            <div :class="{ buttonStyle: true, check: type2,title:true }" ><span
                    class="img2 icon"></span><span class="text">超算中心</span></div>
            <div :class="{ buttonStyle: true, check: type3,title:true }"><span
                    class="img3 icon"></span><span class="text">东数西算</span></div>
        </div> -->
    </div>
</template>
<script>
export default {
  name: 'statusMap',
  props: {
    statusData: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      type1: true,
      type2: false,
      type3: false,
      type: 1
    }
  },
  mounted () {
  },
  methods: {
  },
  watch: {
    statusData: {
      handler (newValue, oldValue) {
        this.statusData = newValue
      }
    },
    type: {
      handler (newValue, oldValue) {
        switch (this.type) {
          case 1:
            this.type1 = true
            this.type2 = false
            this.type3 = false
            break
          case 2:
            this.type2 = true
            this.type1 = false
            this.type3 = false
            break
          case 3:
            this.type3 = true
            this.type2 = false
            this.type1 = false
            break
        }
      }
    }

  }
}
</script>
<style scoped>
    .img {
        width: 100%;
        height: 100%;
    }

    .title {
        color: #fff;
        line-height: 40px;
    }

    .wrapper {
        position: absolute;
        left: 25px
    }

    .icon {
        display: inline-block;
        ;
        width: 18px;
        height: 18px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 10px;
        line-height: 18px;

    }

    .text {
        display: inline-block;
        line-height: 18px;
        height: 18px;
        position: relative;
        top: -3px
    }

    .img1 {
        background-image: url('../barStatic/screen1/intellectual.svg');

    }

    .img2 {
        background-image: url('../barStatic/screen1/super.svg');

    }

    .img3 {
        background-image: url('../barStatic/screen1/eandw.svg');

    }

    .buttonStyle {
        /* height: 50px; */
        color: white;
        /* line-height: 50px; */
        /* text-align: center; */
        background-color: rgba(255, 255, 255, 10%);
        cursor: pointer;
        min-width: 120px;
        margin-bottom: 10px;
    }

    .check {
        font-weight: 800;
        background-image: linear-gradient(to right, rgba(24, 144, 255, 0%),
                rgba(24, 144, 255, 10%),
                rgba(24, 144, 255, 40%));
    }
</style>
