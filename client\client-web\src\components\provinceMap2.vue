<template>
  <div>
    <transition name="guangdong">
      <!-- 广东 -->
      <div
        id="guangdong"
        style="
          position: absolute;
          top: 520px;
          pointer-events: none;
          width: 550px;
          height: 280px;
          background-color: rgba(18, 137, 221, 0.1);
          background-size: cover;
          background-position: center center;
          overflow: hidden;
          filter: unset;
          left: 25px;
        "
        v-if="mapArray[1].city"
      >
        <div
          style="
            pointer-events: none;
            position: absolute;
            top: -10px;
            left: -15px;
            width: 446px;
            height: 270px;
            display: block;
            transition: transform 600ms ease 0s;
          "
        >
          <div style="top: -5px; left: 0px; position: absolute">
            <div
              id="78c27851-7f4c-49a7-a24e-aa1422664492"
              style="position: absolute; pointer-events: auto"
            >
              <div
                class="__easyv-component"
                id="15059"
                style="
                  position: absolute;
                  top: 5px;
                  left: 0px;
                  width: 446px;
                  height: 270px;
                  perspective: 446px;
                "
              >
                <div class="map-container" style="position: relative">
                  <svg
                    width="446"
                    height="270"
                    style="
                      position: absolute;
                      zoom: 3;
                      top: -75px;
                      left: -140px;
                    "
                  >
                    <g>
                      <path
                        id="15059_440100"
                        fill="url(#linearGradient_15059)"
                        stroke="RGBA(130,188,255,0.5)"
                        stroke-width="0.5"
                        d="M220.24291561631517, 140.97917727659257L219.84060253802312, 139.94235052539648L221.0835404265326, 139.62106240546788L221.56849103914027, 139.08644745096848L220.67761810822873, 138.4357418918897L220.20340146916368, 138.39256615744807L218.87787291958506, 138.67338160432953L217.75708671254264, 138.04155626000744L217.64215351110084, 136.98312974065854L217.0170987634567, 136.4080224161414L217.2541836463656, 136.22443173594195L217.21106025920068, 135.6655413055815L216.11544498575296, 134.76911605151307L216.53214815084826, 134.4262162276416L216.36687308221863, 133.1059004103753L217.1069079045511, 132.76841470420027L216.49619637046294, 131.8989986024887L215.67000852029602, 131.50749393100782L214.93719217798886, 130.9188969704039L214.84738303689446, 128.9478861152823L215.23891526839589, 128.69677204645353L215.07729631302536, 127.12535640144159L213.70864437628262, 127.27653924225999L213.051247088265, 127.20364793131245L212.30404065915377, 126.54754033540473L211.79396598619792, 126.37204128983689L211.43472942182277, 125.50263070465708L212.2070599112822, 125.13542253148658L212.91475219324192, 125.1543260552345L213.2452085840098, 124.28221294559734L212.88601889288108, 124.33082795834639L212.67766731033225, 123.19949667386271L212.95782870715877, 123.20219794438759L213.04407548148623, 122.14377882971267L212.12803161697843, 122.10060265659051L211.88733749405768, 121.85489598144937L211.92685164120996, 121.3014118690925L212.94704786036735, 120.38607898816757L212.92553304003258, 119.6165700556662L212.7315246710413, 119.15755648705334L212.08490822981508, 118.45553894724297L211.4491195086268, 118.15313656588724L211.40238688145084, 117.3512385611656L211.661033457941, 116.59255945619934L211.45990035541743, 115.58005629585438L210.66957053914288, 114.7970422893407L210.1882291665471, 114.62690531868907L210.19901001333773, 114.21920223580703L209.12851880023746, 113.37411893485147L208.97402457839766, 113.72511841426922L208.0867608874972, 113.97622221596774L208.29511247004524, 114.32183201619836L208.07598004070655, 114.64042865911362L207.37189699875947, 114.62960998892748L207.16715465622326, 114.86720851520032L206.5169289749852, 114.91854421848112L206.96958392046741, 116.0687501118773L206.86899393258238, 116.66275816939985L206.41994822711362, 116.72754849012954L205.63683689086352, 116.49261686984646L205.2345238125707, 116.5547559246739L205.6009319837239, 115.98232049861737L205.53985614299117, 115.21823288386446L205.12315297789428, 114.96171489732548L204.6525455788442, 114.07074099703202L204.8896304617523, 113.09063162216604L204.22867080696852, 112.84490581156555L204.4226323027133, 112.40478220116319L205.16266712504574, 111.50571209356924L204.76030717350645, 111.1870702959039L203.9879766840478, 112.00521588172828L203.718549260767, 111.430099204865L202.65166728767855, 111.32206474103457L202.22062090902566, 110.01795893925969L202.58341984016698, 109.51575503662349L202.6875721948166, 108.87586853432131L203.1797412874503, 108.6868378778172L202.59780992697029, 107.99834568479116L202.3211640236634, 107.25047142724513L202.61932474730506, 106.79956131366394L202.12720252791942, 106.88595487655543L201.9188509453706, 107.2693736409264L201.2650628973664, 107.68245042967078L201.34409119166935, 106.74014274548884L201.86138434464968, 106.45392491107847L201.96553669930086, 105.76004407050804L202.81689548306247, 105.59535185627988L203.34857872284687, 105.84103457793573L203.97363347049102, 105.48190964258013L205.1411054314637, 105.22272279321041L205.74820772553997, 105.33340987872967L206.12178750347118, 104.79344820709304L206.66781395681235, 104.70702783874438L207.06295542832638, 104.19400226673054L208.23764586932435, 104.00499375785901L209.16081446736365, 103.2570854623942L210.32472406157098, 103.75663730513419L211.194035298902, 103.26792234303551L212.13164085699185, 103.14641537590371L212.08851746982774, 102.71440123883838L213.06202793505565, 101.6560001796854L212.97939040074084, 101.19157425741898L212.54112554206267, 100.74066743516474L212.60941986282148, 99.99009460106114L213.18417961652273, 99.59316787907798L213.7696733437689, 99.5175747412734L213.80923436416685, 99.07207961878966L214.653374667904, 99.38796991090192L214.3947280914138, 98.46997485541299L214.7755263493703, 98.70758020863995L216.09388329217091, 97.97043905409019L216.2555491207887, 98.32955610919585L216.74767134017588, 97.91375614300054L217.1643745052712, 96.87153389535251L216.68659549944155, 96.51786491900485L217.70679171859973, 96.57184771971673L218.51863635520988, 96.46654247839052L218.50068390164046, 95.94812795326408L218.8240155588768, 95.90222553199831L218.99285299427336, 95.42435085698163L219.4131185261356, 95.34871067887521L220.30042909028333, 93.14279370261457L220.58419972712173, 93.17248648600604L220.86440799719392, 92.0573806443849L221.7768426216891, 92.10597201200252L222.07856571209612, 92.46781040632432L223.6807401649816, 92.28149193967928L223.93216826144726, 92.85660993615198L224.89133551311917, 93.26161523782693L225.0996870956672, 94.13103173872211L225.6780092161346, 94.48743729272098L227.04666115287733, 94.56841031336745L227.83338172913923, 94.37943761626099L228.6955213596923, 93.85558883564129L228.7637688072038, 93.53969339550673L229.32057610733563, 93.63419228449206L229.75523172600194, 94.17152053019565L229.75523172600194, 94.72231777006209L230.6568855037057, 94.93830430297325L230.65327626369307, 95.25694847581613L231.12388366274467, 95.46483181887223L231.98958566006232, 94.684545000962L231.79923340433095, 94.37672480571716L232.37038391802034, 93.94199548795784L232.31291731729863, 93.69899952566062L233.04212441959396, 93.43710478940449L232.57156389378957, 93.07260558118071L232.7367920891743, 92.24370536732705L232.963096125291, 92.03311042943209L232.69366870200943, 91.67667628181802L232.95953375852562, 91.09889606334511L232.47814551268257, 90.21868739824204L231.96446159971487, 89.58418083942723L232.83377283704513, 88.69316766866659L233.13910516746634, 87.81832810586978L233.67435077401686, 87.51864502179953L234.3640906024064, 87.84804461886739L235.2046685393782, 87.88585612795063L235.34838191442577, 87.2324245641338L236.23925484533885, 87.76704119096667L236.88582441331937, 87.26214369546626L236.63439631685364, 86.75725909801545L236.78528129868081, 86.30091864483032L236.39731143394556, 85.66644827472409L236.57332047611933, 85.34780648441081L237.51809764098797, 85.73123058038297L237.92401995929265, 85.51791810278525L238.2401331365045, 85.76362149622089L238.66761714839123, 85.60433047036977L239.08432031348732, 84.88880626479073L239.47229017822335, 84.55669686474175L239.7416707282585, 83.61440804241109L240.23383982089217, 83.22563194962905L240.63976213919605, 83.29310415563765L240.9702185299647, 83.40379605153201L242.57239298285018, 82.46421483336191L243.02500105508432, 82.38319695227118L243.68239834310194, 81.94040133897295L244.36852893147883, 81.86747952294363L244.72771862260834, 82.19418556163524L244.96480350551644, 81.79999279478085L245.6796673942542, 82.1644950021873L247.02319527064873, 81.95117031547498L247.24589006675433, 82.21577395046853L247.78474491331673, 81.90527384149266L249.1641776968501, 81.90260722093473L249.27554853152654, 82.08347345459568L250.39633473856975, 81.73250537869285L250.84533357079206, 82.12941997288286L251.58893075988985, 82.4561130898452L251.4703883184362, 83.1014013117043L251.7003015945655, 83.33360765760203L252.0020715582206, 83.94379629361329L251.9625574110691, 84.88614084418329L252.23915644112867, 85.09132384231887L252.36847972937454, 85.74470973069207L252.70971696693306, 86.15245390980405L252.45107039044288, 87.01106409551711L251.89070072354494, 87.2972428432706L251.65717820740218, 87.86423493904347L252.23915644112867, 88.3799477482018L251.74698734849576, 88.88214782807844L252.6414695194215, 89.36817462488158L252.8139161948307, 89.78665789456296L251.8044539492167, 90.02426478373272L251.28720766948203, 90.59128074588203L251.1686652280276, 90.98008722572213L250.8561144175827, 90.45356447678431L250.4681445528474, 90.44547246915207L250.8309903572345, 89.66244742807436L250.61542029466113, 89.67868455673545L249.9436797930875, 90.44547246915207L248.880360186766, 90.71547390002745L248.72952207818537, 91.66049522508261L248.1762771448189, 91.71451727078099L247.93562989514464, 91.99798502530982L247.2028135528367, 91.71717995745513L247.02319527064873, 92.02502033816535L246.25442714795622, 92.16270416212339L245.89523745682752, 92.82420230724398L245.35999185027697, 92.88092835375804L245.20193526167182, 93.66122097333445L244.46550967935204, 94.4307248839409L242.87055370649182, 94.72774310961768L242.5005362953252, 95.95084012661081L241.5090733765271, 95.9130231198314L241.9293857816358, 96.33155695309416L241.82518655373815, 96.74735277556954L241.41209262865547, 96.9552410067171L241.4444351690282, 97.58705269364376L242.1377373641839, 97.54653339825632L242.23110887204362, 97.88945601177764L242.6693737307218, 97.8732388225625L243.42014252659834, 98.27824754241699L243.36267592587825, 99.12067122412482L243.6284941091464, 99.43384846574396L243.1040293493881, 100.29249190621755L243.55668429486872, 100.51116275290072L243.9733405867183, 101.78827107500683L244.76006116298026, 102.10689673528572L244.9935836791238, 102.86561700943894L245.61502918675527, 103.29491223408674L245.85572330967602, 103.68911588754125L246.39814052300454, 103.74309218480619L246.71786294022905, 104.19671111162535L247.50097427647833, 104.2831380251133L247.23154685319753, 104.73942921846613L246.77532954095, 104.76641321392515L247.13095686531253, 105.09041714751962L248.1762771448189, 104.90414234112757L248.79059791891814, 104.98243460150933L249.14622524328067, 105.32533581297012L248.8049411324757, 105.6952509643679L247.9140682015618, 105.63582267069958L247.78835415333015, 106.81846478974778L248.12959139088866, 107.33685922082488L248.8480645196398, 107.50963149297544L249.43716748689783, 107.40434412635815L249.26115844472326, 107.92545202009049L249.38331012619034, 108.72468529272146L250.0227080873905, 109.34834545139422L249.81435650484244, 109.68586712252869L249.16056845683823, 110.06386732080756L249.5593191683641, 111.31670381776671L250.04426978097257, 111.73520046824336L249.71737575697006, 112.42372107037828L249.26115844472326, 112.55869088687496L249.8215749848677, 113.63871001885501L249.84669904521513, 114.24619947905623L248.98095017465025, 114.06262631530426L248.94504526751143, 113.86822830464793L248.0541723365983, 113.35252858212803L248.07573403018037, 112.45072545817614L247.15608092565995, 111.92419707161069L246.60288286554078, 112.0754109802354L245.12646933413424, 111.816170484949L244.81752776370195, 112.37241754211257L245.07978358020475, 113.36600342025397L244.7744512497843, 114.59990958879236L244.86065115086606, 114.99952728784194L244.24637725001176, 115.941860449737L245.08695518698352, 116.48726002820017L244.84987030407464, 117.06776407264567L245.4928775052882, 118.57707756323366L245.03309782627525, 118.57167135949683L244.55531882044562, 119.64089420991793L244.7995753101325, 120.30776039782238L245.41384921098603, 120.38067421849178L245.11212612057824, 121.02600405562404L244.13144404857152, 120.98276983207195L243.38062837944767, 121.39858198606375L242.96392521435078, 121.82517608953444L242.7232779646773, 123.01050702321781L242.55444052928, 123.49112579602303L241.31511188078233, 122.89980192171487L240.21944973408733, 122.90520469847304L239.56927092609575, 122.72971421019693L238.47360877940076, 122.80800503392258L237.74801091711888, 122.20861904003873L236.7349863047387, 122.3327924425643L235.36989673476134, 123.30749569609469L234.5652705781765, 123.4776200032508L234.15934825987182, 123.73142251934911L234.12339647948568, 123.39928587932373L233.65639832044667, 123.33180670851952L233.25408524215462, 123.62883239509266L233.13910516746634, 124.11751046176131L231.74171993036356, 124.12291227687795L231.3178451584879, 124.54954028609018L230.61737135655343, 124.93833362920839L229.20559603264735, 125.27044655665875L228.86074955507544, 125.5377353255742L228.61649306538857, 126.23703896727231L228.06329500526937, 126.81483025237429L227.639373360148, 127.51145691248787L227.64298260016065, 128.83177691806233L227.9339717170243, 130.2951695398704L228.5230746842823, 131.4778158391755L228.93260624260117, 132.65771416481437L229.5792226838258, 134.14812870020492L230.0857349900171, 135.0931226704836L229.90250746781723, 136.8832295131207L230.50600052187855, 137.99293627327376L231.82074822466652, 139.46989574349536L233.50912257863297, 141.53001424579685L234.91728866252637, 143.341733931881L235.25135429330768, 143.76829304463834L236.73137706472608, 147.45383555280586L237.83777318496522, 153.0834243768227L237.25945106449782, 153.7071381284326L235.56746747051872, 154.05543154848775L233.67078840725074, 154.0256973834955L233.09602865354952, 152.3733248414798L232.22671741621846, 150.7856934973636L231.23525449742033, 150.0323587761259L230.78259955193892, 149.991895007775L230.2473539453884, 149.51397834918959L229.45346176234688, 149.36546364749998L228.2931614081522, 147.3350397652391L228.1171523659784, 146.8841661963742L228.47277969034099, 146.38465139481343L227.66093505373084, 145.720455608228L226.18452152232354, 144.67823903890158L224.9164595734666, 143.6035977799731L225.0637353152811, 143.22022534953157L224.11895815041245, 142.84760611318694L223.1023711712677, 142.73691422580498L222.44858312326272, 142.4911800646039L220.24291561631517, 140.97917727659257Z"
                        style="cursor: pointer"
                      ></path>
                      <path
                        id="15059_440300"
                        fill="url(#linearGradient_15059)"
                        stroke="RGBA(130,188,255,0.5)"
                        stroke-width="0.5"
                        d="M238.35150397118096, 143.88709886998157L238.86518788414864, 142.7935817805445L240.19788804050603, 141.95388901353866L240.24462066768203, 141.23030254313562L240.71518119348636, 140.74969980410756L240.73313364705658, 140.35549326541786L241.14627444538576, 140.47700115160828L241.2575984068157, 140.07200047928615L241.79645325337808, 139.94509607896066L242.37121300708012, 139.5400643316621L242.44306969460354, 138.58967721335742L242.25267056562646, 138.4573550853952L242.57239298285018, 137.85795807633434L243.0321726618631, 137.67974620588208L243.30160008514468, 137.9281429985994L244.03085406068652, 137.6905285613598L244.2679389435946, 136.81572991713435L244.53014788685093, 137.01553087927044L244.72054701582957, 136.61053116831494L245.29169752951896, 136.76445623431263L245.02948858626263, 137.2369430745669L245.5431724992311, 137.5447618999316L245.8269431360687, 138.06047524204078L247.08783347814762, 137.92275200806668L247.87455405441034, 138.51675287913037L247.89250650797973, 138.81917535439877L248.7079603846025, 138.95149056940485L248.98816865467552, 139.2538940641534L248.83011206606957, 139.66428132935118L249.2791108982919, 139.39967559724064L249.57370925516898, 139.57514835931997L249.78206083771622, 141.0466871483404L250.14125052884495, 141.44359937713L250.84177120402592, 141.60829502168303L251.65361584063604, 141.27620563272103L251.8727482699755, 141.4003915393474L252.33613718900108, 141.05207570919458L252.31818473543245, 141.7999775321493L253.02943938415677, 141.8270190302276L253.5072183899864, 142.59110114644258L253.9562172222095, 142.0997154698552L254.61722375023896, 142.26978316786762L255.02670843530979, 143.5009979247876L255.51526828793004, 143.48753126378463L255.72001063046548, 143.84390622559977L256.7833302367878, 143.94919417811758L256.8012826903572, 144.60802072986675L257.11739586756903, 145.16964575712572L257.8502590831227, 145.6502478099766L257.6849840144923, 146.42782827914812L257.28984254297745, 146.24694074474317L258.0873439660316, 146.9867670022592L258.7914270079787, 146.37652395137619L258.7914270079787, 146.0120491656335L259.1578351791319, 145.67991602524444L259.35179667487665, 144.564832561263L259.2224733866316, 144.14899299560258L259.7541097531687, 143.92221175144914L259.7756714467508, 143.18241547029007L260.1097839507785, 142.861124836274L259.52424335028513, 141.86747942089607L259.42721572916616, 141.37609336652554L258.5363427982539, 141.35179510863833L258.81294182831346, 141.0925914525382L258.47887619753294, 140.8199062789172L258.90636020941974, 140.31227865040617L259.71459560601795, 140.104387167728L260.1097839507785, 139.19175674068646L260.74557267196604, 138.9487446221399L261.0150000952484, 139.94235052539648L261.51073155464746, 140.0611709877039L261.758597284347, 140.51746941434848L262.25793798375867, 140.51208043821484L263.26018174934745, 139.4644551058928L263.3535532572072, 139.16475581514032L263.79898972266335, 139.41315019170008L264.743766887532, 139.5778940583391L264.93416601651063, 139.25114823675096L265.49453568340857, 139.11344868171702L266.55424604971745, 139.24845325694625L266.4608745418577, 140.10977646069898L266.06573307034444, 140.41756931761282L266.6009786768942, 140.6956591443971L266.68717857797594, 141.37070505726325L267.1397866502117, 141.31128087859784L268.1312495690082, 141.64880751814619L268.5084385869528, 141.62989832713677L268.58029527447707, 140.73887137074297L268.9718743792265, 140.53638163708055L269.600491493636, 141.2842882050981L269.4100923646574, 142.01330769183596L269.4029207578786, 142.826006595027L268.91079853849294, 143.50369125385757L269.1837883285407, 143.97892045919278L269.71547156832423, 143.95732445564892L269.988461358372, 144.67280248022118L270.7212777006784, 144.79164366855235L270.875771922519, 145.22903696143686L270.68893516030647, 145.57465425189696L270.07109889268764, 145.85004860724942L270.24710793486224, 146.65462952002503L271.0805142650544, 146.43052047601728L271.2565233072289, 146.1956350852384L272.42043290143624, 146.60601899076622L272.59283270359896, 146.2874262311868L273.71361891064214, 146.27122190537136L274.11597886218067, 146.66275663917622L274.1518837693211, 146.93003199076378L275.9336296311466, 146.8922422551468L277.2016915800034, 146.6249654547102L278.2614019463139, 146.94085078865038L278.27218279310455, 147.78055162715629L277.50346154365855, 149.095488836985L276.82811180207227, 149.611158568407L276.20666629444077, 150.59669477799054L276.23896196156613, 151.10966491794832L276.63054106631483, 151.7387824865045L277.63995643868157, 152.2977025109266L278.80742839965507, 152.52989599761707L279.39296900014847, 153.81242970317703L279.4289207805339, 154.67104251921086L278.66015265784057, 155.73485212633997L277.4423857029258, 156.17222091223587L275.74683974218055, 156.5610210327832L275.27984158314155, 157.3980476358287L274.6296627751492, 157.8381529078263L273.76391390458434, 157.93805773728593L272.9628501147656, 157.68154796496174L272.51380440929603, 157.21176293338473L272.2408146192491, 156.62315604478874L271.94626313562003, 155.25961007104274L272.1222721777954, 153.69632979537278L271.9210922020253, 153.12930025948768L270.3800404631199, 151.44448392212672L270.1896413341405, 150.59126282954085L269.8088430761833, 150.1160779245839L268.67371365558483, 149.60308565496192L267.9013831661254, 149.67056273901312L266.8524067733599, 150.17547780391206L266.26686617286725, 150.32128459855363L265.25027919372167, 150.3239752942538L264.61805283929874, 150.8342749377425L264.2911588152963, 151.3364664426974L263.70922745481704, 151.62807935600063L262.1357863022916, 151.8386726669956L260.9036761338192, 152.7918213131806L260.4689736419065, 152.14650615458083L259.4452150559829, 152.0655013255606L258.5650760986148, 152.14381615963782L258.5471236450445, 151.941302252088L257.76401230879446, 151.83329243952733L257.36887083728124, 152.1870082176838L257.1964710351185, 152.8296299134112L256.334284531319, 152.85119848657627L255.49731583436142, 153.4560049964694L255.38594499968497, 153.15898747925996L254.41243453445705, 153.11849103541675L253.95265485544334, 153.55049178489813L254.2364254922817, 153.7584394257335L253.82689393396362, 154.19314069609518L253.61137074463755, 154.03650520890352L253.02943938415677, 154.28761755273086L252.56605046513198, 154.84384835137388L251.44882662485412, 154.60620118240902L251.12915108087688, 154.32541808686713L248.9019218803473, 154.28492837945385L248.0865148769718, 154.57383102634657L247.51175512326978, 155.97246109438734L246.05329404543343, 157.05783215502473L244.9288985983768, 157.7328709533275L244.45833807257324, 157.52225160149837L243.90870237921942, 156.87422662177553L243.57102750842628, 156.04804381637467L243.40936167980774, 155.0436402782115L243.6284941091464, 154.01220026358973L243.28003839156267, 152.81339000273883L243.02500105508432, 152.66220477786555L242.25267056562646, 152.8160797405443L242.42511724103488, 151.81166997422758L242.1377373641839, 151.25819333631392L240.78703788101134, 150.09450092037167L239.79557496221324, 148.44206293299118L239.3752625571045, 147.5537362159064L238.43053226548392, 144.7430201356289L238.35150397118096, 143.88709886998157ZM241.56297761048341, 160.0791794736105L240.9702185299647, 159.88206547744812L240.690057133139, 159.52026988691597L240.0039265447613, 159.20708064690194L240.57507705844986, 158.75347153901475L241.15344605216453, 158.85878893409705L241.76054834623926, 159.6741975234786L241.56297761048341, 160.0791794736105ZM242.7124971178867, 154.30380330973338L242.6226879767923, 154.02031883506754L243.29442847836592, 154.33622545045907L243.29081923835406, 154.64131096341728L242.7124971178867, 154.30380330973338ZM242.48619308176842, 154.8978302757864L242.7124971178867, 154.65211784042026L243.0645152022358, 154.99503728063306L242.841820406131, 155.23262055183102L242.48619308176842, 154.8978302757864Z"
                        style="cursor: pointer"
                      ></path>
                      <path
                        id="15059_441900"
                        fill="url(#linearGradient_15059)"
                        stroke="RGBA(130,188,255,0.5)"
                        stroke-width="0.5"
                        d="M260.74557267196604, 138.9487446221399L260.1097839507785, 139.19175674068646L259.71459560601795, 140.104387167728L258.90636020941974, 140.31227865040617L258.47887619753294, 140.8199062789172L258.81294182831346, 141.0925914525382L258.5363427982539, 141.35179510863833L259.42721572916616, 141.37609336652554L259.52424335028513, 141.86747942089607L260.1097839507785, 142.861124836274L259.7756714467508, 143.18241547029007L259.7541097531687, 143.92221175144914L259.2224733866316, 144.14899299560258L259.35179667487665, 144.564832561263L259.1578351791319, 145.67991602524444L258.7914270079787, 146.0120491656335L258.7914270079787, 146.37652395137619L258.0873439660316, 146.9867670022592L257.28984254297745, 146.24694074474317L257.6849840144923, 146.42782827914812L257.8502590831227, 145.6502478099766L257.11739586756903, 145.16964575712572L256.8012826903572, 144.60802072986675L256.7833302367878, 143.94919417811758L255.72001063046548, 143.84390622559977L255.51526828793004, 143.48753126378463L255.02670843530979, 143.5009979247876L254.61722375023896, 142.26978316786762L253.9562172222095, 142.0997154698552L253.5072183899864, 142.59110114644258L253.02943938415677, 141.8270190302276L252.31818473543245, 141.7999775321493L252.33613718900108, 141.05207570919458L251.8727482699755, 141.4003915393474L251.65361584063604, 141.27620563272103L250.84177120402592, 141.60829502168303L250.14125052884495, 141.44359937713L249.78206083771622, 141.0466871483404L249.57370925516898, 139.57514835931997L249.2791108982919, 139.39967559724064L248.83011206606957, 139.66428132935118L248.98816865467552, 139.2538940641534L248.7079603846025, 138.95149056940485L247.89250650797973, 138.81917535439877L247.87455405441034, 138.51675287913037L247.08783347814762, 137.92275200806668L245.8269431360687, 138.06047524204078L245.5431724992311, 137.5447618999316L245.02948858626263, 137.2369430745669L245.29169752951896, 136.76445623431263L244.72054701582957, 136.61053116831494L244.53014788685093, 137.01553087927044L244.2679389435946, 136.81572991713435L244.03085406068652, 137.6905285613598L243.30160008514468, 137.9281429985994L243.0321726618631, 137.67974620588208L242.57239298285018, 137.85795807633434L242.25267056562646, 138.4573550853952L242.44306969460354, 138.58967721335742L242.37121300708012, 139.5400643316621L241.79645325337808, 139.94509607896066L241.2575984068157, 140.07200047928615L241.14627444538576, 140.47700115160828L240.73313364705658, 140.35549326541786L240.71518119348636, 140.74969980410756L240.24462066768203, 141.23030254313562L240.19788804050603, 141.95388901353866L238.86518788414864, 142.7935817805445L238.35150397118096, 143.88709886998157L238.09641976145537, 143.34442732265168L237.52166000775412, 142.82061941062543L237.0618803287404, 142.72339526105864L235.83333252703417, 142.7665948681672L234.91728866252637, 143.341733931881L233.50912257863297, 141.53001424579685L231.82074822466652, 139.46989574349536L230.50600052187855, 137.99293627327376L229.90250746781723, 136.8832295131207L230.0857349900171, 135.0931226704836L229.5792226838258, 134.14812870020492L228.93260624260117, 132.65771416481437L228.5230746842823, 131.4778158391755L227.9339717170243, 130.2951695398704L227.64298260016065, 128.83177691806233L227.639373360148, 127.51145691248787L228.06329500526937, 126.81483025237429L228.61649306538857, 126.23703896727231L228.86074955507544, 125.5377353255742L229.20559603264735, 125.27044655665875L230.61737135655343, 124.93833362920839L231.3178451584879, 124.54954028609018L231.74171993036356, 124.12291227687795L233.13910516746634, 124.11751046176131L233.25408524215462, 123.62883239509266L233.65639832044667, 123.33180670851952L234.12339647948568, 123.39928587932373L234.15934825987182, 123.73142251934911L234.5652705781765, 123.4776200032508L235.36989673476134, 123.30749569609469L236.7349863047387, 122.3327924425643L237.74801091711888, 122.20861904003873L238.47360877940076, 122.80800503392258L239.56927092609575, 122.72971421019693L240.21944973408733, 122.90520469847304L241.31511188078233, 122.89980192171487L242.55444052928, 123.49112579602303L242.7232779646773, 123.01050702321781L242.8848969200478, 123.45330950906525L243.59258920200836, 123.56940790366087L244.2176439496525, 123.07798936422336L245.07261197342595, 123.73952569759274L245.57551503960303, 123.54509773566365L247.1130044117431, 123.93119619117905L248.2553523123684, 123.59101686935816L248.62176048352234, 123.63693569484153L249.36535767262012, 124.21474248157189L249.68864245660998, 124.68992627419911L250.4825346396507, 125.15972705243846L251.40214087092465, 125.11111792429627L252.33613718900108, 124.44421068734867L253.0905152248903, 124.24170017122051L254.1717872847828, 124.61160602528592L254.2938920930026, 125.47022627955896L254.79323279241433, 126.42334147544238L255.673324876536, 126.96337108927682L257.04558605329055, 128.15137831609655L257.0814909604302, 128.4375511806024L256.52829290031025, 128.2134623904415L255.83499070515455, 129.71466680930791L256.19778963629585, 129.69307712996795L256.4779979063681, 130.1007745563681L257.3006233897689, 130.26818470818324L257.4335559180266, 130.51389551428724L258.37828620964797, 129.96039739187455L258.5471236450445, 129.64175045029123L259.3949200620408, 130.08728177239595L259.96246133571674, 130.03056087399457L260.42589712798963, 130.38966653107704L260.4941445755011, 131.2563714926332L259.9086039750085, 131.75321139996697L259.98763226931226, 133.05190177194626L260.7707436055615, 133.75120485366898L261.4999507078568, 133.9023948218143L261.6831782300567, 134.23722987651135L262.1501763890949, 134.19133095622206L262.20403374980316, 134.5180106385065L261.80172067151113, 135.29561908691878L261.47843588752124, 135.49002023623035L261.10841847635544, 135.21731778476772L260.83899105307387, 135.50080600736277L261.0078284884704, 136.38375759342833L260.9288001941667, 137.43403618327457L261.0473426356211, 137.55824010458875L260.9647051013063, 138.72464114498962L260.74557267196604, 138.9487446221399Z"
                        style="cursor: pointer"
                      ></path>

                      <polygon
                        fill="none"
                        stroke="RGBA(130,188,255,0.5)"
                        stroke-width="0.5"
                        points="265.3,159.9 265,159.7 265,159.1 263.8,158.8 263.5,157.9 262.6,158.8 262.6,157.3 262.3,157.9 261.8,157.3 260,159.1 258,159.4 257.4,158.8 257.4,158.2 258,158.5 257.7,157.6 258.3,157.6 259.1,158.5 262.6,156.1 261.2,156.4 260.3,155.9 260.6,155.3 259.7,155.3 259.4,154.4 258.8,154.4 258.3,155 258,155 258,155.3 257.7,155.3 258,154.7 258.3,154.7 255.9,153.8 255.3,154.7 253,154.7 251.5,156.1 250.4,156.1 250.4,157 249.2,156.7 248.3,158.2 246.3,159.7 245.7,160.2 246.3,162 247.1,162.3 248.6,162 250.4,162.9 247.4,164.6 247.4,163.7 245.4,164.3 245.4,165.5 243.3,167.5 243.3,169.3 245.1,168.1 246.9,168.7 248.3,167.5 248.9,168.4 249.8,168.4 250.4,167.5 249.5,167.3 250.1,166.7 249.5,166.1 250.4,166.1 250.7,164.6 252.4,163.5 255,164.6 253.9,165.8 255.6,167.8 256.5,167.3 257.1,167.5 257.7,169.3 258.5,168.1 259.7,168.7 259.4,166.4 259.1,165.2 259.7,164.9 260,166.4 261.2,166.4 261.8,164.9 260,163.5 260.3,160.8 261.8,160.8 262.3,161.7 262.6,162.6 263.5,163.2 264.7,162 264.4,159.9 "
                      ></polygon>
                      <defs></defs>
                    </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="dotWrapper">
          <div>
            <div class="dot start"></div>
            <div class="startCity">广州超算</div>
          </div>
          <div>
            <div class="dot end"></div>
            <div class="endCity">鹏城云脑</div>
          </div>
          <div>
            <div class="commonPoint"></div>
            <div class="midCity">香港</div>
          </div>
          <div class="linetext">10TB全光网络互联</div>
          <div class="linetext2">6NT</div>
          <dv-flyline-chart-enhanced
            :config="config"
            style="width: 100%; height: 270px"
          />
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
export default {
  data () {
    return {
      timer: null,
      mapArray: [
        { city: false },
        { city: true },
        { city: false },
        { city: false }
      ],
      index: 1,
      config: {
        points: [
          {
            name: '广州超算',
            coordinate: [0.4, 0.465],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '鹏城云脑',
            coordinate: [0.535, 0.74],
            halo: {
              show: true,
              radius: 80
            }
          },
          {
            name: '香港',
            coordinate: [0.6, 0.9],
            halo: {
              show: true,
              radius: 80
            }
          }
        ],
        lines: [
          {
            source: '鹏城云脑',
            target: '广州超算',
            width: 8,
            color: '#FCCA00',
            duration: [10, 20],
            orbitColor: 'rgba(255, 255, 0, .4)'
          },
          {
            source: '鹏城云脑',
            target: '香港',
            width: 3,
            color: 'rgb(0,255,0)',
            duration: [10, 20],
            orbitColor: 'rgba(0,255,0, .2)'
          }
        ]
      }
    }
  },
  mounted () {}
}
</script>
<style scoped>
.img {
  width: 20px;
  height: 20px;
}

.beijing-enter-active {
  animation: beijing-in 0.2s;
}

.beijing-leave-active {
  animation: beijing-in 0.2s;
}

.guangdong-enter-active {
  animation: guangdong-in 0.2s;
}

.guangdong-leave-active {
  animation: guangdong-in 0.2s;
}

.anhui-enter-active {
  animation: anhui-in 0.2s;
}

.anhui-leave-active {
  animation: anhui-in 0.2s;
}

.hubei-enter-active {
  animation: hubei-in 0.2s;
}

.hubei-leave-active {
  animation: hubei-in 0.2s;
}

/* 小圆点 */

.dot {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 100%;
  /* background: rgb(0, 255, 31); */
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.start {
  position: absolute;
  top: 195px;
  left: 290px;
  background: #fcca00;
}

.end {
  position: absolute;
  top: 120px;
  left: 215px;
  background: #fcca00;
}

img {
  width: 18px !important;
  height: 25px !important;
}

.startCity {
  font-size: 18px;
  position: absolute;
  top: 115px;
  left: 235px;
  color: white;
  font-family: SourceHanSansSC;
  font-weight: 400;
  font-style: normal;
}

.endCity {
  font-size: 18px;
  position: absolute;
  top: 186px;
  left: 320px;
  color: #fcca00;
  font-family: SourceHanSansSC;
  font-style: normal;
}

.linetext {
  font-size: 14px;
  position: absolute;
  top: 150px;
  left: 295px;
  color: #fcca00;
  font-family: SourceHanSansSC;
  font-weight: 400;
  font-style: normal;
}
.linetext2 {
  font-size: 14px;
  position: absolute;
  top: 220px;
  left: 275px;
  color: #fcca00;
  font-family: SourceHanSansSC;
  font-weight: 400;
  font-style: normal;
}
.commonPoint {
  width: 15px;
  height: 15px;
  background-image: url("../assets/dot.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 235px;
  left: 325px;
}
.midCity {
  font-size: 18px;
  position: absolute;
  top: 230px;
  left: 350px;
  color: white;
  font-family: SourceHanSansSC;
  font-weight: 400;
  font-style: normal;
}
</style>
