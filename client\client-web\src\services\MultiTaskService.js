import { jobDetail } from '@/api/screenService.js'

/**
 * 多任务服务类 - 处理多个任务的数据获取和处理
 */
export class MultiTaskService {
  /**
   * 批量获取多个任务的详情数据
   * @param {Array} taskIds - 任务ID数组
   * @param {number} intervalHours - 时间间隔（小时）
   * @returns {Promise<Array>} 返回任务数据数组
   */
  static async getMultipleTasksData (taskIds, intervalHours) {
    if (!taskIds || taskIds.length === 0) {
      return []
    }

    const promises = taskIds.map(taskId => {
      if (taskId === 0) return Promise.resolve(null)

      const params = {
        id: taskId,
        resolution_n_hours: intervalHours
      }

      return jobDetail(params).catch(error => {
        console.error(`获取任务${taskId}详情失败:`, error)
        return null
      })
    })

    try {
      const results = await Promise.all(promises)
      return results.filter(result => result !== null)
    } catch (error) {
      console.error('批量获取任务数据失败:', error)
      throw error
    }
  }

  /**
   * 处理多个任务的数据，提取统计信息
   * @param {Array} tasksData - 多个任务的数据数组
   * @returns {Object} 处理后的统计数据
   */
  static processMultipleTasksData (tasksData) {
    if (!tasksData || tasksData.length === 0) {
      return {
        combinedStats: null,
        individualStats: [],
        taskCount: 0
      }
    }

    const individualStats = []
    let combinedSubmitJob = []
    let combinedCompleteJob = []
    let combinedTime = []
    let maxFrames = 0

    // 处理每个任务的数据
    tasksData.forEach((taskData, index) => {
      if (!taskData || !taskData.CenterInfoToWebList) {
        individualStats.push(null)
        return
      }

      const centerInfoList = taskData.CenterInfoToWebList
      const frames = centerInfoList[0]?.SnapshotInfoToWebList?.length || 0
      maxFrames = Math.max(maxFrames, frames)

      // 提取单个任务的统计数据
      const taskStats = this.extractSingleTaskStats(centerInfoList, frames)
      individualStats.push({
        taskId: taskData.ID,
        strategy: taskData.Strategy,
        stats: taskStats,
        frames: frames
      })

      // 累加到组合统计中
      if (index === 0) {
        combinedSubmitJob = [...taskStats.submitJob]
        combinedCompleteJob = [...taskStats.completeJob]
        combinedTime = [...taskStats.time]
      } else {
        // 将其他任务的数据累加
        for (let i = 0; i < Math.min(combinedSubmitJob.length, taskStats.submitJob.length); i++) {
          combinedSubmitJob[i] += taskStats.submitJob[i]
          combinedCompleteJob[i] += taskStats.completeJob[i]
        }
      }
    })

    return {
      combinedStats: {
        submitJob: combinedSubmitJob,
        completeJob: combinedCompleteJob,
        time: combinedTime,
        totalTasks: tasksData.length
      },
      individualStats: individualStats,
      taskCount: tasksData.length,
      maxFrames: maxFrames
    }
  }

  /**
   * 提取单个任务的统计数据
   * @param {Array} centerInfoList - 中心信息列表
   * @param {number} frames - 帧数
   * @returns {Object} 单个任务的统计数据
   */
  static extractSingleTaskStats (centerInfoList, frames) {
    const submitJob = []
    const completeJob = []
    const time = []

    for (let i = 0; i < frames; i++) {
      let frameSubmitJob = 0
      let frameCompleteJob = 0

      centerInfoList.forEach(center => {
        if (center.SnapshotInfoToWebList && center.SnapshotInfoToWebList[i]) {
          frameSubmitJob += center.SnapshotInfoToWebList[i].HistorySubmitJob || 0
          frameCompleteJob += center.SnapshotInfoToWebList[i].HistoryCompleteJob || 0
        }
      })

      submitJob.push(frameSubmitJob)
      completeJob.push(frameCompleteJob)

      // 提取时间信息
      if (centerInfoList[0]?.SnapshotInfoToWebList?.[i]?.Time) {
        const timeValue = Math.floor(centerInfoList[0].SnapshotInfoToWebList[i].Time * 1000 / (1000 * 60 * 60))
        time.push(timeValue)
      }
    }

    return {
      submitJob,
      completeJob,
      time
    }
  }

  /**
   * 获取多个任务的对比数据（用于图表显示）
   * @param {Array} tasksData - 多个任务的数据数组
   * @param {number} frameIndex - 当前帧索引
   * @returns {Object} 对比数据
   */
  static getMultipleTasksCompareData (tasksData, frameIndex = 0) {
    if (!tasksData || tasksData.length === 0) {
      return {
        xData: [],
        series: []
      }
    }

    const cities = []
    const series = []

    // 从第一个任务获取城市列表
    if (tasksData[0] && tasksData[0].CenterInfoToWebList) {
      tasksData[0].CenterInfoToWebList.forEach(center => {
        cities.push(center.InfoName)
      })
    }

    // 为每个任务创建数据系列
    tasksData.forEach((taskData, taskIndex) => {
      if (!taskData || !taskData.CenterInfoToWebList) return

      const taskSubmitData = []
      const taskPendingData = []
      const taskResourceData = []

      taskData.CenterInfoToWebList.forEach((center, centerIndex) => {
        let submitValue = 0
        let pendingValue = 0
        let resourceValue = 0

        if (center.SnapshotInfoToWebList && center.SnapshotInfoToWebList[frameIndex]) {
          // 当前帧有数据，直接使用
          const snapshot = center.SnapshotInfoToWebList[frameIndex]
          submitValue = snapshot.HistorySubmitJob || 0
          pendingValue = snapshot.AveragePendingTime || 0
          resourceValue = snapshot.AverageMachineUse || 0
        } else if (center.SnapshotInfoToWebList && center.SnapshotInfoToWebList.length > 0) {
          // 当前帧没有数据，使用最后一个有效数据
          const lastValidIndex = center.SnapshotInfoToWebList.length - 1
          const lastSnapshot = center.SnapshotInfoToWebList[lastValidIndex]
          if (lastSnapshot) {
            submitValue = lastSnapshot.HistorySubmitJob || 0
            pendingValue = lastSnapshot.AveragePendingTime || 0
            resourceValue = lastSnapshot.AverageMachineUse || 0
          }
        }

        taskSubmitData.push(submitValue)
        taskPendingData.push(pendingValue)
        taskResourceData.push(resourceValue)
      })

      series.push({
        taskId: taskData.ID,
        strategy: taskData.Strategy,
        submitData: taskSubmitData,
        pendingData: taskPendingData,
        resourceData: taskResourceData
      })
    })

    return {
      xData: cities,
      series: series
    }
  }
}

export default MultiTaskService
