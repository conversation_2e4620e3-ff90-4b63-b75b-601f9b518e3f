<template>
  <div>
    <div class="background">
      <el-container>
        <!-- 页面标题 -->
        <el-header>
          <el-row justify="center" type="flex">
            <el-col :span="12">
              <div class="title">中国算力网实验场（仿真）</div>
            </el-col>
          </el-row>
        </el-header>

        <el-main>
          <el-row justify="space-around" type="flex">
            <!-- 左侧栏 - 任务总览和列表 -->
            <el-col :span="7">
              <!-- 任务总览面板 -->
              <div class="showMt">
                <el-row class="linear-gradient" style="margin-bottom: 3px">
                  <el-col :span="14">
                    <el-row>
                      <el-col :span="5">
                        <div class="arrow"></div>
                      </el-col>
                      <el-col :span="19">
                        <div class="title1">仿真任务总览</div>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                <el-row justify="space-between" type="flex">
                  <el-col :span="24">
                    <div>
                      <CumulativeTask :Data="Data" :msg="msg2"></CumulativeTask>
                    </div>
                    <div>
                      <div
                        key="demo1"
                        ref="echart"
                        style="width: 100%; height: 250px"
                      ></div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 任务列表面板 -->
              <div class="showMt">
                <el-row class="linear-gradient">
                  <el-col :span="19">
                    <el-row align="middle" type="flex">
                      <el-col :span="3">
                        <div class="arrow"></div>
                      </el-col>
                      <el-col :span="16">
                        <div class="title1">仿真任务列表</div>
                      </el-col>
                    </el-row>
                  </el-col>
                  <el-col :span="5" style="text-align: right; padding-right: 15px;">
                    <el-button
                      size="small"
                      type="primary"
                      @click="openAddTaskDialog"
                      :disabled="disable"
                    >新增任务
                    </el-button>
                  </el-col>
                </el-row>
                <Table
                  ref="table"
                  :Count="count"
                  :Stop="Stop"
                  :Total="total"
                  class="index"
                  @row-click="openTaskDetailDialog"
                ></Table>
              </div>
            </el-col>

            <!-- 中间栏 - 地图和控制 -->
            <el-col :span="12">
              <div>
                <el-row justify="center" type="flex">
                  <el-col :span="18">
                    <Total :msg="msg" :taskDetail="taskDetail"></Total>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <div style="max-height: 800px; overflow: hidden">
                      <chinaMap ref="chinaMap"
                                :Count="count"
                                :Stop="Stop"
                                :intervalSetting="interval"
                                :taskResponse="taskIdRes"
                                @request-job-detail="handleRequestJobDetail"
                      ></chinaMap>
                      <div class="progress">
                        <div style="width: 600px; margin: 0 auto">
                          <div
                            style="
                              display: inline-block;
                              min-width: 150px;
                              margin-right: 10px;
                            "
                          >
                            <el-slider
                              v-if="showPer && intervalChange"
                              v-model="percentage"
                              :disabled="disable"
                              :format-tooltip="formatTooltip"
                              class="progressStyle"
                              @change="changeProgress"
                            ></el-slider>
                          </div>
                          <div
                            v-if="intervalChange"
                            style="width: 80px; display: inline-block"
                          >
                            <el-button
                              :disabled="disable"
                              size="small"
                              @click="start"
                            >开始
                            </el-button
                            >
                          </div>
                          <div
                            v-show="buttonShow && showButtom"
                            style="width: 80px; display: inline-block"
                          >
                            <el-button
                              :disabled="disable"
                              size="small"
                              @click="stop"
                            >暂停
                            </el-button
                            >
                          </div>
                          <div
                            v-show="!buttonShow && showButtom"
                            style="width: 80px; display: inline-block"
                          >
                            <el-button
                              :disabled="disable"
                              size="small"
                              @click="goOn"
                            >继续
                            </el-button
                            >
                          </div>
                          <div style="display: inline-block">
                            <el-select
                              v-if="intervalChange"
                              v-model="value"
                              :disabled="disable"
                              class="interval"
                              placeholder="请选择仿真时间间隔"
                              style="
                                width: 150px;
                                display: inline-block;
                                margin-left: 10px;
                              "
                              @change="changeInterval"
                            >
                              <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              >
                              </el-option>
                            </el-select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>

            <!-- 右侧栏 - 统计面板 -->
            <el-col :span="5">
              <div class="taskDetailWrapper">
                <div>
                  <div class="mt">
                    <el-row justify="center" type="flex">
                      <el-col>
                        <div>
                          <el-row
                            class="row-bg"
                            justify="space-around"
                            type="flex"
                            :gutter="16"
                          >
                            <el-col :span="8" class="bg2" :class="{'active-tab': check1}">
                              <el-link
                                :class="{ button2: true, check3: check1 }"
                                :disabled="disable"
                                :underline="false"
                                @click="change(1)"
                              >提交任务量
                              </el-link>
                            </el-col>
                            <el-col :span="8" class="bg2" :class="{'active-tab': check2}">
                              <el-link
                                :class="{ button2: true, check3: check2 }"
                                :disabled="disable"
                                :underline="false"
                                @click="change(2)"
                              >
                                任务平均等待时长
                              </el-link>
                            </el-col>
                            <el-col :span="8" class="bg2" :class="{'active-tab': check3}">
                              <el-link
                                :class="{ button2: true, check3: check3 }"
                                :disabled="disable"
                                :underline="false"
                                @click="change(3)"
                              >资源利用率对比
                              </el-link>
                            </el-col>
                          </el-row>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div
                    key="demo2"
                    ref="echart2"
                    style="width: 100%; min-height: 700px"
                  ></div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-main>

        <div class="footer"></div>
      </el-container>
    </div>

    <!-- 任务弹窗 -->
    <TaskDialog
      :visible.sync="addTaskDialogVisible"
      @close="handleCloseTaskDialog"
      @submit="submitTaskForm"
    />

    <!-- 任务详情弹窗 -->
    <TaskDetailDialog
      :taskId="currentTaskId"
      :visible.sync="taskDetailDialogVisible"
      @close="handleTaskDetailClose"
      @submit-success="handleTaskSubmitSuccess"
    />
  </div>
</template>

<script>
// 组件导入
import Total from '@/components/Total'
import chinaMap from '@/components/chinaMapDemo'
import Table from '@/components/Table'
import circle from '@/components/circle'
import TaskDialog from '@/components/TaskDialog'
import TaskDetailDialog from '@/components/TaskDetailDialog'

// 服务导入
import { jobDetail } from '@/api/screenService.js'

/**
 * 中国算力网实验场（仿真）主页面
 * 使用Vue 2选项式API，与c2net页面保持一致
 */
export default {
  name: 'HomePage',

  components: {
    Total,
    chinaMap,
    CumulativeTask: circle,
    Table,
    TaskDialog,
    TaskDetailDialog
  },

  data () {
    return {
      // 对话框相关状态
      addTaskDialogVisible: false,
      taskDetailDialogVisible: false,
      currentTaskId: '',

      // 图表数据
      taskData: {
        xData: [],
        used: [],
        used2: []
      },
      Data: {
        xAxis: [],
        yAxis: []
      },

      // 任务状态信息
      msg: {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      },
      msg2: {
        totalNum: 0,
        execNum: 0
      },
      taskDetail: {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      },

      // 任务对比数据
      submitJobCompare0: null,
      pendingJobCompare0: null,
      resouceCompare0: null,
      submitJobCompare: null,
      pendingJobCompare: null,
      resouceCompare: null,

      // 计时和控制相关
      timer: null,
      count: 0,
      nowCount: 0,
      total: 0,
      total2: 0,
      runningTime: 0,

      // 图表和任务类型
      check1: true,
      check2: false,
      check3: false,
      taskType: 1,
      name: '提交任务量',
      myChart: null,
      myChart2: null,

      // UI控制状态
      buttonShow: true,
      disable: false,
      Stop: true,
      showButtom: false, // 初始状态不显示暂停/继续按钮
      showPer: false, // 初始状态不显示进度条
      intervalChange: true,
      showStart: false,

      // 时间配置
      options: [
        { value: 24, label: '24h' },
        { value: 12, label: '12h' },
        { value: 6, label: '6h' }
      ],
      value: 24,
      percentage: 0,

      // 数据缓存
      temp: null,
      taskIdRes: null,
      compareIdRes: null,
      CenterInfoToWebList: null,
      dataAlreadyLoaded: false,

      // 地图相关
      show2: false,
      show3: false,
      tipData: {}
    }
  },

  computed: {
    taskId () {
      return this.$store.state.id
    },
    compareId () {
      return this.$store.state.compareId
    },
    Strategy1 () {
      return this.$store.state.strategy1
    },
    Strategy2 () {
      return this.$store.state.strategy2
    },
    lastTime () {
      return this.$store.state.lastTime
    },
    interval () {
      return this.$store.state.interval
    }
  },

  watch: {
    taskId (newValue) {
      // 保持地图状态
      if (this.$refs.chinaMap) {
        this.$refs.chinaMap.stopped = false

        // 如果地图上有数据，保留它们
        if (this.$refs.chinaMap.nowConfig &&
          this.$refs.chinaMap.nowConfig.points &&
          this.$refs.chinaMap.nowConfig.points.length > 0) {
          // 地图数据已存在，不清空
        } else {
          // 如果没有数据，初始化演示数据
          this.$refs.chinaMap.initDemoData()
          this.$refs.chinaMap.showDot()
        }
      }

      // 只在任务ID变化时重置状态，不自动加载数据
      if (newValue && newValue !== 0) {
        // 重置状态但不自动加载数据
        this.resetChartData()
        this.resetTaskInfo()
        // 设置任务详情的基本信息
        this.taskDetail.ID = newValue
        // 任务ID已更新为: newValue，等待用户点击开始按钮
      } else {
        this.resetChartData()
        this.resetTaskInfo()
      }
    },
    compareId (newValue) {
      // 只有在有主任务且用户已经开始仿真时才加载对比数据
      if (newValue && this.taskId && this.taskId !== 0 && this.showButtom) {
        this.getCompare(newValue)
      }
    },
    interval (newValue) {
      // 只有在用户已经开始仿真时才重新加载数据
      if (this.showButtom) {
        if (this.taskId && this.taskId !== 0) {
          this.getJobDetail(this.taskId)
        }
        if (this.compareId && this.compareId !== 0) {
          this.getCompare(this.compareId)
        }
      }
    }
  },

  created () {
    this.drawLine()
    this.drawLine2()
  },

  methods: {
    // 新增任务相关方法
    openAddTaskDialog () {
      // openAddTaskDialog called
      this.addTaskDialogVisible = true
      // addTaskDialogVisible: this.addTaskDialogVisible
    },

    handleCloseTaskDialog () {
      this.addTaskDialogVisible = false
    },

    submitTaskForm (responseOrFormData) {
      // submitTaskForm被调用，参数: responseOrFormData

      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自TaskDialog的generateYaml响应
        this.$message.success('任务创建成功')
        this.addTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          // 新增任务成功后刷新任务列表
          if (this.$refs.table) {
            // 调用table.init()刷新任务列表
            this.$refs.table.init()
          } else {
            // table引用不存在，无法刷新任务列表
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/tasks', responseOrFormData)
          .then(response => {
            this.$message.success('任务创建成功')
            this.addTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              // 新增任务成功后刷新任务列表
              if (this.$refs.table) {
                // 调用table.init()刷新任务列表
                this.$refs.table.init()
              } else {
                // table引用不存在，无法刷新任务列表
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建任务失败: ' + error.message)
          })
      }
    },

    // 打开任务详情弹窗
    openTaskDetailDialog (row) {
      // 打开任务详情: row
      this.currentTaskId = row.ID
      this.taskDetailDialogVisible = true
      // 当前任务ID: this.currentTaskId, 对话框可见性: this.taskDetailDialogVisible
    },

    // 处理任务详情弹窗关闭
    handleTaskDetailClose () {
      this.taskDetailDialogVisible = false
    },

    // 处理任务提交成功
    handleTaskSubmitSuccess () {
      // 任务提交/重新提交成功事件被触发
      // 刷新任务列表
      if (this.$refs.table) {
        // 调用table.init()刷新任务列表
        this.$refs.table.init()
      } else {
        // table引用不存在，无法刷新任务列表
      }
    },

    // 基础数据操作方法
    resetChartData () {
      // 初始化为空数据，仅保持图表框架
      this.Data = {
        xAxis: [],
        yAxis: []
      }

      this.taskData = {
        xData: [],
        used: [],
        used2: []
      }

      this.drawLine()
      this.drawLine2()
      this.count = 0
      this.total = 0
      this.buttonShow = true
      this.showButtom = false // 重置时隐藏暂停/继续按钮
      this.showPer = false // 重置时隐藏进度条

      // 清除定时器
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    resetTaskInfo () {
      this.msg = {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      }
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
      this.taskDetail = {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      }
    },

    // 获取任务详情
    getJobDetail (id) {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      const data = {
        id: id,
        resolution_n_hours: this.interval
      }
      this.disable = false // 修改为false，允许操作
      this.showButtom = true
      this.showPer = true
      jobDetail(data).then((res) => {
        this.processMainJobData(res)
      }).catch((error) => {
        // 获取任务详情失败: error
        this.disable = false // 出错时也要解除禁用状态
      })
    },

    getCompare (id) {
      if (!id == 0) {
        const data = {
          id: id,
          resolution_n_hours: this.interval
        }
        this.disable = false // 修改为false，允许操作
        this.showButtom = true
        this.showPer = true
        jobDetail(data).then((res) => {
          this.processCompareJobData(res)
        }).catch((error) => {
          // 获取对比任务详情失败: error
          this.disable = false // 出错时也要解除禁用状态
        })
      } else {
        this.taskData.used2 = []
        this.CenterInfoToWebList = null
      }
    },

    // 处理主任务数据
    processMainJobData (res) {
      try {
        // 处理主任务数据: res

        this.prepareForProcessing()

        if (!res) return

        // 处理主任务数据
        this.initBaseTaskInfo(res)

        if (
          res.CenterInfoToWebList != null &&
          res.CenterInfoToWebList[0] &&
          res.CenterInfoToWebList[0].SnapshotInfoToWebList.length
        ) {
          this.total = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length
        }

        const {
          historySubmitJob,
          historyCompleteJob,
          submitJob,
          completeJob,
          Time,
          city,
          submitJobCompare,
          pendingJobCompare,
          resouceCompare
        } = this.extractJobData(res.CenterInfoToWebList)

        this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

        const xAxis = JSON.parse(JSON.stringify(Time))
        const yAxis = JSON.parse(JSON.stringify(submitJob))
        this.msg.SnapshotTime = Time[this.count] + 'h'
        this.Data.xAxis = xAxis
        this.temp = JSON.parse(JSON.stringify(yAxis))
        this.Data.yAxis = [] // 重置yAxis数组
        this.Data.yAxis[this.count] = yAxis[this.count]

        this.runningTime = Time[this.count] + 'h'
        this.updateTaskDataByType(city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used)

        // 初始化msg2数据
        this.msg2.totalNum = historySubmitJob[this.count] || 0
        this.msg2.execNum = historyCompleteJob[this.count] || 0

        this.finalizeProcessing()
        this.startAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis)
      } catch (error) {
        // 处理主任务数据失败: error
        this.disable = false
      }
    },

    /**
     * 准备处理数据
     */
    prepareForProcessing () {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      this.disable = true
      this.showButtom = true
      this.showPer = true
      this.count = 0 // 重置计数器
    },

    /**
     * 完成数据处理
     */
    finalizeProcessing () {
      this.drawLine()
      this.drawLine2()
      this.disable = false
    },

    /**
     * 初始化基础任务信息
     */
    initBaseTaskInfo (res) {
      this.taskDetail = {
        ID: res.ID || '',
        NJobs: res.NJobs || 0,
        SnapshotTime: res.SnapshotTime || '',
        CompletedFlag: res.CompletedFlag || false,
        strategy: res.strategy || ''
      }

      this.msg.SnapshotTime = res.SnapshotTime || 0
      this.msg.NCenters = res.NCenters || 0
      this.msg.NPops = res.NPops || 0
    },

    /**
     * 初始化任务类型数据
     */
    initTaskTypeData () {
      return {
        xData: [],
        used: [],
        used2: []
      }
    },

    /**
     * 初始化主要对比数据
     */
    initMainCompareData (city, submitJobCompare, pendingJobCompare, resouceCompare) {
      this.submitJobCompare = submitJobCompare
      this.pendingJobCompare = pendingJobCompare
      this.resouceCompare = resouceCompare
      this.taskData.xData = city
      this.updateTaskDataByType(city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used)
    },

    /**
     * 根据任务类型更新数据
     */
    updateTaskDataByType (city, submitJobUsed, pendingJobUsed, resouceCompareUsed, index) {
      const idx = index !== undefined ? index : this.count

      if (this.taskType == 1) {
        this.updateTaskDataForType(city, submitJobUsed[idx], this.submitJobCompare?.used2?.[idx])
      }
      if (this.taskType == 2) {
        this.updateTaskDataForType(city, pendingJobUsed[idx], this.pendingJobCompare?.used2?.[idx])
      }
      if (this.taskType == 3) {
        this.updateTaskDataForType(city, resouceCompareUsed[idx], this.resouceCompare?.used2?.[idx])
      }
    },

    /**
     * 更新指定类型的任务数据
     */
    updateTaskDataForType (city, usedData, usedData2) {
      this.taskData.xData = city
      this.taskData.used = usedData
      if (usedData2) {
        this.taskData.used2 = usedData2
      }
    },

    /**
     * 开始动画计时器
     */
    startAnimationTimer (Time, historySubmitJob, historyCompleteJob, city, submitJobUsed, pendingJobUsed, resouceCompareUsed, xAxis) {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count >= that.total) {
          clearInterval(that.timer)
          that.timer = null
          that.buttonShow = true
          that.Stop = true
          return
        }

        that.updateAnimationTimerData(Time, historySubmitJob, historyCompleteJob, xAxis)
        that.updateTaskDataByType(city, submitJobUsed, pendingJobUsed, resouceCompareUsed)

        that.drawLine2()
        that.drawLine()
        that.showStart = true

        // 更新进度条
        that.percentage = Math.round((that.count / that.total) * 100)
      }, 2000)
    },

    /**
     * 更新动画计时器数据
     */
    updateAnimationTimerData (Time, historySubmitJob, historyCompleteJob, xAxis) {
      // 注意：这里传入的Time已经是小时值了，因此不需要再处理
      this.msg.SnapshotTime = Time[this.count] + 'h'
      this.msg2.totalNum = historySubmitJob[this.count]
      this.msg2.execNum = historyCompleteJob[this.count]
      this.Data.xAxis = xAxis
      this.runningTime = Time[this.count] + 'h'

      // 更新折线图数据 - 逐步显示数据
      if (this.temp && this.temp.length > this.count) {
        // 只显示到当前帧的数据
        this.Data.yAxis = this.temp.slice(0, this.count + 1)
      }
    },

    // 处理对比任务数据
    processCompareJobData (res) {
      this.compareIdRes = res
      // 处理对比数据逻辑
    },

    // 提取任务数据
    extractJobData (centerInfoList) {
      const historySubmitJob = []
      const historyCompleteJob = []
      const submitJob = []
      const completeJob = []
      const Time = []
      this.Data.xAxis = []
      this.Data.yAxis = []

      const submitJobCompare = this.initTaskTypeData()
      const pendingJobCompare = this.initTaskTypeData()
      const resouceCompare = this.initTaskTypeData()

      const city = []
      const data1 = []
      const data2 = []
      const data3 = []

      centerInfoList.forEach((item) => {
        city.push(item.InfoName)
      })

      for (let i = 0; i < this.total; i++) {
        let HistorySubmitJob = 0
        let HistoryCompleteJob = 0
        let SubmitJob = 0
        let CompleteJob = 0
        data1[i] = []
        data2[i] = []
        data3[i] = []

        centerInfoList.forEach((item, key) => {
          if (item.SnapshotInfoToWebList[i]) {
            HistorySubmitJob += item.SnapshotInfoToWebList[i].HistorySubmitJob
            HistoryCompleteJob += item.SnapshotInfoToWebList[i].HistoryCompleteJob
            SubmitJob += item.SnapshotInfoToWebList[i].SubmitJob
            CompleteJob += item.SnapshotInfoToWebList[i].CompleteJob
            data1[i][key] = item.SnapshotInfoToWebList[i].HistorySubmitJob
            data2[i][key] = item.SnapshotInfoToWebList[i].AveragePendingTime
            data3[i][key] = item.SnapshotInfoToWebList[i].AverageMachineUse
          }
        })

        historySubmitJob[i] = HistorySubmitJob
        historyCompleteJob[i] = HistoryCompleteJob
        submitJob[i] = SubmitJob
        completeJob[i] = CompleteJob
      }

      submitJobCompare.xData = JSON.parse(JSON.stringify(city))
      pendingJobCompare.xData = JSON.parse(JSON.stringify(city))
      resouceCompare.xData = JSON.parse(JSON.stringify(city))
      submitJobCompare.used = data1
      pendingJobCompare.used = data2
      resouceCompare.used = data3

      centerInfoList[0].SnapshotInfoToWebList.forEach((item) => {
        // 使用修改后的formatDuring确保返回纯小时数
        const timeValue = this.formatDuring(item.Time)
        Time.push(timeValue)
      })

      return {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      }
    },

    // 图表相关方法
    change (type) {
      this.check1 = type === 1
      this.check2 = type === 2
      this.check3 = type === 3
      this.taskType = type

      if (type === 1) {
        this.name = '提交任务量'
      } else if (type === 2) {
        this.name = '任务平均等待时长'
      } else if (type === 3) {
        this.name = '资源利用率对比'
      }

      // 重置任务数据
      this.taskData = {
        xData: this.taskData.xData || [],
        used: [],
        used2: []
      }

      // 根据类型更新数据
      if (this.submitJobCompare && this.pendingJobCompare && this.resouceCompare) {
        this.updateTaskDataByType(
          this.taskData.xData,
          this.submitJobCompare.used,
          this.pendingJobCompare.used,
          this.resouceCompare.used,
          this.count // 传入当前帧索引
        )
      }

      this.drawLine2()
    },

    // 仿真控制方法
    start () {
      // 用户点击开始按钮，任务ID: this.taskId

      // 如果没有数据，先加载数据
      if (!this.temp || this.temp.length === 0) {
        if (this.taskId && this.taskId !== 0) {
          // 开始加载任务数据...
          this.getJobDetail(this.taskId)

          // 如果有对比任务，也加载对比数据
          if (this.compareId && this.compareId !== 0) {
            this.getCompare(this.compareId)
          }
        }
        return
      }

      // 如果已有数据，开始播放动画
      this.buttonShow = false
      this.Stop = false

      // 如果动画已经在运行，先停止
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }

      // 开始播放动画，总帧数: this.total

      // 开始动画定时器（这里不需要重新调用startAnimationTimer，因为数据加载时已经启动了）
      // 但如果动画已经结束，需要重新开始
      if (this.count >= this.total - 1) {
        this.count = 0
        this.percentage = 0
      }
    },

    stop () {
      this.buttonShow = true
      this.Stop = true
      clearInterval(this.timer)
      this.timer = null
    },

    goOn () {
      this.buttonShow = false
      this.Stop = false

      // 重新启动动画定时器
      if (this.temp && this.temp.length > 0 && this.count < this.total - 1) {
        const that = this
        this.timer = setInterval(() => {
          that.count++
          if (that.count >= that.total) {
            clearInterval(that.timer)
            that.timer = null
            that.buttonShow = true
            that.Stop = true
            return
          }

          // 更新数据显示
          that.msg.SnapshotTime = that.Data.xAxis[that.count] + 'h'
          that.msg2.totalNum = that.temp[that.count] || 0
          that.msg2.execNum = that.temp[that.count] || 0

          // 更新折线图数据
          that.Data.yAxis = that.temp.slice(0, that.count + 1)

          // 更新右侧图表数据
          if (that.submitJobCompare && that.pendingJobCompare && that.resouceCompare) {
            that.updateTaskDataByType(
              that.taskData.xData,
              that.submitJobCompare.used,
              that.pendingJobCompare.used,
              that.resouceCompare.used
            )
          }

          that.drawLine2()
          that.drawLine()

          // 更新进度条
          that.percentage = Math.round((that.count / that.total) * 100)
        }, 2000)
      }
    },

    changeProgress (val) {
      this.count = Math.round((val / 100) * this.total)
      this.percentage = val
      this.msg.SnapshotTime = this.Data.xAxis[this.count]
      this.Data.yAxis[this.count] = this.temp[this.count]
      this.drawLine()
    },

    changeInterval (val) {
      this.value = val
      this.$store.commit('setInterval', val)
    },

    formatTooltip (val) {
      return `${val}%`
    },

    // 处理地图组件请求任务详情
    handleRequestJobDetail (taskId) {
      // 地图组件请求任务详情: taskId
      if (taskId && taskId !== 0) {
        this.getJobDetail(taskId)
      }
    },

    // 图表绘制方法
    drawLine () {
      const option = this.createLineChartOption()
      this.myChart && this.myChart.setOption(option, true)
    },

    drawLine2 () {
      const option = this.createBarChartOption()
      this.myChart2 && this.myChart2.setOption(option, true)
    },

    /**
     * 创建折线图配置
     */
    createLineChartOption () {
      // 确保即使没有数据也显示图表框架
      const hasData = this.Data.xAxis && this.Data.xAxis.length > 0

      return {
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%',
          padding: [10, 0, 0, 10]
        },
        color: '#32c5ff ',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid',
              color: 'rgba(0, 0, 0, 0)'
            }
          },
          formatter: function (params) {
            params = [params[0]]
            let htmlStr = ''
            htmlStr += '<div>'
            htmlStr += '<div>'
            htmlStr += params[0].axisValue + 'h'
            htmlStr += '</div>'
            htmlStr +=
              '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
              '#1890ff' +
              ';"></span>'
            htmlStr += params[0].seriesName + params[0].value
            htmlStr += '</div>'
            return htmlStr
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        grid: {
          right: '5%',
          bottom: '25%',
          top: '12%',
          left: '10%'
        },
        xAxis: this.createXAxisConfig(),
        yAxis: this.createYAxisConfig(),
        series: [
          {
            name: '提交任务量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: false
            },
            zlevel: 1,
            z: 1,
            data: this.Data.yAxis || [],
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(50,197,255,0)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(22,93,255)'
                  }
                ],
                global: false
              }
            }
          }
        ],
        legend: {
          data: ['提交任务量'],
          left: 255,
          top: 225,
          itemHeight: 12,
          textStyle: {
            color: '#a1a1a1',
            fontSize: 12
          }
        },
        animation: true,
        animationDuration: function (idx) {
          return idx * 500
        },
        animationEasing: 'backln'
      }
    },

    /**
     * 创建X轴配置
     */
    createXAxisConfig () {
      return {
        type: 'category',
        boundaryGap: false,
        data: this.Data.xAxis || [],
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(1, 145, 255, 0.3)'
          }
        },
        axisLabel: {
          show: true,
          showMaxLabel: true,
          interval: 'auto',
          fontSize: '12',
          lineHeight: 40,
          color: 'rgba(255, 255, 255,1)',
          fontFamily: 'Microsoft YaHei',
          fontWeight: 'normal',
          formatter: function (value) {
            // 确保值显示为数字+h格式，无论输入是什么格式
            return value + 'h'
          }
        },
        axisTick: {
          show: false
        }
      }
    },

    /**
     * 创建Y轴配置
     */
    createYAxisConfig () {
      return {
        type: 'value',
        name: '',
        show: true,
        nameTextStyle: {
          padding: [0, 0, 0, 0]
        },
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            type: 'dotted',
            color: 'rgba(1, 145, 255, 0.3)'
          }
        },
        nameGap: 10,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255,0.9)'
          }
        },
        axisLabel: {
          show: true,
          interval: 'auto',
          align: 'right',
          fontSize: '10',
          fontWeight: 'bold',
          color: 'rgba(255, 255, 255,0.9)'
        },
        scale: true,
        min: 0,
        splitNumber: 4
      }
    },

    /**
     * 创建柱状图配置
     */
    createBarChartOption () {
      // 确保即使没有数据也显示图表框架
      const hasData = this.taskData.xData && this.taskData.xData.length > 0

      const option = {
        darkMode: true, // 启用暗黑模式
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.6)', // 调整颜色
            fontSize: 14
          },
          left: 'center', // 居中
          top: 'center' // 居中
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow', // 更适合柱状图
            shadowStyle: {
              color: 'rgba(0, 190, 255, 0.05)'
            }
          },
          backgroundColor: 'rgba(0, 25, 50, 0.9)', // 深蓝背景
          borderColor: 'rgba(0, 190, 255, 0.7)', // 科技蓝边框
          borderWidth: 1,
          padding: [10, 15], // 调整内边距
          textStyle: {
            color: 'rgba(255, 255, 255, 0.95)',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 3px 10px rgba(0, 150, 255, 0.35); border-radius: 4px;'
        },
        legend: {
          show: hasData, // MODIFIED: Dynamically show/hide legend
          top: '4%',
          right: '5%',
          orient: 'horizontal',
          itemWidth: 16,
          itemHeight: 10,
          icon: 'rect',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.9)',
            fontSize: 12
          },
          inactiveColor: 'rgba(255, 255, 255, 0.4)'
        },
        grid: {
          left: '3%',
          right: '5%',
          top: '5%', // MODIFIED: Further reduced top margin for legend area
          bottom: '5%', // MODIFIED: Reduced bottom margin (from previous step)
          containLabel: true
        },
        yAxis: {
          type: 'category',
          data: this.taskData.xData || [],
          show: hasData, // MODIFIED: Dynamically show/hide yAxis
          axisLine: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLine
            lineStyle: {
              color: 'rgba(0, 190, 255, 0.4)',
              width: 1
            }
          },
          axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide yAxis.axisTick
          axisLabel: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLabel
            interval: 0,
            textStyle: {
              color: 'rgba(220, 220, 220, 0.95)',
              fontSize: 12,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
            },
            margin: 12
          },
          splitLine: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.splitLine
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          }
        },
        xAxis: {
          type: 'value',
          show: hasData, // MODIFIED: Dynamically show/hide xAxis
          axisLine: {
            show: hasData, // MODIFIED: Dynamically show/hide xAxis.axisLine
            lineStyle: {
              color: 'rgba(0, 190, 255, 0.4)',
              width: 1
            }
          },
          axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide xAxis.axisTick
          axisLabel: { // <-- Target this object
            show: hasData,
            rotate: 30, // <--- ADD THIS LINE
            textStyle: {
              color: 'rgba(220, 220, 220, 0.85)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
            },
            margin: 10
          },
          splitLine: {
            show: hasData, // MODIFIED: Dynamically show/hide xAxis.splitLine
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed',
              width: 1
            }
          },
          scale: true,
          min: 0,
          splitNumber: 5
        },
        series: hasData ? this.createBarSeriesConfig() : [], // MODIFIED: Provide empty series array if no data
        animation: true,
        animationDurationUpdate: 300,
        animationEasingUpdate: 'cubicInOut'
      }

      // This logic was slightly adjusted to ensure it only applies when hasData is true.
      if (this.compareId == 0 && hasData) {
        option.series.length = 1
      }
      // No need for an explicit else if (!hasData) for series, as the ternary operator handles it.

      return option
    },

    /**
     * 创建柱状图系列配置
     */
    createBarSeriesConfig () {
      const formatter = (param) => {
        if (this.taskType == 1) {
          return param.data || 0
        } else if (this.taskType == 2) {
          // 确保formatDuring2返回的是纯数字或带单位的字符串，以便后续可能的样式处理
          const value = this.formatDuring2(param.data || 0)
          return typeof value === 'number' ? value : value + 'h' // 假设formatDuring2可能返回数字或已格式化的字符串
        } else {
          return (param.data || 0).toFixed(2) + '%'
        }
      }

      const series = [
        {
          name: this.taskId == 0 ? '' : this.taskId + ' ' + this.Strategy1,
          type: 'bar',
          barWidth: 10, // Changed from 12
          z: 1,
          label: {
            show: true,
            position: 'right',
            distance: 5,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.95)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              // fontWeight: 'bold' // 可以考虑加粗
            },
            formatter
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderColor: 'rgba(0, 230, 255, 1)',
              shadowColor: 'rgba(0, 230, 255, 0.8)',
              shadowBlur: 10
            }
          },
          data: this.taskData.used || [],
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(0, 130, 220, 0.9)' }, // Changed color and alpha
              { offset: 1, color: 'rgba(30, 230, 255, 1)' } // Changed color
            ]),
            borderRadius: [0, 5, 5, 0], // 右侧圆角
            borderColor: 'rgba(50, 240, 255, 0.9)', // Changed color and alpha
            borderWidth: 0.5
            // shadowColor: 'rgba(0, 200, 255, 0.5)',
            // shadowBlur: 3
          }
        }
      ]

      if (this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0) {
        series.push({
          name: this.compareId == 0 ? '' : this.compareId + ' ' + this.Strategy2,
          type: 'bar',
          barWidth: 10, // Changed from 12
          z: 1,
          label: {
            show: true,
            position: 'right',
            distance: 5,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.95)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              // fontWeight: 'bold' // 可以考虑加粗
            },
            formatter
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderColor: 'rgba(255, 140, 0, 1)',
              shadowColor: 'rgba(255, 140, 0, 0.8)',
              shadowBlur: 10
            }
          },
          data: this.taskData.used2 || [],
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(255, 100, 0, 0.9)' }, // Changed color and alpha
              { offset: 1, color: 'rgba(255, 200, 0, 1)' } // Changed color
            ]),
            borderRadius: [0, 5, 5, 0], // 右侧圆角
            borderColor: 'rgba(255, 180, 0, 0.9)', // Changed color and alpha
            borderWidth: 0.5
            // shadowColor: 'rgba(255, 140, 0, 0.5)',
            // shadowBlur: 3
          }
        })
      }

      return series
    },

    formatDuring (val) {
      // 修改为只返回小时数，不使用天(d)为单位
      const totalHours = Math.floor(val * 1000 / (1000 * 60 * 60))
      return totalHours
    },

    formatDuring2 (val) {
      // 确保只返回小时数
      var hours = Math.floor(val * 3600 / 3600)
      return hours
    },

    formatDuring3 (val) {
      // 修改为只返回小时为单位的时间
      if (val < (1 / 3600)) {
        return (val * 3600).toFixed(2) + 's'
      }

      // 转换为小时，包括小数部分
      const totalHours = val
      return totalHours.toFixed(2) + 'h'
    },

    /**
     * 创建柱状图配置
     */
    createBarChartOption () {
      // 确保即使没有数据也显示图表框架
      const hasData = this.taskData.xData && this.taskData.xData.length > 0

      const option = {
        darkMode: true, // 启用暗黑模式
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.6)', // 调整颜色
            fontSize: 14
          },
          left: 'center', // 居中
          top: 'center' // 居中
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow', // 更适合柱状图
            shadowStyle: {
              color: 'rgba(0, 190, 255, 0.05)'
            }
          },
          backgroundColor: 'rgba(0, 25, 50, 0.9)', // 深蓝背景
          borderColor: 'rgba(0, 190, 255, 0.7)', // 科技蓝边框
          borderWidth: 1,
          padding: [10, 15], // 调整内边距
          textStyle: {
            color: 'rgba(255, 255, 255, 0.95)',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 3px 10px rgba(0, 150, 255, 0.35); border-radius: 4px;'
        },
        legend: {
          show: hasData, // MODIFIED: Dynamically show/hide legend
          top: '4%',
          right: '5%',
          orient: 'horizontal',
          itemWidth: 16,
          itemHeight: 10,
          icon: 'rect',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.9)',
            fontSize: 12
          },
          inactiveColor: 'rgba(255, 255, 255, 0.4)'
        },
        grid: {
          left: '3%',
          right: '5%',
          top: '5%', // MODIFIED: Further reduced top margin for legend area
          bottom: '5%', // MODIFIED: Reduced bottom margin (from previous step)
          containLabel: true
        },
        yAxis: {
          type: 'category',
          data: this.taskData.xData || [],
          show: hasData, // MODIFIED: Dynamically show/hide yAxis
          axisLine: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLine
            lineStyle: {
              color: 'rgba(0, 190, 255, 0.4)',
              width: 1
            }
          },
          axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide yAxis.axisTick
          axisLabel: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLabel
            interval: 0,
            textStyle: {
              color: 'rgba(220, 220, 220, 0.95)',
              fontSize: 12,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
            },
            margin: 12
          },
          splitLine: {
            show: hasData, // MODIFIED: Dynamically show/hide yAxis.splitLine
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          }
        },
        xAxis: {
          type: 'value',
          show: hasData, // MODIFIED: Dynamically show/hide xAxis
          axisLine: {
            show: hasData, // MODIFIED: Dynamically show/hide xAxis.axisLine
            lineStyle: {
              color: 'rgba(0, 190, 255, 0.4)',
              width: 1
            }
          },
          axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide xAxis.axisTick
          axisLabel: { // <-- Target this object
            show: hasData,
            rotate: 30, // <--- ADD THIS LINE
            textStyle: {
              color: 'rgba(220, 220, 220, 0.85)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
            },
            margin: 10
          },
          splitLine: {
            show: hasData, // MODIFIED: Dynamically show/hide xAxis.splitLine
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed',
              width: 1
            }
          },
          scale: true,
          min: 0,
          splitNumber: 5
        },
        series: hasData ? this.createBarSeriesConfig() : [], // MODIFIED: Provide empty series array if no data
        animation: true,
        animationDurationUpdate: 300,
        animationEasingUpdate: 'cubicInOut'
      }

      // This logic was slightly adjusted to ensure it only applies when hasData is true.
      if (this.compareId == 0 && hasData) {
        option.series.length = 1
      }
      // No need for an explicit else if (!hasData) for series, as the ternary operator handles it.

      return option
    },

    /**
     * 创建柱状图系列配置
     */
    createBarSeriesConfig () {
      const formatter = (param) => {
        if (this.taskType == 1) {
          return param.data || 0
        } else if (this.taskType == 2) {
          // 确保formatDuring2返回的是纯数字或带单位的字符串，以便后续可能的样式处理
          const value = this.formatDuring2(param.data || 0)
          return typeof value === 'number' ? value : value + 'h' // 假设formatDuring2可能返回数字或已格式化的字符串
        } else {
          return (param.data || 0).toFixed(2) + '%'
        }
      }

      const series = [
        {
          name: this.taskId == 0 ? '' : this.taskId + ' ' + this.Strategy1,
          type: 'bar',
          barWidth: 10, // Changed from 12
          z: 1,
          label: {
            show: true,
            position: 'right',
            distance: 5,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.95)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              // fontWeight: 'bold' // 可以考虑加粗
            },
            formatter
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderColor: 'rgba(0, 230, 255, 1)',
              shadowColor: 'rgba(0, 230, 255, 0.8)',
              shadowBlur: 10
            }
          },
          data: this.taskData.used || [],
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(0, 130, 220, 0.9)' }, // Changed color and alpha
              { offset: 1, color: 'rgba(30, 230, 255, 1)' } // Changed color
            ]),
            borderRadius: [0, 5, 5, 0], // 右侧圆角
            borderColor: 'rgba(50, 240, 255, 0.9)', // Changed color and alpha
            borderWidth: 0.5
            // shadowColor: 'rgba(0, 200, 255, 0.5)',
            // shadowBlur: 3
          }
        }
      ]

      if (this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0) {
        series.push({
          name: this.compareId == 0 ? '' : this.compareId + ' ' + this.Strategy2,
          type: 'bar',
          barWidth: 10, // Changed from 12
          z: 1,
          label: {
            show: true,
            position: 'right',
            distance: 5,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.95)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              // fontWeight: 'bold' // 可以考虑加粗
            },
            formatter
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderColor: 'rgba(255, 140, 0, 1)',
              shadowColor: 'rgba(255, 140, 0, 0.8)',
              shadowBlur: 10
            }
          },
          data: this.taskData.used2 || [],
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(255, 100, 0, 0.9)' }, // Changed color and alpha
              { offset: 1, color: 'rgba(255, 200, 0, 1)' } // Changed color
            ]),
            borderRadius: [0, 5, 5, 0], // 右侧圆角
            borderColor: 'rgba(255, 180, 0, 0.9)', // Changed color and alpha
            borderWidth: 0.5
            // shadowColor: 'rgba(255, 140, 0, 0.5)',
            // shadowBlur: 3
          }
        })
      }

      return series
    },

    formatDuring (val) {
      // 修改为只返回小时数，不使用天(d)为单位
      const totalHours = Math.floor(val * 1000 / (1000 * 60 * 60))
      return totalHours
    },

    formatDuring2 (val) {
      // 确保只返回小时数
      var hours = Math.floor(val * 3600 / 3600)
      return hours
    },

    formatDuring3 (val) {
      // 修改为只返回小时为单位的时间
      if (val < (1 / 3600)) {
        return (val * 3600).toFixed(2) + 's'
      }

      // 转换为小时，包括小数部分
      const totalHours = val
      return totalHours.toFixed(2) + 'h'
    }
  },

  mounted () {
    // 初始化图表
    this.$nextTick(() => {
      if (this.$refs.echart) {
        this.myChart = this.$echarts.init(this.$refs.echart)
        this.drawLine() // 初始化空图表
      }
      if (this.$refs.echart2) {
        this.myChart2 = this.$echarts.init(this.$refs.echart2)
        this.drawLine2() // 初始化空图表
      }
    })
  }
}
</script>

<style scoped>
.background {
  background-image: url("../static/screen1/background.png");
  background-size: 100% 100%;
  min-width: 2020px;
  min-height: 100vh;
  width: 100%;
  position: relative;
}

:deep(.el-container) {
  background: transparent;
  position: relative;
  z-index: 1;
}

:deep(.el-header) {
  height: 125px !important;
  background-image: url("../static/screen1/header.png");
  background-size: 100%;
  padding: 20px 0;
}

:deep(.el-main) {
  padding-bottom: 0px;
  z-index: 20;
  overflow: hidden;
  padding: 0px 20px 20px 20px !important;
}

.title {
  margin-top: 20px;
  font-size: 3rem;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  text-align: center;
  margin-left: 20px;
}

.footer {
  height: 20px;
  background: transparent;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .title {
    font-size: 28px;
  }

  :deep(.el-main) {
    padding: 0 15px 15px;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 24px;
  }

  :deep(.el-main) {
    padding: 0 10px 10px;
  }
}

/* c2net页面样式 - 确保与原页面完全一致 */
.showMt {
  margin-top: 0px !important;
  width: 570px;
}

.linear-gradient {
  background: linear-gradient(
    to right,
    rgba(255, 0, 0, 0),
    rgba(255, 255, 255, 0.2)
  );
  margin: 6px 0 6px 0;
  box-sizing: content-box;
  background-clip: content-box;
}

.arrow {
  background-size: 100% 100%;
  background-image: url("../static/screen1/arrow2.png");
  width: 50px;
  height: 35px;
  position: relative;
  top: -5px;
  background-repeat: no-repeat;
}

.title1 {
  color: rgb(255, 255, 255);
  font-size: 20px;
  text-align: left;
  font-family: SourceHanSansSC-medium;
}

/* 任务详情包装器样式 */
.taskDetailWrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.mt {
  margin-top: 20px;
  margin-bottom: 15px;
}

/* 按钮组样式 */
.row-bg {
  background: transparent;
  border-radius: 8px;
  padding: 5px;
  margin-bottom: 20px;
}

.bg2 {
  background: linear-gradient(to bottom, #2A3A4A, #1F2A35);
  border: 1px solid rgba(0, 140, 190, 0.3);
  border-radius: 6px;
  padding: 8px 4px;
  margin: 0 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.bg2:hover {
  background: linear-gradient(to bottom, #2F4050, #243540);
  border-color: rgba(0, 174, 239, 0.5);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3), 0 0 8px rgba(0, 174, 239, 0.2);
}

.bg2:active {
  background: linear-gradient(to bottom, #25303D, #1E2730);
  box-shadow: inset 0 2px 3px rgba(0, 0, 0, 0.35);
  border-color: rgba(0, 140, 190, 0.2);
}

.bg2:hover .button2 {
  color: #FFFFFF;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
}

/* 选中标签样式 */
.bg2.active-tab {
  background: linear-gradient(to bottom, #1A4E6E, #10354F);
  border-color: #00AEEF;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 174, 239, 0.4);
}

.bg2.active-tab:hover {
  background: linear-gradient(to bottom, #205A7C, #16405A);
  border-color: #00C0FF;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.15), 0 0 12px rgba(0,191,255,0.5);
}

.bg2.active-tab .button2.check3 {
  color: #FFFFFF !important;
  text-shadow: 0 0 7px rgba(0, 180, 255, 0.7);
  font-weight: 700;
  font-size: 15px;
}

.button2 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: block;
  text-align: center;
  padding: 4px 8px;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.button2.check3 {
  color: #FFFFFF !important;
  text-shadow: 0 0 5px rgba(0, 174, 239, 0.6);
  font-weight: 600;
}

/* 进度条样式 */
.progress {
  width: 100%;
  z-index: 100;
  height: 120px;
  padding: 20px 5px 20px 5px;
  border-radius: 5%;
  background: transparent;
  position: absolute;
  top: 770px;
}

.el-button--small {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

.progressStyle {
  width: 220px;
  display: inline-block;
  height: 20px;
  margin-right: 10px;
  margin-left: 20px;
}

.interval {
  margin-top: 0px;
  max-height: 30px;
  width: 100px;
}

.el-select {
  width: 80px;
}

.el-select > .el-input {
  background: transparent !important;
}

:deep(.el-input__inner) {
  background: transparent !important;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
  height: 30px;
}

:deep(.el-progress__text) {
  color: #fff !important;
}

.index {
  z-index: 120;
}

.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
}

:deep(.el-select .el-input .el-select__caret) {
  position: absolute;
  top: 6px;
  right: 10px;
}

:deep(.el-select .el-input .el-select__caret.is-reverse) {
  position: absolute;
  top: -5px !important;
  right: 10px;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.3);
}

/* echart2 容器的样式调整 */
div[ref="echart2"] {
  margin-top: 25px;
  background-color: rgba(10, 30, 55, 0.25);
  border-radius: 6px;
  padding: 15px 10px;
  border: 1px solid rgba(0, 190, 255, 0.15);
  box-shadow: inset 0 0 15px rgba(0, 25, 50, 0.25);
}

/* 全局样式优化 */
:deep(.el-row) {
  margin: 0;
}

:deep(.el-col) {
  padding: 0 8px;
}

/* 滚动条样式 */
:deep(::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(::-webkit-scrollbar-track) {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: rgba(38, 173, 255, 0.6);
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb:hover) {
  background: rgba(38, 173, 255, 0.8);
}
</style>
