---
description: 
globs: 
alwaysApply: true
---
## 核心原则

- 对话语言:
  - 必须使用中文进行回复。
  - Always response in Chinese
- 代码修改原则:
  - 原子性：同一个文件尽量一次性完成修改,减少工具调用。
  - 上下文感知: 修改前需理解现有代码结构和逻辑，学习并保持代码原有的编码风格。
  - 禁止硬编码: 避免在代码中使用硬编码的字符串或数字。
  - 最佳实践：遵循各平台的最佳实践
  - 极简设计：无冗余,拒绝重复内容
  - 易维护：逻辑集中，结构清晰

- 服务管理:
  - 禁止主动启动/停止服务 (如yarn dev, uvicorn，docker等)。
  - 如需调试请调用MCP mcp-feedback-enhanced工具请求用户手动操作。

- 沟通反馈规则:
  - 在每个任务步骤中都必须调用mcp-feedback-enhanced工具以获取用户反馈,并根据反馈调整
  - 什么时候结束任务对话? 当且仅当用户明确指示“结束”时才停止。
