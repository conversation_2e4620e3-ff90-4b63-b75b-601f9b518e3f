<template>
  <div>
    <el-row type="flex" justify="space-around">
      <el-col :span="8"
        ><div><Pie1 :msg="tableData[0]" id="msg1"></Pie1></div
      ></el-col>
      <el-col :span="8"
        ><div><Pie2 :msg="tableData[1]" id="msg2"></Pie2></div
      ></el-col>
      <el-col :span="8"
        ><div><Pie3 :msg="tableData[2]" id="msg3"></Pie3></div
      ></el-col>
    </el-row>
    <el-row type="flex" class="row-bg" justify="space-around">
      <el-col :span="8"
        ><div><Pie4 :msg="tableData[3]" id="msg4"></Pie4></div
      ></el-col>
      <el-col :span="8"
        ><div><Pie5 :msg="tableData[4]" id="msg5"></Pie5></div
      ></el-col>
    </el-row>
  </div>
</template>
<script>
import Pie1 from '@/barComponents/Pie2'
import Pie2 from '@/barComponents/Pie2'
import Pie3 from '@/barComponents/Pie2'
import Pie4 from '@/barComponents/Pie2'
import Pie5 from '@/barComponents/Pie2'
import { storageMessage } from '@/barApi/screenService.js'
export default {
  name: 'storage',
  props: {},
  watch: {},
  components: {
    Pie1: Pie1,
    Pie2: Pie2,
    Pie3: Pie3,
    Pie4: Pie4,
    Pie5: Pie5
  },
  created () {
    storageMessage().then((res) => {
      if (res) {
        delete res.data
        res.infojson = JSON.parse(res.infojson)
        res.infojson.forEach((item) => {
          this.tableData.push(item)
        })
      }
    })
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {}
}
</script>
<style scoped>
.row-bg {
  padding-top: 10px;
}
</style>
