<template>
  <div :id="id" style="width: 100%; height: 235px" ref="echart"></div>
</template>
<script>
export default {
  name: 'histogram',
  data () {
    return {
      timer: null,
      Data: {
        unused: [],
        used: [],
        xData: []
      },
      BarWidth: 25,
      Max: 0
    }
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    data: {
      handler (newValue, oldValue) {
        const Data = { unused: [], used: [], xData: [] }
        clearInterval(this.timer)
        const TempData = JSON.parse(JSON.stringify(newValue))
        this.barWidth = 10
        Data.xData = TempData.xData
        Data.used = TempData.used ? TempData.used : []
        Data.unused = TempData.unused ? TempData.unused : []
        if (TempData.xData.length <= 5) {
          this.Data = Data
          this.drawLine()
        } else {
          this.Data.unused = Data.unused.slice(0, 5)
          this.Data.used = Data.used.slice(0, 5)
          this.Data.xData = Data.xData.slice(0, 5)
          this.drawLine()
          this.timer = setInterval(() => {
            const unused = Data.unused.shift()
            const used = Data.used.shift()
            const xData = Data.xData.shift()
            Data.unused.push(unused)
            Data.used.push(used)
            Data.xData.push(xData)
            this.Data.unused = Data.unused.slice(0, 5)
            this.Data.used = Data.used.slice(0, 5)
            this.Data.xData = Data.xData.slice(0, 5)
            this.drawLine()
          }, 3000)
        }
      },
      deep: true
    }
  },
  mounted () {
    if (this.myChart) {
      this.myChart && this.myChart.dispose()
    }
    this.$nextTick(() => {
      this.drawLine()
    })
  },
  destroyed () {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    drawLine () {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      var option
      option = {
        title: {
          text: '',
          color: '#008B45',
          padding: [10, 0, 0, 10] // 位置
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            show: false
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        legend: {
          right: '0',
          itemWidth: 15,
          color: '#fff'
        },
        grid: {
          right: '5%',
          bottom: '18%',
          top: '12%'
        },
        xAxis: {
          type: 'category',
          data: this.Data.xData,
          axisLine: {
            show: true
          },
          axisLabel: {
            show: true,
            interval: 0,
            fontSize: '10',
            lineHeight: 40,
            color: '#fff',
            fontFamily: 'Microsoft YaHei',
            margin: this.config.unit.length > 10 ? 35 : 8,
            formatter: function (value, index) {
              if (value.length > 4) {
                return value.substr(0, 4) + '...'
              } else {
                return value
              }
            }
          },
          axisTick: {
            // x轴刻度相关设置
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          offset: this.config.unit.length > 10 ? -40 : 0,
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              type: 'solid',
              color: 'rgba(255,255,255,0.2)'
            }
          },
          name: this.config.unit,
          data: [],
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: true,
            fontSize: '8',
            fontWeight: 'bolder',
            color: '#fff'
          },
          scale: true,
          min: 0,
          splitNumber: 3
        },
        series: [
          {
            name: this.config.status[0] ? this.config.status[0] : '',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.used ? this.Data.used : [],
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1,
                    color: 'rgba(50, 197, 255,0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 0,
                    color: 'rgba(50, 197, 255,1)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          {
            name: this.config.status[1] ? this.config.status[1] : '',
            type: 'bar',
            stack: 'total',
            barWidth: this.barWidth,
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.Data.unused !== [] ? this.Data.unused : [],
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1,
                    color: 'rgba(24, 144, 255,0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(24, 144, 255,1)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ],
        animation: true,
        animationDuration: function (idx) {
          // 越往后的数据时长越大
          return idx * 100
        },
        animationEasing: 'backln'
      }
      if (this.Data.xData.length > 8) {
        option.xAxis.axisLabel.formatter = function (params) {
          var newParamsName = ''
          var paramsNameNumber = params.length
          var provideNumber = 4 // 一行显示几个字
          var rowNumber = Math.ceil(paramsNameNumber / provideNumber)
          if (paramsNameNumber > provideNumber) {
            for (var p = 0; p < rowNumber; p++) {
              var tempStr = ''
              var start = p * provideNumber
              var end = start + provideNumber
              if (p == rowNumber - 1) {
                tempStr = params.substring(start, paramsNameNumber)
              } else {
                tempStr = params.substring(start, end) + '\n'
              }
              newParamsName += tempStr
            }
          } else {
            newParamsName = params
          }
          return newParamsName
        }
      }
      option && this.myChart.setOption(option)
    },
    digit (val) {
      let num = Math.trunc(val)
      const number = num.toString() // 把数字转换成字符串
      var temp = num
      var count = 0
      if (num == 0) {
        return 0
      } else {
        while (num != 0) {
          count++ // 统计位数；
          num = parseInt(num / 10) // 赋值给 num 以备下次循环使用；
        }
        return count
      }
    }
  }
}
</script>
