<template>
  <div class="task-overview-panel">
    <div class="panel-header">
      <el-row class="linear-gradient" style="margin-bottom: 3px">
        <el-col :span="14">
          <el-row>
            <el-col :span="5">
              <div class="arrow"></div>
            </el-col>
            <el-col :span="19">
              <div class="title1">仿真任务总览</div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>

    <div class="panel-content">
      <!-- 环形图组件 -->
      <div class="circular-chart-container">
        <CumulativeTask :Data="chartData" :msg="taskSummary"></CumulativeTask>
      </div>

      <!-- 折线图容器 -->
      <div class="line-chart-container">
        <div
          ref="lineChart"
          class="line-chart"
          style="width: 100%; height: 250px"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import CumulativeTask from '@/components/circle'

export default {
  name: 'TaskOverviewPanel',

  components: {
    CumulativeTask
  },

  props: {
    // 任务数据
    taskData: {
      type: Object,
      default: () => ({
        xAxis: [],
        yAxis: []
      })
    },

    // 任务摘要信息
    taskSummary: {
      type: Object,
      default: () => ({
        totalNum: 0,
        execNum: 0
      })
    },

    // 当前帧数
    currentFrame: {
      type: Number,
      default: 0
    },

    // 总帧数
    totalFrames: {
      type: Number,
      default: 0
    }
  },

  data () {
    return {
      lineChart: null
    }
  },

  computed: {
    // 图表数据
    chartData () {
      return {
        xAxis: this.taskData.xAxis || [],
        yAxis: this.taskData.yAxis || []
      }
    }
  },

  watch: {
    // 监听任务数据变化
    taskData: {
      handler (newData) {
        this.updateLineChart(newData)
      },
      deep: true,
      immediate: true
    },

    // 监听当前帧变化
    currentFrame (newFrame) {
      this.updateChartFrame(newFrame)
    }
  },

  mounted () {
    this.initLineChart()
  },

  beforeDestroy () {
    if (this.lineChart) {
      this.lineChart.dispose()
      this.lineChart = null
    }
  },

  methods: {
    /**
     * 初始化折线图
     */
    initLineChart () {
      if (!this.$refs.lineChart) return

      this.lineChart = echarts.init(this.$refs.lineChart)
      this.updateLineChart(this.taskData)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    /**
     * 更新折线图
     */
    updateLineChart (data) {
      if (!this.lineChart || !data) return

      const option = this.createLineChartOption(data)
      this.lineChart.setOption(option, true)
    },

    /**
     * 创建折线图配置
     */
    createLineChartOption (data) {
      const hasData = data.xAxis && data.xAxis.length > 0

      return {
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '30%'
        },
        color: '#32c5ff',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid',
              color: 'rgba(0, 0, 0, 0)'
            }
          },
          formatter: (params) => {
            if (!params || params.length === 0) return ''

            const param = params[0]
            return `
              <div>
                <div>${param.axisValue}h</div>
                <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:#1890ff;"></span>
                ${param.seriesName}: ${param.value}
              </div>
            `
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        grid: {
          right: '5%',
          bottom: '25%',
          top: '12%',
          left: '10%'
        },
        xAxis: {
          type: 'category',
          data: data.xAxis || [],
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            formatter: (value) => value + 'h'
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            name: '提交任务量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            data: data.yAxis || [],
            lineStyle: {
              width: 2,
              color: '#32c5ff'
            },
            itemStyle: {
              color: '#32c5ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(50, 197, 255, 0.3)' },
                  { offset: 1, color: 'rgba(50, 197, 255, 0.1)' }
                ]
              }
            }
          }
        ]
      }
    },

    /**
     * 更新图表当前帧
     */
    updateChartFrame (frame) {
      if (!this.lineChart || !this.taskData.yAxis) return

      // 只显示到当前帧的数据
      const currentData = this.taskData.yAxis.slice(0, frame + 1)
      const currentXData = this.taskData.xAxis.slice(0, frame + 1)

      const option = this.createLineChartOption({
        xAxis: currentXData,
        yAxis: currentData
      })

      this.lineChart.setOption(option, true)
    },

    /**
     * 处理窗口大小变化
     */
    handleResize () {
      if (this.lineChart) {
        this.lineChart.resize()
      }
    }
  }
}
</script>

<style scoped>
.task-overview-panel {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.panel-header {
  margin-bottom: 15px;
}

.linear-gradient {
  background: linear-gradient(90deg, rgba(50, 197, 255, 0.3) 0%, rgba(50, 197, 255, 0.1) 100%);
  padding: 10px;
  border-radius: 4px;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 8px solid #32c5ff;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  margin-top: 2px;
}

.title1 {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  line-height: 20px;
}

.panel-content {
  color: #fff;
}

.circular-chart-container {
  margin-bottom: 20px;
}

.line-chart-container {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 10px;
}

.line-chart {
  background: transparent;
}
</style>
