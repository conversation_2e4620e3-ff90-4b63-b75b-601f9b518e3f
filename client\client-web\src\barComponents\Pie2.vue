<template>
  <div style="position: relative">
    <div id="id" style="width: 100%; height: 112px" ref="echart"></div>
  </div>
</template>
<script>
export default {
  name: 'pie2',
  props: {
    msg: {
      type: Object,
      default: () => {}
    },
    id: {
      type: String,
      default: ''
    }
  },
  watch: {
    msg: {
      handler (newValue, oldValue) {
        this.storageData.name = newValue.name
        this.storageData.usedStorage = newValue.usedStorage
        this.storageData.totalStorage = newValue.totalStorage
        this.used = newValue.usedStorage
        this.unused = newValue.totalStorage - newValue.usedStorage
        this.total = this.storageData.totalStorage
        this.percentage =
          (
            (this.storageData.usedStorage / this.storageData.totalStorage) *
            100
          ).toFixed(0) + '%'
        this.drawLine()
      },
      deep: true
    }
  },
  mounted () {
    window.addEventListener('resize', this.resize)
    window.addEventListener('visibilitychange', this.Refresh)
    this.drawLine()
    this.timer = setInterval(() => {
      this.resize()
    }, 1800000)
  },
  beforeDestroy () {
    this.myChart && this.myChart.dispose()
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('visibilitychange', this.Refresh)
    clearInterval(this.timer)
    this.timer = null
  },
  data () {
    return {
      storageData: {
        usedStorage: 0,
        totalStorage: 0,
        name: '',
        unUsedStorage: 0
      },
      percentage: 0,
      percentage: '50%',
      total: 0,
      used: 0,
      unused: 0,
      show: false,
      option: {
        title: {
          text: '算力中心节点',
          top: '80%',
          left: 'center',
          textStyle: {
            color: '#fff',
            fontSize: '14px'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            return `<div style="width:120px;min-height:55px">
            <span style="width:10px;height:10px;background-color:rgb(50, 197, 255,1);display:inline-block"></span>
             <span style="width:10px;height:10px;display:inline-block">已使用:</span> <span style="width:10px;height:10px;display:inline-block">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp${this.used}</span>
            <div>
            <div style="width:80px;height:50px">
            <span style="width:10px;height:10px;background-color:rgb(22, 93, 255,0.4);display:inline-block"></span>
             <span style="width:10px;height:10px;display:inline-block">未使用:</span> <span style="width:10px;height:10px;display:inline-block">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp${this.unused}</span>
            <div>
            <div style="width:80px;height:50px">
            <span style="width:10px;height:10px;background-color:rgb(255, 255, 255,0);display:inline-block"></span>
             <span style="width:10px;height:10px;display:inline-block">总存储:</span> <span style="width:10px;height:10px;display:inline-block">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp${this.total}</span>
            <div>`
          }
        },
        legend: {
          show: false,
          orient: 'vertical',
          top: '30%',
          left: '65%',
          itemWidth: 3,
          itemHeight: 18,
          textStyle: {
            fontSize: 12,
            color: '#ffd408'
          }
        },
        color: ['rgb(255,255,255,0.2)'],
        series: [
          {
            type: 'pie',
            radius: ['60%', '80%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'center',
              formatter: (param) => {
                return `{a|${this.percentage}}`
              },
              rich: {
                a: {
                  color: '#fff',
                  fontSize: 12,
                  fontWeight: 'bold'
                }
              }
            },
            emphasis: {
              label: {
                show: true
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {
                value: 0,
                name: '已使用',
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(24, 144, 255,0.5)' // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: 'rgb(24, 144, 255)' // 100% 处的颜色
                      }
                    ],
                    global: false // 缺省为 false
                  }
                }
              },
              {
                value: 0,
                name: '未使用'
              }
            ]
          }
        ]
      },
      myChart: null,
      timer: null
    }
  },
  methods: {
    drawLine () {
      if (this.myChart) {
        this.myChart && this.myChart.dispose()
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.echart)
      const option = this.option
      option.title.text = this.storageData.name
      option.series[0].data[0].value = this.storageData.usedStorage
      option.series[0].data[1].value =
        this.storageData.totalStorage - this.storageData.usedStorage
      option && this.myChart.setOption(option)
    },
    resize () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    Refresh () {
      if (document.visibilityState == 'visible') {
        this.resize()
      }
    }
  }
}
</script>
<style scoped></style>
